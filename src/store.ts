// src/store.ts
import { configureStore } from '@reduxjs/toolkit';
import userReducer from  './components/Users/<USER>';
// 如果有其他 slice，请在此导入
// import dashboardReducer from './Dashboard/dashboardSlice';

const store = configureStore({
    reducer: {
        user: userReducer,
        // dashboard: dashboardReducer, // 添加其他 slice
    },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
