/**
 * 工作流代码块处理器
 * 用于处理WorkflowGraph中的code_blocks数据并转换为HtmlSvgViewer需要的格式
 * 
 * 支持两种code_blocks数据格式：
 * 1. 对象格式（新）: { 'node-id': [WorkflowCodeBlock[], ...] }
 * 2. 数组格式（旧）: WorkflowCodeBlock[]
 */

import { fetchBulk } from '../components/api/api';
import { WORKFLOW_GRAPH_ENDPOINT } from '../Constant/RouterConstant';

// WorkflowGraph中的code_blocks数据结构
export interface WorkflowCodeBlock {
  node_id: string;
  node_name: string;
  content_id: string;
  language: string;
  code: string;
  has_code: boolean;
}

// HtmlSvgViewer需要的CodeBlock格式
export interface ViewerCodeBlock {
  id: string;
  type: 'html' | 'svg';
  content: string;
}

// WorkflowGraph数据结构
export interface WorkflowGraphData {
  id: number;
  name: string;
  workflow_id: string;
  session_id: string;
  query: string;
  graph: any;
  product_urls: any[];
  transcriptions: any[];
  code_blocks: WorkflowCodeBlock[]; // 支持对象和数组两种格式
  video_status: string;
  created_at: string;
  updated_at: string;
  username: string;
}

export class WorkflowCodeBlockProcessor {
  /**
   * 获取session_id对应的多条WorkflowGraph记录的原始数据
   * @param sessionId 会话ID
   * @returns API的原始响应数据
   */
  static async getWorkflowGraphsRawData(sessionId: string): Promise<any> {
    try {
      console.log('获取session_id对应的多条WorkflowGraph原始数据:', sessionId);
      
      // 构建查询URL，按updated_at倒序排列
      const endpoint = `${WORKFLOW_GRAPH_ENDPOINT}/bulk?session_id=${sessionId}&sort_by=updated_at&sort_order=desc`;
      
      // 获取数据
      const response = await fetchBulk(endpoint);
      console.log('获取到的原始响应:', response);
      
      return response.data;

    } catch (error) {
      console.error('获取WorkflowGraph原始数据失败:', error);
      return null;
    }
  }

  /**
   * 根据session_id获取WorkflowGraph记录数组，保持完整数据结构
   * @param sessionId 会话ID
   * @returns WorkflowGraph记录数组
   */
  static async getWorkflowGraphsBySessionId(sessionId: string): Promise<WorkflowGraphData[]> {
    try {
      console.log('获取session_id对应的所有WorkflowGraph记录:', sessionId);
      
      // 构建查询URL，按updated_at倒序排列
      const endpoint = `${WORKFLOW_GRAPH_ENDPOINT}/bulk?session_id=${sessionId}&sort_by=updated_at&sort_order=desc`;
      
      // 获取数据
      const response = await fetchBulk(endpoint);
      console.log('获取到的原始响应:', response);
      
      // 处理返回数据格式 {"data":[],"count":1}
      if (!response || !response.data || !Array.isArray(response.data)) {
        console.log('未找到对应的WorkflowGraph数据或数据格式错误');
        return [];
      }

      const workflowGraphList: WorkflowGraphData[] = response.data;
      console.log(`找到${workflowGraphList.length}条WorkflowGraph记录，总数量：${response.count}`);

      // 返回完整的记录数组
      return workflowGraphList;

    } catch (error) {
      console.error('获取WorkflowGraph记录失败:', error);
      return [];
    }
  }

  /**
   * 根据session_id获取WorkflowGraph数据中的原始code_blocks
   * @param sessionId 会话ID
   * @returns 原始的code_blocks数据结构
   */
  static async getRawCodeBlocksBySessionId(sessionId: string): Promise<Record<string, WorkflowCodeBlock[]> | null> {
    try {
      console.log('获取session_id对应的原始code_blocks数据:', sessionId);
      
      // 构建查询URL，按updated_at倒序排列
      const endpoint = `${WORKFLOW_GRAPH_ENDPOINT}/bulk?session_id=${sessionId}&sort_by=updated_at&sort_order=desc`;
      
      // 获取数据
      const response = await fetchBulk(endpoint);
      console.log('原始响应数据:', response);
      
      // 处理返回数据格式 {"data":[],"count":1}
      if (!response || !response.data || !Array.isArray(response.data)) {
        console.log('未找到对应的WorkflowGraph数据或数据格式错误');
        return null;
      }

      const workflowGraphList: WorkflowGraphData[] = response.data;
      console.log(`找到${workflowGraphList.length}条WorkflowGraph记录，总数量：${response.count}`);

      // 返回最新的一条记录的code_blocks（按updated_at倒序，第一条就是最新的）
      if (workflowGraphList.length > 0) {
        const latestWorkflow = workflowGraphList[0];
        console.log(`使用最新记录(ID: ${latestWorkflow.id})的code_blocks`);
        
        if (latestWorkflow.code_blocks && typeof latestWorkflow.code_blocks === 'object') {
          console.log('原始code_blocks数据类型:', Array.isArray(latestWorkflow.code_blocks) ? '数组' : '对象');
          console.log('原始code_blocks数据内容:', latestWorkflow.code_blocks);
          
          // 如果是对象格式，直接返回
          if (!Array.isArray(latestWorkflow.code_blocks)) {
            return latestWorkflow.code_blocks as Record<string, WorkflowCodeBlock[]>;
          }
        }
      }

      console.log('未找到有效的code_blocks数据');
      return null;

    } catch (error) {
      console.error('获取原始code_blocks数据失败:', error);
      return null;
    }
  }

  /**
   * 根据session_id获取WorkflowGraph数据并提取HTML/SVG代码块
   * @param sessionId 会话ID
   * @returns 原始的代码块数组
   */
  
  static async getCodeBlocksBySessionId(sessionId: string): Promise<WorkflowCodeBlock[]> {
    try {
      console.log('获取session_id对应的WorkflowGraph数据:', sessionId);
      
      // 构建查询URL，按updated_at倒序排列
      const endpoint = `${WORKFLOW_GRAPH_ENDPOINT}/bulk?session_id=${sessionId}&sort_by=updated_at&sort_order=desc`;
      
      // 获取数据
      const response = await fetchBulk(endpoint);
      console.log(response,'responseresponseresponseresponseresponseresponseresponse');
      // 处理返回数据格式 {"data":[],"count":1}
      if (!response || !response.data || !Array.isArray(response.data)) {
        console.log('未找到对应的WorkflowGraph数据或数据格式错误');
        return [];
      }

      const workflowGraphList: WorkflowGraphData[] = response.data;
      console.log(`找到${workflowGraphList.length}条WorkflowGraph记录，总数量：${response.count}`);
      // 收集所有的代码块
      const allCodeBlocks: WorkflowCodeBlock[] = [];

      // 遍历所有WorkflowGraph记录，按updated_at倒序处理
      console.log(workflowGraphList,'workflowGraphList');
      workflowGraphList.forEach((workflowGraph, index) => {
        console.log(`处理第${index + 1}条记录，updated_at: ${workflowGraph.updated_at}`);
        
        if (workflowGraph.code_blocks && typeof workflowGraph.code_blocks === 'object') {
          console.log(`记录${workflowGraph.id}的code_blocks类型:`, Array.isArray(workflowGraph.code_blocks) ? '数组' : '对象');
          console.log(`记录${workflowGraph.id}的code_blocks内容:`, workflowGraph.code_blocks);
       
          // 处理不同格式的code_blocks
          if (Array.isArray(workflowGraph.code_blocks)) {
            // 数组格式：直接展开
            allCodeBlocks.push(...workflowGraph.code_blocks);
          } else {
            // 对象格式：遍历所有节点的代码块
            Object.values(workflowGraph.code_blocks).forEach(nodeCodeBlocks => {
              if (Array.isArray(nodeCodeBlocks)) {
                allCodeBlocks.push(...nodeCodeBlocks);
              }
            });
          }
          
          console.log(`从记录${workflowGraph.id}中提取到代码块`);
        } else {
          console.log(`记录${workflowGraph.id}的code_blocks为空或不是对象类型:`, workflowGraph.code_blocks);
        }
      });
      return allCodeBlocks;

    } catch (error) {
      console.error('获取WorkflowGraph代码块失败:', error);
      return [];
    }
  }

  /**
   * 根据session_id获取WorkflowGraph数据并转换为ViewerCodeBlock格式
   * @param sessionId 会话ID
   * @returns 转换后的ViewerCodeBlock数组
   */
  static async getViewerCodeBlocksBySessionId(sessionId: string): Promise<ViewerCodeBlock[]> {
    try {
      console.log('=== getViewerCodeBlocksBySessionId 方法开始执行 ===');
      
      // 获取原始代码块
      const rawCodeBlocks = await this.getCodeBlocksBySessionId(sessionId);
      
      // 转换为ViewerCodeBlock格式
      const viewerCodeBlocks: ViewerCodeBlock[] = [];
      rawCodeBlocks.forEach((block, index) => {
        if (this.isHtmlOrSvgBlock(block)) {
          const type = this.determineCodeType(block);
          if (type) {
            viewerCodeBlocks.push({
              id: `${sessionId}_${block.content_id}_${index}`,
              type: type,
              content: block.code
            });
          }
        }
      });
      
      console.log(`转换后得到${viewerCodeBlocks.length}个HTML/SVG代码块`);
      return viewerCodeBlocks;

    } catch (error) {
      console.error('获取转换后的ViewerCodeBlock失败:', error);
      return [];
    }
  }

  /**
   * 判断代码块是否为HTML或SVG类型
   * @param block 工作流代码块
   * @returns 是否为HTML或SVG
   */
  private static isHtmlOrSvgBlock(block: WorkflowCodeBlock): boolean {
    if (!block.has_code || !block.code || !block.code.trim()) {
      return false;
    }

    const language = block.language.toLowerCase();
    
    // 检查语言类型
    if (language === 'html' || language === 'svg') {
      return true;
    }

    // 检查代码内容特征
    const code = block.code.trim().toLowerCase();
    
    // 检查是否为HTML文档
    if (code.includes('<!doctype html>') || 
        code.includes('<html') || 
        (code.includes('<head>') && code.includes('<body>'))) {
      return true;
    }

    // 检查是否为SVG
    if (code.includes('<svg') && code.includes('</svg>')) {
      return true;
    }

    return false;
  }

  /**
   * 确定代码块的具体类型（html或svg）
   * @param block 工作流代码块
   * @returns 代码类型
   */
  private static determineCodeType(block: WorkflowCodeBlock): 'html' | 'svg' | null {
    const language = block.language.toLowerCase();
    
    // 优先根据language字段判断
    if (language === 'svg') {
      return 'svg';
    }
    
    if (language === 'html') {
      return 'html';
    }

    // 根据代码内容判断
    const code = block.code.trim().toLowerCase();
    
    // 如果代码以<svg开头且包含</svg>，判断为SVG
    if (code.startsWith('<svg') && code.includes('</svg>')) {
      return 'svg';
    }

    // 如果包含HTML文档结构，判断为HTML
    if (code.includes('<!doctype html>') || 
        code.includes('<html') || 
        (code.includes('<head>') && code.includes('<body>'))) {
      return 'html';
    }

    // 如果只包含SVG标签，判断为SVG
    if (code.includes('<svg') && code.includes('</svg>')) {
      return 'svg';
    }

    // 默认判断为HTML
    return 'html';
  }

  /**
   * 验证代码块内容是否有效
   * @param block 代码块
   * @returns 是否有效
   */
  static validateCodeBlock(block: ViewerCodeBlock): boolean {
    if (!block.content || !block.content.trim()) {
      return false;
    }

    if (block.type === 'svg') {
      // 验证SVG内容
      try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(block.content, 'image/svg+xml');
        const parseError = doc.querySelector('parsererror');
        return !parseError;
      } catch (error) {
        console.error('SVG验证失败:', error);
        return false;
      }
    }

    if (block.type === 'html') {
      // 简单的HTML验证
      return block.content.includes('<') && block.content.includes('>');
    }

    return true;
  }

  /**
   * 去重代码块（基于内容）
   * @param codeBlocks 代码块数组
   * @returns 去重后的代码块数组
   */
  static deduplicateCodeBlocks(codeBlocks: ViewerCodeBlock[]): ViewerCodeBlock[] {
    const uniqueBlocks: ViewerCodeBlock[] = [];
    const contentSet = new Set<string>();

    codeBlocks.forEach(block => {
      // 创建内容标识符：类型+内容的组合
      const contentId = `${block.type}:${block.content.trim()}`;

      if (!contentSet.has(contentId)) {
        contentSet.add(contentId);
        uniqueBlocks.push(block);
      }
    });

    if (uniqueBlocks.length < codeBlocks.length) {
      console.log(`代码块去重：原始${codeBlocks.length}个，去重后${uniqueBlocks.length}个`);
    }

    return uniqueBlocks;
  }

  /**
   * 限制代码块数量
   * @param codeBlocks 代码块数组
   * @param maxCount 最大数量，默认50
   * @returns 限制后的代码块数组
   */
  static limitCodeBlocks(codeBlocks: ViewerCodeBlock[], maxCount: number = 50): ViewerCodeBlock[] {
    if (codeBlocks.length <= maxCount) {
      return codeBlocks;
    }

    console.warn(`代码块数量(${codeBlocks.length})超过限制(${maxCount})，将截取最新的${maxCount}个`);
    return codeBlocks.slice(-maxCount); // 取最新的maxCount个
  }
}

export default WorkflowCodeBlockProcessor; 