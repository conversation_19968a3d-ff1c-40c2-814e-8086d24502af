/**
 * 工作流内容处理器
 * 用于处理工作流图表数据并生成Markdown内容
 * 支持新旧两种数据结构
 */

export interface WorkflowNode {
  id: string;
  label: string;
  data: {
    output_contents?: Array<{
      content: string;
      agent_name: string;
    }>;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface WorkflowContent {
  content: any;
  agent_name: string;
  query?: string;
  prompt_name?: string;
  [key: string]: any;
}

export interface WorkflowVersionInfo {
  input_query_ids: string[];
  input_content_ids: string[];
  input_attachment_ids: string[];
  output_query_ids: string[];
  output_content_ids: string[];
  output_attachment_ids: string[];
  timestamp: string;
  description: string;
  created_by: string;
}

export interface WorkflowVersionData {
  current_version: string;
  versions: Record<string, WorkflowVersionInfo>;
}

export interface OldWorkflowGraph {
  graph: {
    nodes: WorkflowNode[];
    [key: string]: any;
  };
  node_sort?: string[];
  [key: string]: any;
}

export interface NewWorkflowGraph {
  node_sort: string[];
  version_data: Record<string, WorkflowVersionData>;
  workflow_contents: Record<string, WorkflowContent>;
  [key: string]: any;
}

export class WorkflowContentProcessor {
  /**
   * 从工作流图表生成Markdown内容
   * @param graph 工作流图表数据（支持新旧数据结构）
   * @returns 生成的Markdown内容
   */
  static generateMarkdownFromGraph(graph: any): string {
    if (!graph) {
      return '找不到有效的图表数据';
    }

    // 检查新数据结构
    if (graph.node_sort && graph.version_data && graph.workflow_contents) {
      console.log('检测到新数据结构，使用新的处理方法');
      return this.processNewDataStructure(graph as NewWorkflowGraph);
    }
    
    // 兼容旧数据结构
    if (graph.graph && graph.graph.nodes) {
      console.log('检测到旧数据结构，使用兼容处理方法');
      return this.processOldDataStructure(graph as OldWorkflowGraph);
    }

    return '数据结构不支持或数据不完整';
  }

  /**
   * 处理新数据结构
   * @param graph 新格式的工作流图表数据
   * @returns 生成的Markdown内容
   */
  private static processNewDataStructure(graph: NewWorkflowGraph): string {
    const nodeSortOrder = graph.node_sort || [];
    let combinedContent = '';

    console.log('处理新数据结构，节点排序:', nodeSortOrder);
    console.log('版本数据:', Object.keys(graph.version_data));
    console.log('工作流内容:', Object.keys(graph.workflow_contents));

    // 遍历排序后的节点
    nodeSortOrder.forEach((nodeId: string) => {
      const nodeVersionData = graph.version_data[nodeId];
      if (!nodeVersionData) {
        console.log(`节点 ${nodeId} 没有版本数据，跳过`);
        return;
      }

      // 获取最大版本
      const selectedVersion = this.getMaxVersion(nodeVersionData);
      
      if (!selectedVersion || !nodeVersionData.versions[selectedVersion]) {
        console.log(`节点 ${nodeId} 没有有效版本，跳过`);
        return;
      }

      const versionInfo = nodeVersionData.versions[selectedVersion];
      const outputContentIds = versionInfo.output_content_ids || [];

      console.log(`节点 ${nodeId} 使用版本 ${selectedVersion}，输出内容ID:`, outputContentIds);

      // 为整个节点的所有output_contents内容添加一个<NODE>标签对
      if (outputContentIds.length > 0) {
        let nodeContent = '<NODE>\n';

        // 遍历输出内容ID，从workflow_contents中获取内容
        outputContentIds.forEach((contentId: string) => {
          const contentData = graph.workflow_contents[contentId];
          if (contentData && contentData.content) {
            const contentText = this.extractContentText(contentData.content);
            
            if (contentText) {
              nodeContent += contentText + '\n';
            }
          } else {
            console.log(`内容 ${contentId} 不存在或为空`);
          }
        });

        // 关闭NODE标签
        nodeContent += '</NODE>\n\n';
        combinedContent += nodeContent;
      }
    });

    return combinedContent;
  }

  /**
   * 处理旧数据结构（兼容性）
   * @param graph 旧格式的工作流图表数据
   * @returns 生成的Markdown内容
   */
  private static processOldDataStructure(graph: OldWorkflowGraph): string {
    console.log('处理旧数据结构');

    const nodeSortOrder = graph.node_sort || [];
    let sortedNodes: WorkflowNode[] = [];

    // 如果有节点排序数组，按照顺序排列节点
    if (nodeSortOrder.length > 0) {
      const nodeMap = new Map<string, WorkflowNode>();
      graph.graph.nodes.forEach((node: WorkflowNode) => {
        nodeMap.set(node.id, node);
      });

      sortedNodes = nodeSortOrder
        .map((id: string) => nodeMap.get(id))
        .filter((node: WorkflowNode | undefined): node is WorkflowNode => node !== undefined);
    } else {
      sortedNodes = graph.graph.nodes;
    }

    // 合并所有节点的output_contents
    let combinedContent = '';

    sortedNodes.forEach((node: WorkflowNode) => {
      if (node.data && Array.isArray(node.data.output_contents) && node.data.output_contents.length > 0) {
        let nodeContent = '<NODE>\n';

        node.data.output_contents.forEach((item: any) => {
          if (item && typeof item.content === 'string') {
            nodeContent += item.content + '\n';
          }
        });

        nodeContent += '</NODE>\n\n';
        combinedContent += nodeContent;
      }
    });

    return combinedContent;
  }

  /**
   * 获取最大版本号
   * @param nodeVersionData 节点版本数据
   * @returns 最大版本号
   */
  private static getMaxVersion(nodeVersionData: WorkflowVersionData): string | null {
    // 优先使用current_version
    if (nodeVersionData.current_version) {
      return nodeVersionData.current_version;
    }

    // 如果没有current_version，选择最大版本
    if (nodeVersionData.versions) {
      const versions = Object.keys(nodeVersionData.versions);
      if (versions.length > 0) {
        // 版本比较，假设版本格式为v1, v2, v3等
        return versions.sort((a, b) => {
          const aNum = this.extractVersionNumber(a);
          const bNum = this.extractVersionNumber(b);
          return bNum - aNum; // 降序排列，取最大版本
        })[0];
      }
    }

    return null;
  }

  /**
   * 从版本字符串中提取版本号
   * @param version 版本字符串（如 "v1", "v2"）
   * @returns 版本号数字
   */
  private static extractVersionNumber(version: string): number {
    const match = version.match(/v?(\d+)/i);
    return match ? parseInt(match[1]) : 0;
  }

  /**
   * 从内容对象中提取文本内容
   * @param content 内容对象
   * @returns 提取的文本内容
   */
  private static extractContentText(content: any): string {
    if (typeof content === 'string') {
      return content;
    }
    
    if (typeof content === 'object' && content !== null) {
      // 处理问题数组
      if (content.questions && Array.isArray(content.questions)) {
        return content.questions
          .map((q: string, index: number) => `${index + 1}. ${q}`)
          .join('\n');
      }
      
      // 处理其他特殊对象结构
      if (content.text && typeof content.text === 'string') {
        return content.text;
      }
      
      if (content.message && typeof content.message === 'string') {
        return content.message;
      }
      
      // 其他对象类型，转换为格式化的JSON字符串
      try {
        return JSON.stringify(content, null, 2);
      } catch (error) {
        console.error('JSON序列化失败:', error);
        return String(content);
      }
    }
    
    return String(content);
  }

  /**
   * 提取节点内容（用于后续处理）
   * @param content 完整的内容字符串
   * @returns 提取的节点数组
   */
  static extractNodes(content: string): Array<{ content: string; isMarkdown?: boolean }> {
    const regex = /<NODE>([\s\S]*?)<\/NODE>/g;
    const extractedNodes: Array<{ content: string; isMarkdown?: boolean }> = [];
    let match;

    while ((match = regex.exec(content)) !== null) {
      const nodeContent = match[1].trim();
      extractedNodes.push({
        content: nodeContent,
        isMarkdown: true
      });
    }

    console.log('提取节点内容数量:', extractedNodes.length);
    return extractedNodes;
  }

  /**
   * 验证数据结构类型
   * @param graph 图表数据
   * @returns 数据结构类型
   */
  static detectDataStructureType(graph: any): 'new' | 'old' | 'unknown' {
    if (!graph) {
      return 'unknown';
    }

    if (graph.node_sort && graph.version_data && graph.workflow_contents) {
      return 'new';
    }

    if (graph.graph && graph.graph.nodes) {
      return 'old';
    }

    return 'unknown';
  }
}

export default WorkflowContentProcessor; 