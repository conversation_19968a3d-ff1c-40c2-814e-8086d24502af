// src/utils/openaiClient.ts
import OpenAI from 'openai';
import {CHAT_API_BASE_URL } from '../Constant/ServerConstant';

// 获取token函数保持不变
const getToken = (): string | null => localStorage.getItem('access_token');

// 替换为创建客户端的工厂函数
const createOpenAIClient = () => {
  // 每次调用时获取最新的token
  const token = getToken() || '';
  console.log(token, 'token');
  // 检查token是否存在，如果不存在可以在控制台输出警告
  if (!token) {
    console.warn('未找到访问令牌，API请求可能会失败');
  }
  
  return new OpenAI({
    baseURL: CHAT_API_BASE_URL,
    apiKey: token,
    dangerouslyAllowBrowser: true,
  });
};

// 导出工厂函数而非实例
export default createOpenAIClient;