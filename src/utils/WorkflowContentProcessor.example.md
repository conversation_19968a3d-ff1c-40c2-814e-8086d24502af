# WorkflowContentProcessor 使用指南

`WorkflowContentProcessor` 是一个通用的工作流内容处理器，用于处理工作流图表数据并生成Markdown内容。它支持新旧两种数据结构，并提供了完整的类型定义。

## 主要功能

1. **数据结构兼容性**: 自动检测并处理新旧两种工作流数据格式
2. **版本管理**: 自动选择最大版本号的内容
3. **内容拼接**: 基于node_sort排序拼接节点内容
4. **节点提取**: 从Markdown内容中提取节点信息

## 基本用法

```typescript
import WorkflowContentProcessor from '../utils/WorkflowContentProcessor';

// 1. 生成Markdown内容
const markdownContent = WorkflowContentProcessor.generateMarkdownFromGraph(graphData);

// 2. 提取节点内容
const extractedNodes = WorkflowContentProcessor.extractNodes(markdownContent);

// 3. 检测数据结构类型
const dataType = WorkflowContentProcessor.detectDataStructureType(graphData);
console.log('数据结构类型:', dataType); // 'new', 'old', or 'unknown'
```

## 数据结构类型

### 新数据结构
包含以下字段的数据会被识别为新结构：
- `node_sort`: 节点排序数组
- `version_data`: 版本数据对象
- `workflow_contents`: 工作流内容对象

### 旧数据结构
包含以下字段的数据会被识别为旧结构：
- `graph.nodes`: 节点数组，包含 `output_contents`

## 新数据结构示例

```typescript
const newFormatData = {
  node_sort: ['start-node', 'node-1', 'node-2'],
  version_data: {
    'node-1': {
      current_version: 'v2',
      versions: {
        'v1': { output_content_ids: ['content-1'] },
        'v2': { output_content_ids: ['content-2', 'content-3'] }
      }
    }
  },
  workflow_contents: {
    'content-2': {
      content: { questions: ['问题1', '问题2'] },
      agent_name: '问题分解'
    },
    'content-3': {
      content: 'HTML内容或其他文本',
      agent_name: 'HTML生成'
    }
  }
};

// 处理新格式数据
const markdown = WorkflowContentProcessor.generateMarkdownFromGraph(newFormatData);
```

## 旧数据结构示例

```typescript
const oldFormatData = {
  graph: {
    nodes: [
      {
        id: 'node-1',
        data: {
          output_contents: [
            { content: '节点1的内容', agent_name: 'agent1' },
            { content: '节点1的更多内容', agent_name: 'agent1' }
          ]
        }
      }
    ]
  },
  node_sort: ['node-1']
};

// 处理旧格式数据
const markdown = WorkflowContentProcessor.generateMarkdownFromGraph(oldFormatData);
```

## 内容类型处理

处理器能够智能处理多种内容类型：

1. **字符串内容**: 直接使用
2. **问题数组**: 格式化为编号列表
   ```json
   { "questions": ["问题1", "问题2"] }
   ```
   转换为:
   ```
   1. 问题1
   2. 问题2
   ```
3. **特殊对象**: 提取 `text` 或 `message` 字段
4. **其他对象**: 转换为格式化的JSON字符串

## 版本选择策略

1. **优先使用 current_version**: 如果存在则直接使用
2. **自动选择最大版本**: 按版本号降序排列，选择最大的版本
3. **版本格式支持**: 支持 `v1`, `v2`, `version1` 等格式

## 输出格式

生成的Markdown内容使用 `<NODE>` 标签包装每个节点的内容：

```markdown
<NODE>
1. 重组人TNK组织型纤溶酶原激活剂的主要适应症是什么？
2. 该药物是否用于急性心肌梗死的治疗？
</NODE>

<NODE>
生成的HTML内容或其他文本内容
</NODE>
```

## 错误处理

- 自动跳过无效节点或内容
- 详细的控制台日志输出
- 优雅的错误降级处理

## 在组件中的使用

```typescript
// 在React组件中使用
import WorkflowContentProcessor from '../utils/WorkflowContentProcessor';

const MyComponent = () => {
  const processWorkflowData = (graphData: any) => {
    try {
      // 生成内容
      const markdownContent = WorkflowContentProcessor.generateMarkdownFromGraph(graphData);
      
      // 提取节点
      const nodes = WorkflowContentProcessor.extractNodes(markdownContent);
      
      // 处理节点内容...
      nodes.forEach((node, index) => {
        console.log(`节点 ${index + 1}:`, node.content);
      });
      
    } catch (error) {
      console.error('处理工作流数据失败:', error);
    }
  };

  return (
    // JSX content...
  );
};
```

## 类型定义

组件提供了完整的TypeScript类型定义：

- `WorkflowNode`: 旧格式节点类型
- `WorkflowContent`: 工作流内容类型
- `WorkflowVersionInfo`: 版本信息类型
- `WorkflowVersionData`: 版本数据类型
- `OldWorkflowGraph`: 旧数据结构类型
- `NewWorkflowGraph`: 新数据结构类型

这些类型可以帮助你在开发时获得更好的类型检查和智能提示。 