// src/utils/jwt.ts
import jwt_decode from "jwt-decode";

interface TokenPayload {
    exp: number;
    [key: string]: any;
}

export const isTokenExpired = (token: string): boolean => {
    try {
        const decoded: TokenPayload = jwt_decode(token);
        const currentTime = Date.now() / 1000;
        return decoded.exp < currentTime;
    } catch (error) {
        return true; // 如果解码失败，认为 token 无效或过期
    }
};
