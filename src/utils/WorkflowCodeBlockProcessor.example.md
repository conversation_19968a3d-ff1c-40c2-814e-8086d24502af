# WorkflowCodeBlockProcessor 使用指南

`WorkflowCodeBlockProcessor` 是一个专门用于处理WorkflowGraph数据库中code_blocks字段的工具类，它能够根据session_id获取相关的HTML/SVG代码块并转换为HtmlSvgViewer组件需要的格式。

## 主要功能

1. **数据获取**: 根据session_id从WorkflowGraph表获取code_blocks数据
2. **数据转换**: 将数据库格式转换为前端组件需要的格式
3. **智能过滤**: 只处理HTML和SVG类型的代码块
4. **数据优化**: 提供去重、验证和数量限制功能

## 数据结构

### WorkflowGraph中的code_blocks格式
```typescript
interface WorkflowCodeBlock {
  node_id: string;        // 节点ID
  node_name: string;      // 节点名称
  content_id: string;     // 内容ID
  language: string;       // 代码语言类型
  code: string;          // 代码内容
  has_code: boolean;     // 是否包含代码
}
```

### HtmlSvgViewer需要的格式
```typescript
interface ViewerCodeBlock {
  id: string;            // 唯一标识符
  type: 'html' | 'svg';  // 代码类型
  content: string;       // 代码内容
}
```

## 基本用法

### 1. 获取代码块
```typescript
import WorkflowCodeBlockProcessor from '../utils/WorkflowCodeBlockProcessor';

const getCodeBlocks = async (sessionId: string) => {
  try {
    // 获取session_id对应的所有HTML/SVG代码块
    const codeBlocks = await WorkflowCodeBlockProcessor.getCodeBlocksBySessionId(sessionId);
    
    console.log(`获取到${codeBlocks.length}个代码块`);
    return codeBlocks;
  } catch (error) {
    console.error('获取代码块失败:', error);
    return [];
  }
};
```

### 2. 在React组件中使用
```typescript
import React, { useState, useEffect } from 'react';
import WorkflowCodeBlockProcessor, { type ViewerCodeBlock } from '../utils/WorkflowCodeBlockProcessor';
import HtmlSvgViewer from '../HtmlSvgViewer/HtmlSvgViewer';

const CodePreviewComponent: React.FC<{ sessionId: string }> = ({ sessionId }) => {
  const [codeBlocks, setCodeBlocks] = useState<ViewerCodeBlock[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchCodeBlocks = async () => {
      if (!sessionId || sessionId === 'new') {
        setCodeBlocks([]);
        return;
      }

      try {
        setLoading(true);
        
        // 获取代码块
        let blocks = await WorkflowCodeBlockProcessor.getCodeBlocksBySessionId(sessionId);
        
        // 去重和限制数量
        blocks = WorkflowCodeBlockProcessor.deduplicateCodeBlocks(blocks);
        blocks = WorkflowCodeBlockProcessor.limitCodeBlocks(blocks, 50);
        
        setCodeBlocks(blocks);
      } catch (error) {
        console.error('获取代码块失败:', error);
        setCodeBlocks([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCodeBlocks();
  }, [sessionId]);

  if (loading) {
    return <div>正在加载代码块...</div>;
  }

  if (codeBlocks.length === 0) {
    return <div>没有找到HTML或SVG代码块</div>;
  }

  return (
    <HtmlSvgViewer 
      codeBlocks={codeBlocks}
      sessionId={sessionId}
      isTabActive={true}
    />
  );
};
```

## API 方法详解

### getCodeBlocksBySessionId()
根据session_id获取WorkflowGraph数据并提取HTML/SVG代码块。

**参数:**
- `sessionId: string` - 会话ID

**返回:**
- `Promise<ViewerCodeBlock[]>` - 转换后的代码块数组

**特性:**
- 自动按updated_at倒序获取数据
- 只返回HTML和SVG类型的代码块
- 自动生成唯一ID
- 内置错误处理

### deduplicateCodeBlocks()
去除重复的代码块（基于内容）。

```typescript
const uniqueBlocks = WorkflowCodeBlockProcessor.deduplicateCodeBlocks(codeBlocks);
```

### limitCodeBlocks()
限制代码块数量，防止前端性能问题。

```typescript
const limitedBlocks = WorkflowCodeBlockProcessor.limitCodeBlocks(codeBlocks, 50);
```

### validateCodeBlock()
验证单个代码块的有效性。

```typescript
const isValid = WorkflowCodeBlockProcessor.validateCodeBlock(block);
```

## 代码类型识别

处理器能够智能识别代码类型：

1. **优先级**: `language` 字段 > 内容分析
2. **HTML识别**:
   - `language` 为 "html"
   - 包含 `<!DOCTYPE html>` 或 `<html`
   - 包含 `<head>` 和 `<body>`

3. **SVG识别**:
   - `language` 为 "svg"  
   - 以 `<svg` 开头且包含 `</svg>`

## 在Chat.tsx中的集成

### ContentPanel集成
```typescript
// 在ContentPanel组件中
const [workflowCodeBlocks, setWorkflowCodeBlocks] = useState<ViewerCodeBlock[]>([]);

useEffect(() => {
  if (activeTab === 'code' && visible && sessionId && sessionId !== 'new') {
    fetchWorkflowCodeBlocks();
  }
}, [sessionId, activeTab, visible]);

const fetchWorkflowCodeBlocks = async () => {
  try {
    let codeBlocks = await WorkflowCodeBlockProcessor.getCodeBlocksBySessionId(sessionId);
    codeBlocks = WorkflowCodeBlockProcessor.deduplicateCodeBlocks(codeBlocks);
    codeBlocks = WorkflowCodeBlockProcessor.limitCodeBlocks(codeBlocks, 50);
    setWorkflowCodeBlocks(codeBlocks);
  } catch (error) {
    console.error('获取代码块失败:', error);
  }
};
```

### RightSidebar集成
```typescript
// 在RightSidebar组件中显示代码块数量
const [workflowCodeBlocks, setWorkflowCodeBlocks] = useState<ViewerCodeBlock[]>([]);

useEffect(() => {
  // 获取代码块数量用于显示badge
  fetchWorkflowCodeBlocksCount(sessionId);
}, [sessionId]);

const totalCodeBlocks = workflowCodeBlocks.length;
const badgeCount = totalCodeBlocks > 0 ? totalCodeBlocks : false;
```

## 错误处理

```typescript
try {
  const codeBlocks = await WorkflowCodeBlockProcessor.getCodeBlocksBySessionId(sessionId);
  // 处理成功
} catch (error) {
  if (error.message.includes('session_id')) {
    // 处理session_id相关错误
  } else if (error.message.includes('network')) {
    // 处理网络错误
  } else {
    // 处理其他错误
  }
}
```

## 性能优化

1. **数据量控制**: 使用 `limitCodeBlocks()` 限制数量
2. **去重优化**: 使用 `deduplicateCodeBlocks()` 避免重复
3. **按需加载**: 只在需要时调用API
4. **缓存策略**: 可以考虑在组件级别添加缓存

## 注意事项

1. **API依赖**: 需要后端提供 `/api/v1/workflow_graph/session/{session_id}` 端点
2. **数据格式**: 确保后端返回的数据符合 `WorkflowGraphData` 接口
3. **权限控制**: 确保用户有权限访问对应的session_id数据
4. **性能考虑**: 大量代码块可能影响渲染性能，建议使用分页或虚拟滚动

## 与现有系统的兼容性

- **向后兼容**: 保持对从Markdown提取代码块的支持
- **优雅降级**: WorkflowGraph数据不可用时自动使用Markdown数据
- **渐进增强**: 优先使用WorkflowGraph数据，提供更准确的代码块信息 