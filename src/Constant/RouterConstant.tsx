import { MANAGER_API_BASE_URL } from "./ServerConstant";
export const REGISTER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/users/register`;
export const LOGIN_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/users/login`;
export const UPDATE_USER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/users/`;
export const MEMORY_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/memory`;
export const SESSION_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/sessions`;
export const EVENT_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/events`;
export const WORKFLOW_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/workflows`;
export const PROMPT_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/prompts`;
export const AGENT_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/agents`;
export const MULTI_DIMENSIONAL_TABLE_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/product/multidimensional-tables`;
export const WORKFLOW_GRAPH_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/workflow_graph`;
export const DIGITAL_HUMAN_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/product/digital-human`;
export const KNOWLEDGE_BASE_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/knowledge_bases`;
export const MCP_SERVERS_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/mcp-servers`;