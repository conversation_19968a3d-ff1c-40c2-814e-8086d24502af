
type EnvType = 'development' | 'production' | 'local';

// 定义配置接口
interface ConfigType {
  API_BASE_URL: string;
  MANAGER_API_BASE: string;
  MANAGER_API_BASE_URL: string;
  TTS_CLONE_API_BASE_URL: string;
  NODE_MANAGER_API_BASE:string;
}

// 定义完整配置对象的类型
type ConfigMap = {
  [K in EnvType]: ConfigType;
};

// 获取环境变量
const getEnvironment = (): EnvType => {
  // 使用 import.meta.env 替代 process.env
  // const env = import.meta.env.SERVER_TYPE || 'local';
  // const env = 'development';
  // // 确保返回值是有效的 EnvType
  // if (env === 'development' || env === 'production' || env === 'local') {
  //   return env;
  // }
  
  // 默认返回 local
  return 'local';
};

const ENV = getEnvironment();

console.log('Current Environment:', ENV);

const config: ConfigMap = {
  development: {
    API_BASE_URL: 'flint.hsmap.com',
    MANAGER_API_BASE: 'llm-agent.aihuoshi.net',
    NODE_MANAGER_API_BASE:"http://192.168.98.43:3002",
    MANAGER_API_BASE_URL: 'https://llm-agent.aihuoshi.net',
    TTS_CLONE_API_BASE_URL: 'https://fish-speech.aimed.cn'
  },
  production: {
    API_BASE_URL: 'flint.hsmap.com', // 算法服务地址
    MANAGER_API_BASE: 'llm-agent.aihuoshi.net',// 后端服务地址
    MANAGER_API_BASE_URL: 'https://llm-agent.aihuoshi.net',// 后端服务地址
    TTS_CLONE_API_BASE_URL: 'https://fish-speech.aimed.cn',// 语音服务地址
    NODE_MANAGER_API_BASE:"http://192.168.98.43:3002"// 节点管理服务地址
  },
  local: {
    API_BASE_URL: '192.168.98.43:8800',// 算法服务地址
    MANAGER_API_BASE: '192.168.98.43:8880',// 后端服务地址
    MANAGER_API_BASE_URL: 'http://192.168.98.43:8880',// 后端服务地址
    TTS_CLONE_API_BASE_URL: 'http://192.168.98.43:9999',// 语音服务地址
    NODE_MANAGER_API_BASE:"http://192.168.98.43:3002"
  }
};

const currentConfig = config[ENV];

export const {
  API_BASE_URL,
  MANAGER_API_BASE,
  MANAGER_API_BASE_URL,
  TTS_CLONE_API_BASE_URL,
  NODE_MANAGER_API_BASE
} = currentConfig;

export const CHAT_API_BASE_URL = `${MANAGER_API_BASE_URL}/api/v1/flint/v1`;
export const TTS_API_BASE_URL = `${MANAGER_API_BASE_URL}/api/v1/audio/tts`;