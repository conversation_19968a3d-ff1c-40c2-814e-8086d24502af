// src/main.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { Provider } from 'react-redux';
import store from './store';
import './index.css'; // 如果有全局样式
import { ConfigProvider } from 'antd';

// 创建一个弹出容器并添加到body
const popupContainer = document.createElement('div');
popupContainer.id = 'antd-popup-container';
popupContainer.style.position = 'absolute';
popupContainer.style.top = '0';
popupContainer.style.left = '0';
popupContainer.style.width = '100%';
document.body.appendChild(popupContainer);

// 使用另一种方法解决findDOMNode警告 - 包装整个应用在ConfigProvider中
// 使用固定容器作为getPopupContainer
// 保留StrictMode以维持React推荐的最佳实践

ReactDOM.createRoot(document.getElementById('root')!).render(
    <React.StrictMode>
        <Provider store={store}>
            <ConfigProvider 
                getPopupContainer={() => document.getElementById('antd-popup-container') || document.body}
            >
            <App />
            </ConfigProvider>
        </Provider>
    </React.StrictMode>
);
