// src/App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Chat from './components/Chat/Chat';
import {
  REGISTER_API_NAME,
  LOGIN_API_NAME,
  RESET_PASSWORD_API_NAME,
  CHAT_API_NAME,
  MEETING_NOTEs,
  WORKFLOWS_API_NAME,
  AGENTS_API_NAME,
} from './Constant/Constant';
import LoginForm from './components/Users/<USER>';
import RegisterForm from './components/Users/<USER>';
import ResetPasswordForm from './components/Users/<USER>';
import ProtectedRoute from './components/ProtectedRoute';
import 'antd/dist/reset.css'; // Import Ant Design styles
import MeetingNotes from './components/products/MeetingNotes';
import Home from './components/Home';
import ExcelProcessor from './components/products/MultidimensionalTable/ExcelProcessor';
import Workflows from './components/Workflows/Workflows';
import Agents from './components/Agents/Agents';
import MindMapGenerate from './components/products/MindMap/MindMapGenerate';
import VoiceClone from './components/products/VoiceClone';
import PodcastGen from './components/products/PodcastGen';
import DigitalHuman from './components/products/DigitalHuman/DigitalHuman';
import IntegratedDigitalHuman from './components/products/DigitalHuman/IntegratedDigitalHuman';
import AITextDetection from './components/products/AITextDetection';
import AICourseGenerate from './components/products/AICourse';
import CodeRunnerPage from './components/CodeRunner/CodeRunnerPage';
// import './App.css';

// 在路由中添加：

const App: React.FC = () => (
  <Router>

    <Routes>
      {/* 登录、注册和重置密码路由 */}
      <Route path={`${LOGIN_API_NAME}`} element={<LoginForm />} />
      <Route path={`${REGISTER_API_NAME}`} element={<RegisterForm />} />
      <Route path={`${RESET_PASSWORD_API_NAME}`} element={
        <ProtectedRoute>
        <ResetPasswordForm />
      </ProtectedRoute>
      } />
      <Route path={`${MEETING_NOTEs}`} element={
          <ProtectedRoute>
            <MeetingNotes />
          </ProtectedRoute>

        } />
      <Route path="/" element={
        <ProtectedRoute>
          <Navigate to="/chat/new/new?workflow_or_agent=workflow" replace />
        </ProtectedRoute>
        } />
      <Route path="/excel" element={
        <ProtectedRoute>
        <ExcelProcessor />
        </ProtectedRoute>
        } />
      <Route path="/home" element={
        <ProtectedRoute>
        <Home />
        </ProtectedRoute>
        } />
      {/* 工作流路由 */}
      <Route
        path={`${WORKFLOWS_API_NAME}`}
        element={
          <ProtectedRoute>
            <Workflows />
          </ProtectedRoute>
        }
      />

      {/* 智能体路由 */}
      <Route
        path={`${AGENTS_API_NAME}`}
        element={
          <ProtectedRoute>
            <Agents />
          </ProtectedRoute>
        }
      />

      {/* 添加工作流聊天路由 */}
      <Route
        path={`${CHAT_API_NAME}/:workflow_id/:session_id`}
        element={
          <ProtectedRoute>
            <Chat />
          </ProtectedRoute>
        }
      />

      {/* 添加语音克隆路由 */}
      <Route
        path="/voice-clone"
        element={
          <ProtectedRoute>
            <VoiceClone />
          </ProtectedRoute>
        }
      />

      {/* 添加播客生成器路由 */}
      <Route
        path="/podcast-gen"
        element={
          <ProtectedRoute>
            <PodcastGen />
          </ProtectedRoute>
        }
      />

      {/* 添加数字人视频路由 */}
      <Route
        path="/digital-human"
        element={
          <ProtectedRoute>
            <Navigate to="/digital-human/new" replace />
          </ProtectedRoute>
        }
      />
      <Route
        path="/digital-human/:id"
        element={
          <ProtectedRoute>
            <IntegratedDigitalHuman />
          </ProtectedRoute>
        }
      />

      {/* 添加数字人视频路由（步骤版） */}
      <Route
        path="/digital-human-steps"
        element={
          <ProtectedRoute>
            <DigitalHuman />
          </ProtectedRoute>
        }
      />

      {/* 添加代码运行器路由 */}
      <Route
        path="/code-runner"
        element={
          <ProtectedRoute>
            <CodeRunnerPage />
          </ProtectedRoute>
        }
      />

      {/* 思维导图路由 */}
      <Route
        path='mindmap'
        element={
          <ProtectedRoute>
            <MindMapGenerate initialText="" />
          </ProtectedRoute>
        }
      />

      {/* AI文本检测路由 */}
      <Route
        path="/product/ai-text-detection"
        element={
          <ProtectedRoute>
            <AITextDetection />
          </ProtectedRoute>
        }
      />

      {/* AI课程生成器路由 */}
      <Route
        path="/product/ai-course"
        element={
          <ProtectedRoute>
            <AICourseGenerate />
          </ProtectedRoute>
        }
      />
      {/* AI课程生成器路由 - 带session_id参数 */}
      <Route
        path="/product/ai-course/:session_id"
        element={
          <ProtectedRoute>
            <AICourseGenerate />
          </ProtectedRoute>
        }
      />

      {/* 处理未匹配的路由 */}
      <Route
        path="*"
        element={
          <ProtectedRoute>
            <Navigate to="/" replace />
          </ProtectedRoute>
        }
      />
    </Routes>

  </Router>
);

export default App;
