:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --chat-input-max-width: 80%;  /* 控制输入框宽度 */
  --chat-input-margin-bottom: 20px;  /* 控制与底部的距离 */
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-y: auto;
}

#root {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  overflow-y: auto;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* 输入框容器，限制宽度 */
.ant-sender {
  margin-bottom: var(--chat-input-margin-bottom) !important;
  width: var(--chat-input-max-width) !important;
  margin-left: auto !important;
  margin-right: auto !important;
  border-radius: 16px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
}

/* 输入框本身的样式 */
.ant-sender-input {
  background: transparent !important;
  margin: 12px 0 !important;
  min-height: 48px !important;
}

/* 输入框工具栏样式 */
.ant-sender-toolbar {
  padding: 8px !important;
}

/* 发送按钮样式 */
.ant-sender-action-button {
  background: linear-gradient(135deg, #1890ff, #096dd9) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.5) !important;
}

/* 发送按钮图标 */
.ant-sender-action-button svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) !important;
  color: white !important;
}
