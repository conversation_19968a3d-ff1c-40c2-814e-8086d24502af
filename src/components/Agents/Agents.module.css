.agentsContainer {
  width: 100%;
  height: 100vh;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0 auto;
}

.agentsHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 36px;
  top: 8px;
  width: 100%;
  text-align: center;
  position: relative;
}

.title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  text-align: center;
}

.headerSettings {
  position: absolute;
  right: 16px;
  
  transform: translateY(-50%);
}

.sortControls {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  flex: 1;
}

.cardContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  flex: 1;
  overflow-y: auto;
  justify-items: center;
  justify-content: center;
  align-items: start;
  width: 100%;
  margin: 0 auto;
  padding: 0 10px;
}

.card {
  width: 280px;
  height: 220px;
  transition: all 0.3s ease;
  border-radius: 8px;
  /* overflow: hidden; */
}

/* 固定卡片宽度 */
:global(.ant-card.ant-card-bordered.ant-card-hoverable._card_17svv_49.css-dev-only-do-not-override-1kf000u) {
  width: 100% !important;
}

/* 备用选择器，以防类名有变化 */
:global(.ant-card.ant-card-bordered.ant-card-hoverable) {
  width: 100% !important;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 固定卡片内容区域大小 */
.card :global(.ant-card-body) {
  /* height: 180px; */
  height: 100%;
  width: 100%;
  padding: 16px;
  /* overflow: hidden; */
  display: flex;
  flex-direction: column;
  position: relative;
}

.cardContent {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  /* padding-bottom: 24px; */
}

.agentName {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  /* white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
}

.agentType {
  font-size: 14px;
  color: #1890ff;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.agentDescription {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  cursor: pointer;
  /* 三行省略 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.agentDate {
  font-size: 12px;
  color: #999;
  text-align: right;
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  padding-right: 16px;
  padding-bottom: 8px;
  margin-top: auto;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.empty {
  margin: 60px 0;
}

@media (max-width: 768px) {
  .cardContainer {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
  
  .agentsHeader {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .headerSettings {
    margin-top: 12px;
    align-self: flex-end;
  }
}

.container {
  width: 100%;
  height: 100vh;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0 auto;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  width: 100%;
  text-align: center;
}

.cardsContainer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  flex: 1;
  overflow-y: auto;
  padding: 0 10px;
  justify-items: center;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0 auto;
}

.cardTitle {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #1890ff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cardDescription {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  font-size: 12px;
  color: #999;
}

.paginationContainer {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  flex: 1;
}

/* .tooltip {
  max-width: 300px;
  white-space: normal;
  word-wrap: break-word;
}  */