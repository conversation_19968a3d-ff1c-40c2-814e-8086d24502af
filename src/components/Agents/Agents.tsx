import React, { useEffect, useState } from 'react';
import { Card, Pagination, Empty, Spin, message, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import { CHAT_API_NAME } from '../../Constant/Constant';
import { AGENT_ENDPOINT } from '../../Constant/RouterConstant';
import HeaderSettings from '../common/HeaderSettings';
import styles from './Agents.module.css';
import { fetchBulk } from '../api/api';
import { useSelector } from 'react-redux';
// 定义Agent接口
interface Tool {
  id: string;
  name: string;
  description: string;
  path: string;
  method: string;
  is_active: boolean;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

interface Agent {
  id: string;
  name: string;
  description: string;
  agent_type: string;
  llm_name: string;
  is_active: boolean;
  is_published: boolean;
  tools: Tool[];
  created_at: string;
  updated_at: string;
  username: string;
}

// 定义组件Props接口
// interface AgentsProps {
//   username?: string;
// }

const Agents: React.FC = () => {
  const navigate = useNavigate();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(12);
  const { userInfo } = useSelector((state: any) => state.user);
  const username = userInfo.username; // 固定用户名
  // const [sortField, setSortField] = useState<string>('updated_at');
  const sortField = 'updated_at'
  // const [sortOrder, setSortOrder] = useState<string>('desc');
  const sortOrder = 'desc'

  // 获取智能体数据
  const fetchAgents = async () => {
    setLoading(true);
    try {
      const skip = (currentPage - 1) * pageSize;
      const limit = pageSize;
      const endpoint = `${AGENT_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${username}`;
      
      const result = await fetchBulk(endpoint);
      setAgents(result.data || []);
      setTotal(result.count || 0);
      console.log('智能体数据:', result);
    } catch (error) {
      console.error('获取智能体数据失败:', error);
      message.error('获取智能体列表失败，请稍后再试');
      setAgents([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 初始化时获取数据
  useEffect(() => {
    fetchAgents();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, pageSize, sortField, sortOrder]);

  // 处理智能体卡片点击
  const handleCardClick = (agent: Agent) => {
    // 导航到智能体详情页面或开始新的聊天，添加参数标识为agent类型
    navigate(`${CHAT_API_NAME}/${agent.id}/new?workflow_or_agent=agent`);
  };

  // 处理分页变化
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
  };

  // 处理排序变化
  // const handleSortChange = (field: string, order: string) => {
  //   setSortField(field);
  //   setSortOrder(order);
  // };

  // 渲染日期格式
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  // 截取描述前50个字符
  const truncateDescription = (description: string) => {
    if (!description) return '';
    if (description.length <= 50) return description;
    const truncated = `${description.slice(0, 50)}...`;
    console.log(`原始描述长度: ${description.length}, 截取后: ${truncated.length - 3}`);
    return truncated;
  };

  return (
    <div className={styles.agentsContainer}>
      <div className={styles.agentsHeader}>
        <h1 className={styles.title}>智能体</h1>
        <div className={styles.headerSettings}>
          <HeaderSettings />
        </div>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <Spin size="large" tip="加载中..." />
        </div>
      ) : agents.length > 0 ? (
        <div className={styles.cardContainer}>
          {agents.map((agent) => (
            <Card
              key={agent.id}
              className={styles.card}
              onClick={() => handleCardClick(agent)}
              hoverable
            >
              <div className={styles.cardContent}>
                <h2 className={styles.agentName}>{agent.name}</h2>
                <div className={styles.agentType}>{agent.agent_type}</div>
                <Tooltip 
                  title={agent.description} 
                  placement="bottom" 
                  overlayClassName={styles.tooltip}
                >
                  <div className={styles.agentDescription}>
                    {truncateDescription(agent.description)}
                  </div>
                </Tooltip>
                <div className={styles.agentDate}>
                  更新于: {formatDate(agent.updated_at)}
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Empty description="暂无智能体数据" className={styles.empty} />
      )}

      {total > 0 && (
        <div className={styles.pagination}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            pageSizeOptions={['12', '24', '36', '48']}
            showTotal={(total) => `共 ${total} 条记录`}
          />
        </div>
      )}
    </div>
  );
};

export default Agents; 