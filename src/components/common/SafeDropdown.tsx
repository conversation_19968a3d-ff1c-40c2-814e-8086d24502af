import  { forwardRef, useRef, useImperativeHandle, cloneElement, ReactElement } from 'react';
import { Dropdown, DropdownProps } from 'antd';

/**
 * 安全的Dropdown组件，避免使用findDOMNode
 * 使用ref代替findDOMNode，符合React严格模式要求
 */
export interface SafeDropdownProps extends Omit<DropdownProps, 'children'> {
  children: ReactElement;
}

const SafeDropdown = forwardRef<HTMLElement, SafeDropdownProps>((props, ref) => {
  const { children, ...dropdownProps } = props;
  const childRef = useRef<HTMLElement>(null);

  // 将子元素的DOM引用暴露给父组件
  useImperativeHandle(ref, () => childRef.current as HTMLElement);

  // 确保getPopupContainer使用固定容器或ref元素的父节点
  const getContainer = () => {
    const container = document.getElementById('antd-popup-container');
    if (container) return container;
    
    // 如果固定容器不存在，使用ref元素的父节点
    if (childRef.current && childRef.current.parentElement) {
      return childRef.current.parentElement;
    }
    
    // 最后的后备方案是body
    return document.body;
  };

  // 克隆子元素并添加ref
  const childWithRef = cloneElement(children, {
    ref: childRef
  });

  return (
    <Dropdown
      {...dropdownProps}
      getPopupContainer={getContainer}
    >
      {childWithRef}
    </Dropdown>
  );
});

SafeDropdown.displayName = 'SafeDropdown';

export default SafeDropdown; 