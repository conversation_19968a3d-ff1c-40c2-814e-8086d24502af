.settingsButton {
  padding: 1px 2px;
  font-size: 18px;
  color: #000;
}

.settingsButton:hover {
  background: rgba(0, 0, 0, 0.04);
  border-radius: 4px;
}

/* 通用的头部设置样式 */
.headerSettingsContainer {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

/* 页面标题栏样式 */
.headerContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 2px 16px;
  height: 45px;
  background-color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  margin-bottom: 20px;
  text-align: center;
}

/* 标题文字样式 */
.headerTitle {
  margin: 0;
  color: #262626;
  font-weight: 500;
  font-size: 24px;
} 