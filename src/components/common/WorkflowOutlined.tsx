import React from 'react';

const WorkflowOutlined: React.FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1024 1024"
      width="1em"
      height="1em"
      fill="currentColor"
    >
      <g>
        {/* Outer Rectangle - represents the workflow container */}
        <rect x="100" y="100" width="800" height="800" rx="50" fill="#f4f4f4" stroke="#ccc" strokeWidth="10" />

        {/* Vertical arrow to represent smart flow */}
        <path
          d="M512 250 L512 600"
          stroke="#4a90e2"
          strokeWidth="15"
          fill="none"
          strokeLinecap="round"
        />
        <polygon points="500,600 512,630 524,600" fill="#4a90e2" />

        {/* Connecting lines to represent workflow steps */}
        <path
          d="M512 300 L312 400"
          stroke="#4a90e2"
          strokeWidth="12"
          fill="none"
          strokeLinecap="round"
        />
        <path
          d="M512 300 L712 400"
          stroke="#4a90e2"
          strokeWidth="12"
          fill="none"
          strokeLinecap="round"
        />
        <path
          d="M312 400 L312 500"
          stroke="#4a90e2"
          strokeWidth="12"
          fill="none"
          strokeLinecap="round"
        />
        <path
          d="M712 400 L712 500"
          stroke="#4a90e2"
          strokeWidth="12"
          fill="none"
          strokeLinecap="round"
        />

        {/* Final steps / Workflow nodes */}
        <circle cx="312" cy="500" r="40" fill="#fff" stroke="#4a90e2" strokeWidth="12" />
        <circle cx="712" cy="500" r="40" fill="#fff" stroke="#4a90e2" strokeWidth="12" />

        {/* Inner Circle at the Top */}
        <circle cx="512" cy="150" r="20" fill="#4a90e2" />
      </g>
    </svg>
  );
};

export default WorkflowOutlined;
