import React from 'react';
import { Button } from 'antd';
import { SettingOutlined, LogoutOutlined, HomeOutlined, TeamOutlined, MessageOutlined } from '@ant-design/icons';
import styles from './HeaderSettings.module.css';
import { WORKFLOWS_API_NAME, AGENTS_API_NAME } from '../../Constant/Constant';
import WorkflowOutlined from './WorkflowOutlined';
import SafeDropdown from './SafeDropdown';

interface HeaderSettingsProps {
  className?: string;
}

const HeaderSettings: React.FC<HeaderSettingsProps> = ({ className }) => {
  const handleLogout = () => {
    localStorage.removeItem('access_token');
    window.location.href = '/login';
  };

  const handleBackHome = () => {
    window.location.href = '/home';
  };
  const handleChat = () => {
    window.location.href = '/chat/new/new?workflow_or_agent=workflow';
  };
  const handleWorkSpace = () => {
    window.location.href = `${WORKFLOWS_API_NAME}`;
  };
  const handleAgents = () => {
    window.location.href = `${AGENTS_API_NAME}`;
  };


  const menuItems = [
        {
      key: 'chat',
      icon: <MessageOutlined />,
      label: '会话',
      onClick: handleChat,
    },
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '返回主页',
      onClick: handleBackHome,
    },
    {
      key: 'workspace',
      icon: <WorkflowOutlined />,
      label: '工作空间',
      onClick: handleWorkSpace,
    },
    {
      key: 'agents',
      icon: <TeamOutlined />,
      label: '智能体',
      onClick: handleAgents,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },

  ];

  return (
    <SafeDropdown menu={{ items: menuItems }} placement="bottomRight">
      <Button 
        type="text" 
        icon={<SettingOutlined />} 
        className={`${styles.settingsButton} ${className || ''}`} 
      />
    </SafeDropdown>
  );
};

export default HeaderSettings; 