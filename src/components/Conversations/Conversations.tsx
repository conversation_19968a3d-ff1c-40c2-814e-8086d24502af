// src/components/Conversations/Conversations.tsx
import React, { useCallback, useState, useEffect } from 'react';
import { Conversations as XConversations } from '@ant-design/x';
import { Button, Dropdown, MenuProps, Input, Modal, message } from 'antd';
import { EllipsisOutlined } from '@ant-design/icons';
import styles from './Conversations.module.css';
import {deleteData,updateData, fetchBulk} from '../api/api';
import {MEMORY_ENDPOINT, SESSION_ENDPOINT,EVENT_ENDPOINT} from '../../Constant/RouterConstant';
import { useNavigate, useSearchParams } from 'react-router-dom';

interface ConversationItem {
  id: string;
  messages: any[];
  title: string;
  time: string;
}

interface ConversationsProps {
  items: ConversationItem[];
  activeKey?: string;
  className?: string;
  onActiveChange?: (key: string) => void;
  onMessagesUpdate?: (messages: any[]) => void;
  setItems: (items: ConversationItem[]) => void;
  fetchConversationMessages?: (sessionId: string) => Promise<any[]>;
}

const Conversations: React.FC<ConversationsProps> = ({
  items,
  activeKey,
  className,
  onActiveChange,
  onMessagesUpdate,
  setItems,
  fetchConversationMessages: externalFetchConversationMessages,
}) => {
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [newTitle, setNewTitle] = useState('');
  const [selectedItemId, setSelectedItemId] = useState<string>('');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const navigate = useNavigate();
  // 获取URL查询参数
  const [searchParams] = useSearchParams();

  // 检查侧边栏是否折叠
  useEffect(() => {
    const checkCollapsedState = () => {
      const collapsedState = localStorage.getItem('sidebar_collapsed') === 'true';
      setIsSidebarCollapsed(collapsedState);
    };
    
    // 初始检查
    checkCollapsedState();
    
    // 添加存储事件监听器，以便在本地存储更改时更新状态
    window.addEventListener('storage', checkCollapsedState);
    
    return () => {
      window.removeEventListener('storage', checkCollapsedState);
    };
  }, []);
  
  // Fetch single conversation messages
  const fetchConversationMessages = async (id: string) => {
    try {
      console.log(id,'id')
      
      // 优先使用外部传入的方法（来自Chat组件）
      if (externalFetchConversationMessages) {
        const messages = await externalFetchConversationMessages(id);
        onMessagesUpdate?.(messages);
        return;
      }
      
      // 降级使用原有方法（仅获取消息，不处理toolEvents）
      const data = await fetchBulk(`${EVENT_ENDPOINT}/bulk?session_id=${id}&sort_by=created_at&sort_order=asc`);
      console.log(data,'data')
      onMessagesUpdate?.(data.data);
    } catch (error) {
      console.error('获取对话消息失败:', error);
      message.error('获取对话消息失败');
    }
  };

  // Delete conversation
  const deleteConversation = async (id: string) => {
    try {
      // 1. 先删除服务器上的数据
      await deleteData(`${SESSION_ENDPOINT}/${id}`);
      
      // 2. 如果删除的是当前选中的对话，先进行处理
      if (id === activeKey) {
        // 清空消息列表 - 这个步骤很关键
        onMessagesUpdate?.([]);
        
        // 通知父组件将 activeKey 设置为 'new'
        onActiveChange?.('new');
        
        // 获取当前URL中的参数
        const workflowId = window.location.pathname.split('/')[2]; // 从URL路径获取workflow_id
        const workflowOrAgent = searchParams.get('workflow_or_agent') || 'workflow';
        const kbId = searchParams.get('kb_id');
        const mcpId = searchParams.get('mcp_id');
        
        // 构建新的URL，保留所有重要参数
        let newUrl = `/chat/${workflowId}/new?workflow_or_agent=${workflowOrAgent}`;
        
        // 添加kb_id或mcp_id参数（如果存在）
        if (kbId) {
          newUrl += `&kb_id=${kbId}`;
        } else if (mcpId) {
          newUrl += `&mcp_id=${mcpId}`;
        }
        
        // 导航到新对话，保留所有相关参数
        navigate(newUrl);
      }
      
      // 3. 从本地列表中移除被删除的对话
      const updatedItems = items.filter(item => item.id !== id);
      setItems(updatedItems);
      
      // 4. 显示成功消息
      message.success('删除对话成功');
    } catch (error) {
      console.error('删除对话失败:', error);
      message.error('删除对话失败');
    }
  };

  // Rename conversation
  const renameConversation = async (id: string, newTitle: string) => {
    try {
      await updateData(`${MEMORY_ENDPOINT}/update_title/${id}`, { title: newTitle });
      message.success('重命名成功');
      setItems(items.map(item => 
        item.id === id ? { ...item, title: newTitle } : item
      ));
    } catch (error) {
      console.error('重命名对话失败:', error);
      message.error('重命名失败');
    }
  };

  /** 选中一个会话 */
  const handleConversationSelect = useCallback((key: string) => {
    // 如果点击的是当前已选中的会话，不需要重新加载
    if (key === activeKey) return;
    
    // 先清空当前消息，避免界面闪烁
    onMessagesUpdate?.([]);
    
    // 更新选中状态
    onActiveChange?.(key);
    
    // 如果不是新会话，则获取会话消息
    if (key !== 'new') {
      fetchConversationMessages(key);
    }
  }, [activeKey, onActiveChange, onMessagesUpdate, fetchConversationMessages]);

  // Handle menu actions
  const handleMenuClick = useCallback((itemId: string, action: string) => {
    if (action === 'rename') {
      setSelectedItemId(itemId);
      const item = items.find(item => item.id === itemId);
      setNewTitle(item?.title || '');
      setIsRenameModalVisible(true);
    } else if (action === 'delete') {
      deleteConversation(itemId);
    }
  }, [items]);

  // Handle rename modal
  const handleRenameSubmit = async () => {
    if (selectedItemId && newTitle.trim()) {
      await renameConversation(selectedItemId, newTitle.trim());
      setIsRenameModalVisible(false);
    }
  };

  // Memoize dropdown menu
  const getDropdownMenuProps = useCallback((itemId: string): MenuProps => ({
    items: [
      {
        key: 'rename',
        label: '重命名',
        onClick: () => handleMenuClick(itemId, 'rename'),
      },
      {
        key: 'delete',
        label: '删除',
        onClick: () => handleMenuClick(itemId, 'delete'),
      },
    ],
    onClick: (e) => e.domEvent.stopPropagation(),
  }), [handleMenuClick]);

  // Render conversation item label
  const renderLabel = useCallback((item: ConversationItem) => {
    
    return (
      <div className={styles.conversationItem}>
        <span className={styles.label}>{item.title}</span>
        <Dropdown
          menu={getDropdownMenuProps(item.id)}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button
            type="text"
            className={styles.moreButton}
            icon={<EllipsisOutlined />}
            onClick={(e) => {
              e.stopPropagation();
            }}
          />
        </Dropdown>
      </div>
    );
  }, [getDropdownMenuProps, isSidebarCollapsed]);

  // Transform items for XConversations
  const conversationItems = React.useMemo(() => 
    items.map((item) => ({
      key: item.id,
      label: renderLabel(item),
      tooltip: isSidebarCollapsed ? item.title : undefined
    })), [items, renderLabel, isSidebarCollapsed]);

  return (
    <>
      <XConversations
        items={conversationItems}
        activeKey={activeKey}
        className={`${styles.conversations} ${className || ''} ${isSidebarCollapsed ? 'collapsed' : ''}`}
        onActiveChange={handleConversationSelect}
      />

      <Modal
        title="重命名对话"
        open={isRenameModalVisible}
        onOk={handleRenameSubmit}
        onCancel={() => setIsRenameModalVisible(false)}
      >
        <Input
          value={newTitle}
          onChange={(e) => setNewTitle(e.target.value)}
          placeholder="请输入新标题"
        />
      </Modal>
    </>
  );
};

export default React.memo(Conversations);