/* components/Conversations/Conversations.module.css */
.conversations {
  flex: 1;
  overflow: auto;
  padding: 8px;
  background: transparent;
  color: rgba(255, 255, 255, 0.85);
  width: 100%;
}

.conversations :global(.ant-menu-item) {
  border-radius: 8px;
  margin: 4px 0;
  background: rgba(24, 144, 255, 0.05);
  color: rgba(255, 255, 255, 0.85);
  transition: all 0.3s;
}

.conversations :global(.ant-menu-item):hover {
  background: rgba(24, 144, 255, 0.1);
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.2);
}

.conversations :global(.ant-menu-item-selected) {
  background: rgba(24, 144, 255, 0.15) !important;
  box-shadow: 0 0 12px rgba(24, 144, 255, 0.25);
}

.conversationItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  transition: all 0.3s;
  background: transparent;
  color: rgba(255, 255, 255, 0.85);
  position: relative;
  overflow: hidden;
  border-radius: 6px;
  padding: 2px 4px;
}

.conversationItem::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 0;
  background: linear-gradient(0deg, #1890ff, #69c0ff);
  transition: height 0.3s ease;
}

.conversationItem:hover::before {
  height: 100%;
}

.label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
  color: rgba(255, 255, 255, 0.85);
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.1);
}

.moreButton {
  opacity: 0;
  transition: all 0.2s;
  padding: 4px 8px;
  background: transparent;
  color: rgba(255, 255, 255, 0.65);
  border: none;
}

.conversationItem:hover .moreButton {
  opacity: 1;
}

.moreButton:hover {
  background: rgba(24, 144, 255, 0.15);
  color: #1890ff;
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.2);
}

/* 侧边栏折叠状态下调整对话项样式 */
:global(.collapsed) .conversationItem {
  justify-content: center;
  overflow: hidden;
}

:global(.collapsed) .label {
  width: 0;
  flex: 0;
  margin: 0;
  opacity: 0;
}

:global(.collapsed) .moreButton {
  margin: 0;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 50%;
}

:global(.collapsed) .moreButton:hover {
  opacity: 1;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.15);
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
  transform: scale(1.05);
}

/* 添加科技感的炫光效果 */
.conversationItem::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: -100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(24, 144, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.conversationItem:hover::after {
  left: 100%;
}
