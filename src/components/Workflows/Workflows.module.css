.agentsContainer {
  width: 100%;
  height: 100vh;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0 auto;
}

.agentsHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 36px;
  top: 8px;
  width: 100%;
  text-align: center;
  position: relative;
}

.title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  text-align: center;
}

.headerSettings {
  position: absolute;
  right: 16px;
  
  transform: translateY(-50%);
}

.sortControls {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  flex: 1;
}

.cardContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  flex: 1;
  overflow-y: auto;
  justify-items: center;
  justify-content: center;
  align-items: start;
  width: 100%;
  margin: 0 auto;
  padding: 0 10px;
}

.card {
  width: 280px;
  height: 220px;
  transition: all 0.3s ease;
  border-radius: 8px;
  /* overflow: hidden; */
}

/* 固定卡片宽度 */
:global(.ant-card.ant-card-bordered.ant-card-hoverable._card_17svv_49.css-dev-only-do-not-override-1kf000u) {
  width: 100% !important;
}

/* 备用选择器，以防类名有变化 */
:global(.ant-card.ant-card-bordered.ant-card-hoverable) {
  width: 100% !important;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 固定卡片内容区域大小 */
.card :global(.ant-card-body) {
  /* height: 180px; */
  height: 100%;
  width: 100%;
  padding: 16px;
  /* overflow: hidden; */
  display: flex;
  flex-direction: column;
  position: relative;
}

.cardContent {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  /* padding-bottom: 24px; */
}

.agentName {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  /* white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
}

.agentType {
  font-size: 14px;
  color: #1890ff;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.agentDescription {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  cursor: pointer;
  /* 三行省略 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.agentDate {
  font-size: 12px;
  color: #999;
  text-align: right;
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  padding-right: 16px;
  padding-bottom: 8px;
  margin-top: auto;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.empty {
  margin: 60px 0;
}

@media (max-width: 768px) {
  .cardContainer {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
  
  .agentsHeader {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .headerSettings {
    margin-top: 12px;
    align-self: flex-end;
  }
}

.container {
  min-height: 100vh;
  height: 100vh;
  padding: 24px;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 1) 0%,
    rgba(200, 255, 200, 0.9) 35%,
    rgba(150, 200, 255, 0.8) 100%
  );
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 10% 20%, rgba(100, 200, 150, 0.1), transparent 30%),
    radial-gradient(circle at 90% 80%, rgba(100, 150, 255, 0.1), transparent 30%),
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 100% 100%, 100% 100%, 20px 20px, 20px 20px;
  background-position: 0 0, 0 0, -1px -1px, -1px -1px;
  animation: backgroundShift 30s linear infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes backgroundShift {
  0% {
    background-position: 0 0, 0 0, -1px -1px, -1px -1px;
  }
  100% {
    background-position: 100px 100px, -100px -100px, -1px -1px, -1px -1px;
  }
}

.header {
  position: sticky;
  top: 0;
  z-index: 10;
  margin-bottom: 8px;
  padding: 1px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.05);
}

.title {
  font-size: 32px;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, #2c7a7b, #2b6cb0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: left;
}

.headerSettings {
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
}

.cardsContainer {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  padding: 0 10px;
  margin-bottom: 32px;
}

.card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: rgba(44, 122, 123, 0.5);
  box-shadow: 
    0 0 20px rgba(43, 108, 176, 0.2),
    0 0 40px rgba(44, 122, 123, 0.1);
}

.card::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 12px;
  padding: 1px;
  background: linear-gradient(
    135deg,
    rgba(44, 122, 123, 0.5),
    rgba(43, 108, 176, 0.5)
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::after {
  opacity: 1;
}

.cardContent {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.statusDot.active {
  background: #52c41a;
  box-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
}

.statusDot.inactive {
  background: #faad14;
  box-shadow: 0 0 10px rgba(250, 173, 20, 0.5);
}

.cardTitle {
  font-size: 18px;
  font-weight: 500;
  color: #2c7a7b;
  margin-bottom: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cardDescription {
  color: rgba(0, 0, 0, 0.7);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.cardFooter {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.cardStats {
  display: flex;
  gap: 16px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  flex-wrap: wrap;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loadingSpinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.loadingSpinner::before,
.loadingSpinner::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  animation: pulse 2s ease-out infinite;
}

.loadingSpinner::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid rgba(33, 150, 243, 0.5);
}

.loadingSpinner::after {
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid rgba(33, 150, 243, 0.3);
  animation-delay: 0.5s;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  50% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

.paginationContainer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
}

:global(.ant-pagination-item) {
  background: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(0, 0, 0, 0.1) !important;
}

:global(.ant-pagination-item a) {
  color: rgba(0, 0, 0, 0.8) !important;
}

:global(.ant-pagination-item-active) {
  background: rgba(44, 122, 123, 0.1) !important;
  border-color: #2c7a7b !important;
}

:global(.ant-pagination-item:hover) {
  border-color: #2c7a7b !important;
  background: rgba(44, 122, 123, 0.05) !important;
}

:global(.ant-select-selector) {
  background: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(0, 0, 0, 0.1) !important;
}

:global(.ant-select-selection-item) {
  color: rgba(0, 0, 0, 0.8) !important;
}

.tooltip {
  max-width: 300px;
}

/* 为了适应固定的分页器，添加底部padding */
.cardsContainer {
  padding-bottom: 80px;
}