import React, { useState, useEffect } from 'react';
import { Card, Pagination, Empty, Typography, Toolt<PERSON>, Badge, Tag } from 'antd';
import { useNavigate } from 'react-router-dom';
import { 
  ThunderboltOutlined,
  RocketOutlined,
  ClockCircleOutlined,
  UserOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  ApiOutlined,
  NodeIndexOutlined
} from '@ant-design/icons';
import { fetchBulk } from '../api/api';
import { WORKFLOW_ENDPOINT } from '../../Constant/RouterConstant';
import { CHAT_API_NAME } from '../../Constant/Constant';
import styles from './Workflows.module.css';
import HeaderSettings from '../common/HeaderSettings';
import { useSelector } from 'react-redux';
interface WorkflowExample {
  text: string;
  conversable: boolean;
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_published: boolean;
  nodes: string;
  edges: string;
  database: string | null;
  domain: string | null;
  created_at: string;
  updated_at: string;
  username: string;
  examples: WorkflowExample[];
  url: string | null;
}

// interface WorkflowsProps {
//   username?: string;
// }

const { Text } = Typography;

const Workflows: React.FC = () => {
  const navigate = useNavigate();
  const { userInfo } = useSelector((state: any) => state.user);
  const username = userInfo.username; // 固定用户名
  // console.log('username', username);
  // 状态管理
  const [data, setData] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12); // 默认每页12条
  const [totalItems, setTotalItems] = useState(0);
  const [sortField] = useState('updated_at');
  const [sortOrder] = useState('desc');

  // 格式化日期时间
  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 获取工作流数据
  const fetchWorkflows = async () => {
    setLoading(true);
    try {
      const skip = (page - 1) * pageSize;
      const limit = pageSize;
      const endpoint = `${WORKFLOW_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${username}&is_published=false`;
      
      const result = await fetchBulk(endpoint);
      setData(result.data);
      setTotalItems(result.count);
      console.log('工作流数据:', result.data);
    } catch (error) {
      console.error('获取工作流数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载和分页变化时获取数据
  useEffect(() => {
    fetchWorkflows();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize, sortField, sortOrder]);

  // 处理分页变化
  const handlePageChange = (newPage: number, newPageSize?: number) => {
    // 如果页面大小改变，重置到第一页
    if (newPageSize && newPageSize !== pageSize) {
      setPage(1);
      setPageSize(newPageSize);
    } else {
      setPage(newPage);
    }
  };

  // 获取描述的前40个字符
  // const getShortDescription = (description: string) => {
  //   if (!description) return '';
  //   // 不再限制字符数，由CSS控制显示
  //   return description;
  // };

  // 处理卡片点击
  const handleCardClick = (workflow: Workflow) => {
    // 使用新的路由结构直接导航到聊天页面，创建新对话，添加参数标识为workflow类型
    navigate(`${CHAT_API_NAME}/${workflow.id}/new?workflow_or_agent=workflow`);
  };

  const getStatusColor = (workflow: Workflow) => {
    if (!workflow.is_published) return 'default';
    return workflow.is_active ? 'success' : 'warning';
  };

  const getStatusText = (workflow: Workflow) => {
    if (!workflow.is_published) return '未发布';
    return workflow.is_active ? '运行中' : '已暂停';
  };

  const getWorkflowIcon = (workflow: Workflow) => {
    if (workflow.database) return <DatabaseOutlined />;
    if (workflow.url) return <ApiOutlined />;
    return <NodeIndexOutlined />;
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <h1 className={styles.title}>
            <RocketOutlined style={{ marginRight: 12 }} />
            智能工作流
          </h1>
          <Text style={{ color: 'rgba(0, 0, 0, 0.65)' }}>
            探索和管理您的自动化工作流程，释放AI的无限潜能
          </Text>
        </div>
        <div className={styles.headerSettings}>
          <HeaderSettings />
        </div>
      </div>

      {loading ? (
        <div className={styles.loading}>
          <div className={styles.loadingSpinner} />
        </div>
      ) : data.length === 0 ? (
        <Empty 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <Text style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
              暂无工作流数据
            </Text>
          }
        />
      ) : (
        <>
          <div className={styles.cardsContainer}>
            {data.map((workflow) => (
              <Card 
                key={workflow.id} 
                className={styles.card} 
                hoverable
                onClick={() => handleCardClick(workflow)}
              >
                <div className={styles.cardContent}>
                  <div className={styles.cardHeader}>
                    <Badge 
                      status={getStatusColor(workflow)} 
                      text={
                        <Text style={{ color: 'rgba(0, 0, 0, 0.85)' }}>
                          {getStatusText(workflow)}
                        </Text>
                      }
                    />
                    <ThunderboltOutlined style={{ 
                      color: '#2c7a7b', 
                      fontSize: 20,
                      marginLeft: 'auto',
                      animation: workflow.is_active ? 'pulse 2s infinite' : 'none'
                    }} />
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 12 }}>
                    {getWorkflowIcon(workflow)}
                    <Tooltip title={workflow.name}>
                      <div className={styles.cardTitle}>{workflow.name}</div>
                    </Tooltip>
                  </div>

                  <Tooltip title={workflow.description}>
                    <div className={styles.cardDescription}>
                      {workflow.description}
                    </div>
                  </Tooltip>

                  <div className={styles.cardFooter}>
                    <div className={styles.cardStats}>
                      <span>
                        <ClockCircleOutlined style={{ marginRight: 4 }} />
                        {formatDateTime(workflow.updated_at)}
                      </span>
                      <span>
                        <UserOutlined style={{ marginRight: 4 }} />
                        {workflow.username}
                      </span>
                      {workflow.database && (
                        <Tag color="cyan">
                          <DatabaseOutlined style={{ marginRight: 4 }} />
                          {workflow.database}
                        </Tag>
                      )}
                      {workflow.domain && (
                        <Tag color="blue">
                          <GlobalOutlined style={{ marginRight: 4 }} />
                          {workflow.domain}
                        </Tag>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className={styles.paginationContainer}>
            <Pagination
              current={page}
              pageSize={pageSize}
              total={totalItems}
              onChange={handlePageChange}
              onShowSizeChange={handlePageChange}
              showSizeChanger
              pageSizeOptions={['12', '24', '36', '48']}
              showQuickJumper
              showTotal={(total) => `共 ${total} 条记录`}
              hideOnSinglePage
            />
          </div>
        </>
      )}
    </div>
  );
};

export default Workflows; 