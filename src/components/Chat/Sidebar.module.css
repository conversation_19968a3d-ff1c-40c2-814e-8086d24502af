.sidebar {
  width: 240px;
  min-width: 240px;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(0, 120, 255, 0.15);
  background: linear-gradient(180deg, #001529, #002140);
  position: relative;
  transition: all 0.3s cubic-bezier(0.2, 0, 0, 1);
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 80px;
  min-width: 80px;
}

/* 侧边栏标题 */
.sidebarHeader {
  padding: 20px 20px 12px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-bottom: 1px solid rgba(0, 120, 255, 0.15);
  background: linear-gradient(90deg, rgba(0, 21, 41, 0.8), rgba(0, 33, 64, 0.9));
  flex-wrap: wrap;
  gap: 8px;
}

.sidebarTitle {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  background: linear-gradient(45deg, #1890ff, #69c0ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

/* 工作流/智能体名称标签 */
.workflowAgentLabel {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85);
  background: rgba(24, 144, 255, 0.15);
  padding: 2px 8px;
  border-radius: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
  border: 1px solid rgba(24, 144, 255, 0.3);
  cursor: default;
  transition: all 0.3s;
}

.workflowAgentLabel:hover {
  background: rgba(24, 144, 255, 0.25);
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);
}

/* 侧边栏选项卡区域 */
.sidebarTabs {
  display: flex;
  flex-direction: column;
  padding: 16px 0;
  padding-bottom: 60px; /* 为底部固定元素留出空间 */
  overflow-y: auto;
  flex: 1;
}

/* 选项卡项目 */
.tabItem {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.2, 0, 0, 1);
  font-size: 16px;
  color: rgba(255, 255, 255, 0.65);
  border-radius: 6px;
  margin: 4px 8px;
  position: relative;
  overflow: hidden;
}

.tabItem::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 0;
  background: linear-gradient(0deg, #1890ff, #69c0ff);
  transition: height 0.3s ease;
}

.tabItem:hover::before,
.tabItem.active::before {
  height: 100%;
}

.sidebar.collapsed .tabItem {
  justify-content: center;
  padding: 12px 0;
  margin: 4px;
}

.tabItem:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.tabItem.active {
  background: rgba(24, 144, 255, 0.15);
  color: #1890ff;
  box-shadow: 0 2px 12px rgba(24, 144, 255, 0.2);
}

/* 选项卡文本 */
.tabText {
  margin-left: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s;
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.2);
}

.buttonText {
  transition: opacity 0.2s;
  color: rgba(255, 255, 255, 0.85);
}

/* 选项卡内容包装器 */
.tabContentWrapper {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

/* 选项卡内容 */
.tabContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

/* 新增按钮 */
.addBtn {
  margin: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 对话列表 */
.conversations {
  flex: 1;
  overflow: auto;
}

/* 底部设置区域 */
.sidebarFooter {
  border-top: 1px solid rgba(0, 120, 255, 0.15);
  padding: 16px 0;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0, 21, 41, 0.95), rgba(0, 33, 64, 0.85));
  z-index: 5;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* 设置按钮 */
.settingsButton {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.65);
}

.footerSettings {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.sidebar.collapsed .footerSettings {
  justify-content: center;
}

/* 占位符提示 */
.tabPlaceholder {
  text-align: center;
  color: rgba(255, 255, 255, 0.45);
  padding: 20px;
}

/* 历史会话视图样式 */
.historyHeader {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 120, 255, 0.15);
  background: linear-gradient(90deg, rgba(0, 21, 41, 0.8), rgba(0, 33, 64, 0.9));
}

.headerTitleWrapper {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85);
}

.headerIcon {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.headerTitleText {
  font-size: 16px;
  font-weight: 500;
}

.backButton {
  color: rgba(255, 255, 255, 0.75);
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
}

.backButton:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.historyContent {
  flex: 1;
  overflow: auto;
  padding: 8px 0;
  padding-bottom: 60px; /* 为底部固定元素留出空间 */
  display: flex;
  flex-direction: column;
}

.historyConversations {
  width: 100%;
  flex: 1;
  overflow: auto;
}

.historyAddBtn {
  margin: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s;
  background: rgba(24, 144, 255, 0.15);
  color: #1890ff;
  border: 1px solid rgba(24, 144, 255, 0.25);
}

.historyAddBtn:hover {
  background: rgba(24, 144, 255, 0.25);
  box-shadow: 0 0 12px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.sidebar.collapsed .historyAddBtn {
  justify-content: center;
  padding: 4px 8px;
  margin: 16px 8px;
}

/* 折叠按钮 */
.collapseButton {
  position: absolute;
  bottom: 70px;
  right: -12px;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  border: 2px solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
  transition: all 0.3s;
  color: white;
}

.collapseButton:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(24, 144, 255, 0.7);
}

/* 历史会话视图的按钮样式调整 */
.sidebar.collapsed .historyHeader {
  padding: 16px 8px;
  justify-content: center;
}

/* 调整折叠模式下部分元素的大小和间距 */
.sidebar.collapsed .historyContent {
  padding: 8px 4px;
}

/* 图标样式，确保大小统一 */
.tabItem svg {
  min-width: 22px;
  min-height: 22px;
  transition: transform 0.3s;
  filter: drop-shadow(0 0 4px rgba(24, 144, 255, 0.2));
}

.tabItem:hover svg {
  transform: scale(1.1);
  filter: drop-shadow(0 0 6px rgba(24, 144, 255, 0.4));
}

/* 添加科技感的炫光效果 */
.tabItem::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: -100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(24, 144, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.tabItem:hover::after {
  left: 100%;
}

/* 添加图标辉光效果 */
.sidebar .tabItem svg.icon-glow {
  filter: drop-shadow(0 0 4px rgba(24, 144, 255, 0.5));
  transition: all 0.3s ease;
}

.sidebar .tabItem:hover svg.icon-glow {
  filter: drop-shadow(0 0 8px rgba(24, 144, 255, 0.8));
  transform: scale(1.1);
}

.sidebar .tabItem.active svg.icon-glow {
  filter: drop-shadow(0 0 6px rgba(24, 144, 255, 0.7));
}

/* 添加辉光脉动动画 */
@keyframes pulse {
  0% {
    filter: drop-shadow(0 0 4px rgba(24, 144, 255, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(24, 144, 255, 0.8));
  }
  100% {
    filter: drop-shadow(0 0 4px rgba(24, 144, 255, 0.5));
  }
}

.sidebar .collapseButton svg.icon-glow {
  animation: pulse 2s infinite ease-in-out;
} 