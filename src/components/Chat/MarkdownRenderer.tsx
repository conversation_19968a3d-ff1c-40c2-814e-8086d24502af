import React, { useState, useEffect, useCallback, useRef } from 'react';
import { marked, Renderer } from 'marked';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css'; // Or your preferred theme
import styles from './MarkdownRenderer.module.css';

// Extend MarkedOptions type (interface merging)
declare module 'marked' {
    interface MarkedOptions {
      highlight?: (code: string, lang: string, callback?: (error: any, code: string) => void) => string | void;
    }
    // Define types for renderer function parameters based on marked documentation/source if needed
    // For simplicity, we'll use inline types where needed.
    // interface ImageDetails { href: string | null; title: string | null; text: string; }
    // interface LinkDetails { href: string | null; title: string | null; text: string; tokens: Tokens.Link['tokens']; }
}

interface MarkdownRendererProps {
  content: string;
  isStreaming?: boolean;
  onHtmlSvgCodeDetected?: (codeBlocks: {id: string, type: 'html' | 'svg', content: string}[], isUserClick?: boolean) => void;
  role?: 'local' | 'ai'; // 添加角色属性来区分用户和AI消息
}

interface TextSegment {
  type: 'regular' | 'custom_tag';
  content: string;
  tagInfo?: {
    tag: string;
    className: string;
    id: string;
    attributes?: string;
  };
}

// 辅助函数：转义正则表达式中的特殊字符
function escapeRegExp(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 修改processHtmlBlocks函数
const processHtmlBlocks = (code: string): { type: 'html' | 'text'; content: string }[] => {
  const blocks: { type: 'html' | 'text'; content: string }[] = [];
  let currentBlock = '';
  let inHtmlBlock = false;
  
  const lines = code.split('\n');
  let textContent = '';
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测HTML块的开始
    if (line.trim().toLowerCase().startsWith('<!doctype html') || 
        line.trim().toLowerCase().startsWith('<html')) {
      // 如果之前有文本内容，先保存
      if (textContent.trim()) {
        blocks.push({ type: 'text', content: textContent.trim() });
        textContent = '';
      }
      
      inHtmlBlock = true;
      currentBlock = line;
      
      // 继续读取直到找到HTML结束标签
      while (i < lines.length) {
        i++;
        if (i >= lines.length) break;
        
        currentBlock += '\n' + lines[i];
        if (lines[i].trim().toLowerCase().includes('</html>')) {
          blocks.push({ type: 'html', content: currentBlock });
          currentBlock = '';
          inHtmlBlock = false;
          break;
        }
      }
    } else if (!inHtmlBlock) {
      // 收集非HTML块的内容
      textContent += line + '\n';
    }
  }
  
  // 处理最后的文本内容
  if (textContent.trim()) {
    blocks.push({ type: 'text', content: textContent.trim() });
  }
  
  return blocks;
};

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, isStreaming = false, onHtmlSvgCodeDetected, role = 'ai' }) => {
  // console.log(content,"content");   
  const [renderedContent, setRenderedContent] = useState<string>('');
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({});
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [nodesLoaded, setNodesLoaded] = useState<boolean>(false);
  const nodeTimerRef = useRef<NodeJS.Timeout | null>(null);
  const htmlSvgCodeBlocksRef = useRef<{id: string, type: 'html' | 'svg', content: string}[]>([]);
  // console.log(content,"content.length");
  // --- Custom Marked Renderer ---
  // Use a stable renderer instance, configure only once.
  const customRendererRef = useRef<Renderer | null>(null);

  useEffect(() => {
    // Initialize and configure renderer only once
    if (!customRendererRef.current) {
        const renderer = new Renderer();
        customRendererRef.current = renderer;

        // --- Image Handling ---
        // Override image renderer
        renderer.image = ({ href, title, text }: { href: string | null; title: string | null; text: string }): string => {
            // Ensure href is a string before proceeding
            if (typeof href !== 'string') {
                console.warn('Image href is not a string or is null:', href);
                return text || ''; // Return alt text if href is invalid
            }

            // Handle non-http(s) links as plain text/paths or local references
            if (!href.startsWith('http://') && !href.startsWith('https://')) {
                // If it looks like a local file path, render differently
                if (href.includes('/') || href.includes('\\')) {
                   return `<span class="${styles.localPath}" title="${text || href}">${href}</span>`;
                }
                // Otherwise, might be just alt text if src is missing, treat as text
                return text || href;
            }

            // Render standard images that will be enhanced later for click/preview
            const id = `img-${Math.random().toString(36).substring(2, 9)}`;
            // Add data attributes directly for the event listener
            return `<span class="${styles.imageWrapper}" data-img-src="${href}" data-img-id="${id}">
                <img src="${href}" class="${styles.thumbnailImage}" alt="${text || title || 'image'}" title="${title || text || href}" data-full-img="${href}" data-id="${id}" />
            </span>`;
        };

        // --- Link Handling ---
        // Removed custom link renderer override. Default marked link rendering will be used.
        // `enhanceHtml` will process the resulting <a> tags later.

        // --- Configure Marked ---
        marked.setOptions({
            renderer: renderer,
            highlight: (code, lang) => {
            const language = hljs.getLanguage(lang) ? lang : 'plaintext';
            try {
                return hljs.highlight(code, { language, ignoreIllegals: true }).value;
            } catch (error) {
                console.error(`Highlighting error for language ${lang}:`, error);
                return hljs.highlight(code, { language: 'plaintext', ignoreIllegals: true }).value; // Safer fallback
            }
            },
            breaks: true,
            gfm: true,
            // pedantic: false, // Generally not needed unless strict compliance is required
            // sanitize: false // IMPORTANT: Ensure content is trusted or sanitize externally (e.g., DOMPurify)
        });
    }
  }, []); // Empty dependency array ensures this runs only once on mount

  // --- Content Processing ---

  const toggleCollapse = useCallback((id: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  }, []);

  const separateContent = useCallback((text: string): TextSegment[] => {
    const segments: TextSegment[] = [];
    
    // Define patterns directly or ensure styles are stable if coming from props/context
    const customTagPatterns = [
      // 先处理 NODE 标签，因为它可能包含其他标签
      { tag: 'node', regex: /<NODE(?:\s+([^>]*))?>([\s\S]*?)<\/NODE>/g, className: styles.node },
      // 处理 NAME 标签
      // { tag: 'name', regex: /<NAME>([\s\S]*?)<\/NAME>/g, className: styles.name },
      // 处理其他标签
      { tag: 'think', regex: /<think>([\s\S]*?)<\/think>/g, className: styles.think },
      { tag: 'tool_call', regex: /<tool_call>([\s\S]*?)<\/tool_call>/g, className: styles.toolCall },
      { tag: 'observation', regex: /<observation>([\s\S]*?)<\/observation>/g, className: styles.observation }
    ];

    type Match = { 
      tag: string; 
      className: string; 
      startIndex: number; 
      endIndex: number; 
      content: string;
      attributes?: string;
    };
    const allMatches: Match[] = [];

    // 用于跟踪已处理的区域，避免重复处理
    const processedRanges: {start: number, end: number}[] = [];
    
    // 检查某个范围是否已被处理过
    const isRangeProcessed = (start: number, end: number) => {
      return processedRanges.some(range => 
        (start >= range.start && start < range.end) || // 开始位置在已处理范围内
        (end > range.start && end <= range.end) || // 结束位置在已处理范围内
        (start <= range.start && end >= range.end) // 范围完全包含已处理范围
      );
    };

    customTagPatterns.forEach(({ tag, regex, className }) => {
      // 确保regex有'g'标志以进行多次匹配
      const globalRegex = new RegExp(regex.source, regex.flags.includes('g') ? regex.flags : regex.flags + 'g');
      let match;
      while ((match = globalRegex.exec(text)) !== null) {
        const startIndex = match.index;
        const endIndex = globalRegex.lastIndex;
        
        // 如果这个区域已经被处理过，跳过
        if (isRangeProcessed(startIndex, endIndex)) {
          continue;
        }
        
        // 增强NODE标签的处理 - 提前识别和处理agent_name
        let content = tag === 'node' ? match[2] : match[1];
        let attributes = tag === 'node' ? match[1] : undefined;
        
        // 对于NODE标签，预处理内容
        if (tag.toLowerCase() === 'node') {
          // 尝试从内容中提取agent_name
          let agentName = '';
          
          // 从NAME标签提取
          const nameRegex = /<NAME>([\s\S]*?)<\/NAME>/i;
          const nameMatch = nameRegex.exec(content);
          if (nameMatch && nameMatch[1]) {
            agentName = nameMatch[1].trim();
            // 从内容中移除NAME标签
            content = content.replace(nameRegex, '');
          }
          // 或者从NODE属性提取
          else if (attributes) {
            agentName = attributes.trim();
          }
          
          // 如果找到了agentName，从内容的开头移除
          if (agentName) {
            // 移除开头的agent_name（如果它独自成为一行或者是开头）
            const firstLineRegex = new RegExp(`^\\s*${escapeRegExp(agentName)}\\s*[\\n\\r]`, 'i');
            content = content.replace(firstLineRegex, '');
            
            // 如果agent_name单独成为一行，也需要移除
            const singleLineRegex = new RegExp(`^\\s*${escapeRegExp(agentName)}\\s*$`, 'im');
            content = content.replace(singleLineRegex, '');
            
            // 保存agentName到属性中，而不是放在内容里
            attributes = agentName;
          }
        }
        
        // 添加匹配并标记该区域为已处理
        allMatches.push({ tag, className, startIndex, endIndex, content, attributes });
        processedRanges.push({start: startIndex, end: endIndex});
      }
    });

    allMatches.sort((a, b) => a.startIndex - b.startIndex);

    let lastIndex = 0;
    // 使用简单索引为此渲染过程中的唯一ID
    let customTagCounter = 0;

    allMatches.forEach((match) => {
      if (match.startIndex > lastIndex) {
        const regularContent = text.substring(lastIndex, match.startIndex);
        if (regularContent.trim()) {
          segments.push({ type: 'regular', content: regularContent });
        }
      }
      const id = `${match.tag}-${customTagCounter++}`; // 生成唯一ID
      segments.push({ 
        type: 'custom_tag', 
        content: match.content, 
        tagInfo: { 
          tag: match.tag, 
          className: match.className, 
          id,
          attributes: match.attributes 
        } 
      });
      lastIndex = match.endIndex;
    });

    if (lastIndex < text.length) {
      const regularContent = text.substring(lastIndex);
      if (regularContent.trim()) {
        segments.push({ type: 'regular', content: regularContent });
      }
    }
    return segments;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [styles.toolCall, styles.observation, styles.think, styles.node]); // 依赖于使用的特定样式类

  // Helper function to create file link DOM element (kept separate for clarity)
  // Needs to be stable or wrapped in useCallback if `enhanceHtml` depends on it.
  const createFileLinkElement = useCallback((doc: Document, fileUrl: string, fileName: string, fileExt: string): HTMLElement => {
    const container = doc.createElement('div');
    container.className = styles.fileLink;
    container.setAttribute('data-file-url', fileUrl);
    container.setAttribute('data-file-name', fileName);
    container.style.cursor = 'pointer'; // Indicate it's clickable for download

    let iconSvg = '';
    let fileType = 'File'; // Default
    let iconColor = '#607D8B'; // Default Grey

    const lowerExt = fileExt.toLowerCase();

    // Image types: Render as clickable thumbnail (uses imageWrapper style)
    if (['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'].includes(lowerExt)) {
       const img = doc.createElement('img');
       img.src = fileUrl;
       img.className = styles.thumbnailImage;
       img.alt = fileName;
       img.setAttribute('data-full-img', fileUrl); // For preview modal
       img.setAttribute('data-id', `file-img-${Math.random().toString(36).substring(2, 9)}`);

       const wrapper = doc.createElement('div');
       wrapper.className = styles.imageWrapper; // Use imageWrapper for consistency
       wrapper.appendChild(img);
       // Add file data attributes to the wrapper for the download click listener
       wrapper.setAttribute('data-file-url', fileUrl);
       wrapper.setAttribute('data-file-name', fileName);
       wrapper.style.display = 'inline-block';
       wrapper.style.cursor = 'pointer'; // Cursor for the wrapper too
       // Note: The event listener logic needs to differentiate between click for preview (on img)
       // and click for download (potentially on the wrapper or a dedicated button).
       // For simplicity, let's assume clicking the wrapper downloads, clicking the image previews.
       // OR, the current setup might have both wrapper and image trigger preview,
       // and the fileLink handler triggers download if it's not an image type. Let's stick to that.
       // ** Correction: Let image click always preview. File links are for non-image downloads. **
       // We need to ensure the `enhanceHtml` doesn't try to turn image links into file icons.

        // Revisit: If an image URL is linked via `@http` or `<a>`, should it be a download link or an image preview?
        // Current logic: Marked's image syntax `![]()` creates previewable images via `renderer.image`.
        // Regular links `[]()` or `@http...` to image files will be processed by `enhanceHtml`.
        // Let's make `createFileLinkElement` render a generic file icon even for images if called from `enhanceHtml`'s link processing.
        // The `renderer.image` handles the previewable images separately.

        // *** Revised Decision: Keep image rendering separate. `createFileLinkElement` is for NON-IMAGE files. ***
         fileType = 'Image File'; // Type for icon purposes
         iconColor = '#4CAF50'; // Greenish for generic image file
    } else if (lowerExt === 'pdf') {
        fileType = 'PDF Document';
        iconColor = '#FF5722'; // Orange/Red for PDF
    } else if (['doc', 'docx'].includes(lowerExt)) {
        fileType = 'Word Document';
        iconColor = '#2196F3'; // Blue for Word
    } else if (['xls', 'xlsx'].includes(lowerExt)) {
        fileType = 'Excel Spreadsheet';
        iconColor = '#4CAF50'; // Green for Excel
    } else if (['ppt', 'pptx'].includes(lowerExt)) {
        fileType = 'PowerPoint Presentation';
        iconColor = '#FF9800'; // Orange for PowerPoint
    } else if (['txt', 'json', 'xml', 'csv', 'log', 'yaml', 'yml'].includes(lowerExt)) {
        fileType = 'Text/Data File';
        iconColor = '#607D8B'; // Grey for Text/Data
    } else if (['zip', 'rar', 'tar', 'gz'].includes(lowerExt)) {
        fileType = 'Archive File';
        iconColor = '#9C27B0'; // Purple for Archives
    } else {
        fileType = `${fileExt.toUpperCase()} File`; // Generic fallback
    }

    // Generic file icon SVG
    iconSvg = `
      <svg class="${styles.fileIcon}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z" stroke="${iconColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M14 2V8H20" stroke="${iconColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        ${ ['txt', 'json', 'xml', 'csv', 'log', 'yaml', 'yml'].includes(lowerExt) ? '<path d="M16 13H8 M16 17H8 M10 9H8" stroke="'+iconColor+'" stroke-width="2" stroke-linecap="round"/>' : '' }
        ${ lowerExt === 'pdf' ? '<path d="M9 15V11 M12 15V11 M15 15V11 M9 11C9 10.4477 9.44772 10 10 10H14C14.5523 10 15 10.4477 15 11" stroke="'+iconColor+'" stroke-width="1.5" stroke-linecap="round"/>' : '' }
      </svg>`;

    const fileInfo = doc.createElement('div');
    fileInfo.className = styles.fileInfo;

    const nameDiv = doc.createElement('div');
    nameDiv.className = styles.fileName;
    nameDiv.textContent = fileName;

    const typeDiv = doc.createElement('div');
    typeDiv.className = styles.fileType;
    typeDiv.textContent = fileType;

    fileInfo.appendChild(nameDiv);
    fileInfo.appendChild(typeDiv);

    container.innerHTML = iconSvg; // Set SVG first
    container.appendChild(fileInfo); // Append info div

    return container;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [styles.fileLink, styles.fileIcon, styles.fileInfo, styles.fileName, styles.fileType]); // Depend on specific style classes used

  // Function to enhance HTML: Add copy buttons, process file links
  const enhanceHtml = useCallback((rawHtml: string): string => {
    if (!rawHtml) return '';
    
    const parser = new DOMParser();
    const doc = parser.parseFromString(rawHtml, 'text/html');
    const body = doc.body;

    // 处理代码块
    const codeBlocks = body.querySelectorAll('pre > code');
    codeBlocks.forEach((codeElement, index) => {
      const preElement = codeElement.parentElement as HTMLPreElement | null;
      if (!preElement) return;

      const codeText = codeElement.textContent || '';
      const langClass = Array.from(codeElement.classList).find(cls => cls.startsWith('language-'));
      const language = langClass ? langClass.replace('language-', '') : 'text';

      // 检测HTML代码块
      const isHtmlCode = language.startsWith('html') || language === 'markup' || 
        (language === 'text' && (
          codeText.trim().toLowerCase().startsWith('<!doctype html') ||
          codeText.trim().toLowerCase().startsWith('<html') ||
          codeText.includes('<div') || codeText.includes('<text')
        ));

      if (isHtmlCode && !isStreaming) {
        // 处理所有内容块
        const contentBlocks = processHtmlBlocks(codeText);
        
        if (contentBlocks.length > 0) {
          const container = doc.createElement('div');
          container.className = styles.codeBlockContainer;
          
          contentBlocks.forEach((block, blockIndex) => {
            if (block.type === 'html') {
              // 创建HTML代码块卡片
              const id = `html-block-${index}-${blockIndex}-${Math.random().toString(36).substring(2, 9)}`;
              const cardContainer = doc.createElement('div');
              cardContainer.className = `${styles.codeBlockCard} html-svg-code-block`;
              cardContainer.setAttribute('data-code-type', 'html');
              cardContainer.setAttribute('data-code-content', block.content);
              cardContainer.setAttribute('data-code-id', id);
              
              // 将HTML代码块添加到htmlSvgCodeBlocks数组，以便process函数可以通知父组件
              htmlSvgCodeBlocksRef.current.push({
                id,
                type: 'html',
                content: block.content
              });
              
              // 创建卡片内容
              const cardIcon = doc.createElement('div');
              cardIcon.className = styles.codeBlockCardIcon;
              cardIcon.innerHTML = `<svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none">
                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
              </svg>`;
              
              const cardInfo = doc.createElement('div');
              cardInfo.className = styles.codeBlockCardInfo;
              
              const cardTitle = doc.createElement('div');
              cardTitle.className = styles.codeBlockCardTitle;
              cardTitle.textContent = `HTML代码块 ${blockIndex + 1}`;
              
              const cardDesc = doc.createElement('div');
              cardDesc.className = styles.codeBlockCardDesc;
              cardDesc.textContent = '点击查看HTML内容';
              
              cardInfo.appendChild(cardTitle);
              cardInfo.appendChild(cardDesc);
              
              cardContainer.appendChild(cardIcon);
              cardContainer.appendChild(cardInfo);
              
              container.appendChild(cardContainer);
            } else {
              // 创建普通文本内容
              const textContainer = doc.createElement('div');
              textContainer.className = styles.codeBlockText;
              
              // 使用marked处理文本内容中的markdown
              try {
                const textContent = marked.parse(block.content) as string;
                textContainer.innerHTML = textContent;
              } catch (error) {
                console.error('Error parsing markdown:', error);
                textContainer.textContent = block.content;
              }
              
              container.appendChild(textContainer);
            }
          });
          
          preElement.parentNode?.replaceChild(container, preElement);
        }
      }
    });

    // 2. Process File Links (<a> tags and specific @http patterns)
    const validFileExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'json', 'xml', 'csv', 'log', 'yaml', 'yml', 'zip', 'rar', 'tar', 'gz', 'png', 'jpg', 'jpeg', 'gif', 'webp', 'svg']; // Added archives, include images here for detection
    const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'];

    // Find all links created by marked
    const links = body.querySelectorAll('a');
    links.forEach(link => {
        const href = link.getAttribute('href');
        if (!href || !href.match(/^https?:\/\//i)) return; // Only process absolute http/https links

        try {
            const url = new URL(href); // Use URL constructor for robust parsing
            const pathName = url.pathname;
            const fileExt = pathName.split('.').pop()?.toLowerCase() || '';

            if (validFileExtensions.includes(fileExt) && !imageExtensions.includes(fileExt)) { // Process only non-image file links
                const fileName = pathName.split('/').pop() || href;
                const fileDiv = createFileLinkElement(doc, href, fileName, fileExt);
                link.parentNode?.replaceChild(fileDiv, link);
            }
             // Else: it's a regular link or an image link, leave it as is (image links handled by renderer.image)
        } catch (e) {
            console.warn("Could not parse URL from link:", href, e); // Ignore invalid URLs
        }
    });

    // Handle standalone @http... patterns (operating on text nodes)
    const walker = doc.createTreeWalker(body, NodeFilter.SHOW_TEXT);
    let node;
    const nodesToModify: { node: Text; replacements: { index: number; length: number; element: HTMLElement }[] }[] = [];

    while (node = walker.nextNode()) {
        const textNode = node as Text;
        const parentElement = textNode.parentElement;
        // Avoid processing text inside script/style/code/links/custom tags/file links etc.
        if (!parentElement || parentElement.closest('script, style, pre, code, a, button, .fileLink, .customTag')) {
           continue;
        }

        const textContent = textNode.nodeValue || '';
        // Regex: @ + http(s):// + non-space chars until space/newline/< or end of string
        // Ensure it captures the extension correctly for filtering
        const linkRegex = /@((https?:\/\/[^\s<>"']+?)(\.([a-zA-Z0-9]+))?)/g;
        let match;
        const currentReplacements: { index: number; length: number; element: HTMLElement }[] = [];

        while ((match = linkRegex.exec(textContent)) !== null) {
            const fullMatch = match[0]; // e.g., @http://example.com/file.pdf
            const url = match[1];      // e.g., http://example.com/file.pdf
            const fileExt = match[4]?.toLowerCase(); // e.g., pdf (from the 4th group)

            if (fileExt && validFileExtensions.includes(fileExt) && !imageExtensions.includes(fileExt)) { // Process only non-image files
                 const fileName = url.split('/').pop()?.replace(/[\?#].*$/, '') || url; // Basic name extraction
                 const fileDiv = createFileLinkElement(doc, url, fileName, fileExt);
                 currentReplacements.push({ index: match.index, length: fullMatch.length, element: fileDiv });
            }
            // Else: If it's an image file or no valid extension, leave the text as is.
        }
        if (currentReplacements.length > 0) {
             nodesToModify.push({ node: textNode, replacements: currentReplacements });
        }
    }

    // Perform replacements on text nodes (end-to-start)
    nodesToModify.reverse().forEach(({ node, replacements }) => {
        let currentNode: Node = node;
        // Sort replacements for the current node from end to start
        replacements.sort((a, b) => b.index - a.index);

        replacements.forEach(({ index, length, element }) => {
            if (currentNode.nodeType === Node.TEXT_NODE) { // Ensure it's still a text node
                const textNode = currentNode as Text;
                const afterText = textNode.splitText(index + length); // Split after the match
                const matchTextNode = textNode; // This node now contains text up to the match end
                matchTextNode.splitText(index); // Split before the match (matchTextNode now contains only the match text)

                // Replace the middle node (the match text) with the element
                matchTextNode.parentNode?.replaceChild(element, matchTextNode);
                currentNode = afterText.previousSibling!; // Update reference for next replacement in the same original node
            }
        });
    });

    // Return the modified HTML
    return body.innerHTML;

  }, [isStreaming, styles, createFileLinkElement]); // 添加 isStreaming 到依赖项


  const renderSegment = useCallback((segment: TextSegment): string => {
      if (segment.type === 'regular') {
          try {
            const rawHtml = marked.parse(segment.content) as string;
            const enhancedHtml = enhanceHtml(rawHtml);
            return enhancedHtml;
          } catch (error) {
              console.error("Error rendering regular content:", error);
              return `<p>Error rendering content.</p>`;
          }

      } else if (segment.type === 'custom_tag' && segment.tagInfo) {
          const { tag, className, id, attributes } = segment.tagInfo;
          const isCollapsed = collapsedSections[id] ?? false;

          // 检查是否为知识库类型
          const isKnowledgeBase = tag.toLowerCase() === 'node' && 
              attributes && attributes.toLowerCase().includes('knowledge_base');
          
          // 根据是否为知识库类型决定使用什么样式
          const tagClassName = isKnowledgeBase ? 'knowledge_base' : className;
          const tagTitle = isKnowledgeBase ? 'NODE knowledge_base' : (
              tag.toLowerCase() === 'node' && attributes ? attributes : tag.toUpperCase()
          );
          
          // 处理内容，支持嵌套标签
          let parsedContentHtml = '';
          try {
            // 递归处理嵌套标签
            const nestedSegments = separateContent(segment.content);
            parsedContentHtml = nestedSegments.map(renderSegment).join('');
          } catch (error) {
            console.error(`Error rendering content for tag ${tag}:`, error);
            parsedContentHtml = `<p>Error rendering content within tag.</p>`;
          }

          // 如果是知识库类型，使用details元素渲染，与Chat.module.css样式匹配
          if (isKnowledgeBase) {
              return `
                <details class="${tagClassName}" id="${id}" data-type="knowledge_base" ${!isCollapsed ? 'open' : ''}>
                  <summary>${tagTitle}</summary>
                  <div class="content">
                    ${parsedContentHtml}
                  </div>
                </details>
              `;
          }

          // 对于其他类型，使用原来的样式
          return `
            <div class="${styles.customTag} ${className}" data-tag-id="${id}">
              <div class="${styles.tagHeader}" data-tag="${tag}" data-id="${id}" role="button" tabindex="0" aria-expanded="${!isCollapsed}" aria-controls="content-${id}">
                <span class="${styles.tagIcon} ${isCollapsed ? styles.collapsedIcon : ''}"></span>
                <span class="${styles.tagName}">${tagTitle}</span>
              </div>
              <div
                id="content-${id}"
                class="${styles.tagContent} ${isCollapsed ? styles.collapsed : ''}"
                data-id="${id}"
                ${isCollapsed ? 'hidden' : ''}
              >
                ${parsedContentHtml}
              </div>
            </div>`;
      }
      return '';
  }, [collapsedSections, enhanceHtml, separateContent, styles]);

  // 渲染前处理完整内容
  useEffect(() => {
    const process = async () => {
      try {
        if (!content) {
          setRenderedContent('');
          return;
        }

        // 保存HTML和SVG代码块信息
        htmlSvgCodeBlocksRef.current = [];
          
        // 处理标签和内容分离
        const segments = separateContent(content);
        const renderedSegments = segments.map(renderSegment);
        
        // 组合渲染后的内容
        let html = renderedSegments.join('');
        
        // 用户消息添加特殊的包装器和样式处理
        if (role === 'local') {
          // 包装普通文本段落，使其正确右对齐
          html = html.replace(
            /(<p(?![^>]*class=))((?:[^>]*?))>/g, 
            '<p$2 class="userParagraph">'
          );
        }
        
        // 延迟设置以便DOM完全准备好进行事件绑定
        setRenderedContent(html);
        
        // 使用setTimeout确保DOM完全渲染后再通知父组件
        setTimeout(() => {
          // 如果有检测到HTML或SVG代码块，通知父组件
          if (htmlSvgCodeBlocksRef.current.length > 0 && onHtmlSvgCodeDetected) {
            console.log('检测到HTML/SVG代码块，通知父组件:', htmlSvgCodeBlocksRef.current.length);
            onHtmlSvgCodeDetected(htmlSvgCodeBlocksRef.current);
          }
        }, 0);
      } catch (error) {
        console.error('Error processing markdown content:', error);
        setRenderedContent(`<p>Error rendering content: ${(error as Error).message}</p>`);
      }
    };

    process();
  }, [content, enhanceHtml, onHtmlSvgCodeDetected, renderSegment, separateContent, role]);

  // --- Event Handlers ---

  const copyToClipboard = useCallback((code: string,  buttonElement: HTMLElement) => {
    navigator.clipboard.writeText(code)
      .then(() => {
        // Update button text directly for immediate feedback
        const textSpan = buttonElement.querySelector('span');
        if (textSpan) textSpan.textContent = 'Copied!';
        buttonElement.classList.add(styles.copied); // Optional: Add class for styling

        setTimeout(() => {
           if (textSpan) textSpan.textContent = 'Copy'; // Reset text
           buttonElement.classList.remove(styles.copied); // Remove class
        }, 2000);
      })
      .catch(err => {
        console.error('Failed to copy code: ', err);
        const textSpan = buttonElement.querySelector('span');
         if (textSpan) textSpan.textContent = 'Error';
         setTimeout(() => {
             if (textSpan) textSpan.textContent = 'Copy';
         }, 2000);
      });
  }, [styles.copied]); // Include style class if used

  const downloadFile = useCallback(async (url: string, fileName: string) => {
    try {
      // Use fetch with appropriate headers if needed (e.g., for authentication)
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
      }
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link); // Append to body to ensure click works
      link.click();
      document.body.removeChild(link); // Clean up link element
      URL.revokeObjectURL(link.href); // Clean up blob URL
    } catch (error) {
      console.error('Download failed:', error);
      // Provide user feedback (consider a more sophisticated notification system)
      alert(`Failed to download file: ${fileName}\nError: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, []);

  const closeImagePreview = useCallback(() => {
    setSelectedImage(null);
  }, []);

  // Effect for global ESC key listener for image preview
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && selectedImage) {
        closeImagePreview();
      }
    };
    window.addEventListener('keydown', handleEscKey);
    return () => {
      window.removeEventListener('keydown', handleEscKey);
    };
  }, [selectedImage, closeImagePreview]);

  // Effect for attaching delegated event listeners to the container
  useEffect(() => {
    const currentContainer = containerRef.current;
    if (!currentContainer) return;

    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;

      // 处理HTML/SVG代码卡片点击
      const codeCard = target.closest('.html-svg-code-block');
      if (codeCard instanceof HTMLElement) {
        e.stopPropagation();
        const id = codeCard.getAttribute('data-code-id') || '';
        const type = codeCard.getAttribute('data-code-type') || '';
        const content = codeCard.getAttribute('data-code-content') || '';
        
        if (id && type && content && onHtmlSvgCodeDetected) {
          // 验证代码的完整性
          const isValidContent = type === 'html' 
            ? (content.toLowerCase().includes('<!doctype html') || content.includes('<html') || content.includes('<div') || content.includes('<text')) && (content.includes('</html>') || content.includes('</div>') || content.includes('</text'))
            : type === 'svg' && content.trim().startsWith('<svg') && content.includes('</svg>');
          
          if (isValidContent) {
            // 创建单个代码块并通知父组件
            const codeBlock = {
              id,
              type: type as 'html' | 'svg',
              content
            };
            // 传递true表示这是用户点击触发的，应该切换标签页
            console.log('用户点击了完整的HTML/SVG代码块，通知父组件');
            onHtmlSvgCodeDetected([codeBlock], true);
          } else {
            console.warn('用户点击了不完整的HTML/SVG代码块，忽略此次点击');
          }
        }
        return;
      }

      // 1. Handle Copy Button Click
      const copyButton = target.closest(`.${styles.copyButton}`);
      if (copyButton instanceof HTMLElement) {
        e.stopPropagation(); // Prevent other handlers if copy button clicked
        const code = copyButton.getAttribute('data-code');
        // const buttonId = copyButton.getAttribute('data-id');
        if (code) {
          copyToClipboard(code, copyButton);
        }
        return;
      }

      // 2. Handle Thumbnail Image Click (for preview)
      // Prioritize clicks directly on the image element for preview
      const thumbnailImg = target.closest(`.${styles.thumbnailImage}`);
       if (thumbnailImg instanceof HTMLImageElement) {
            const fullImgSrc = thumbnailImg.getAttribute('data-full-img');
            if (fullImgSrc) {
                e.stopPropagation();
                setSelectedImage(fullImgSrc);
            }
           return; // Handled image preview
       }
       // Allow clicking the wrapper also to trigger preview if needed (optional)
       // const imageWrapper = target.closest(`.${styles.imageWrapper}`);
       // if (imageWrapper) {
       //    const img = imageWrapper.querySelector(`.${styles.thumbnailImage}`) as HTMLImageElement | null;
       //    const fullImgSrc = img?.getAttribute('data-full-img');
       //    if (fullImgSrc) { /* ... setSelectedImage(fullImgSrc) ... */ }
       //    return;
       // }


      // 3. Handle File Link Click (for download)
      // This targets elements created by `createFileLinkElement`
       const fileLink = target.closest(`.${styles.fileLink}`);
      if (fileLink instanceof HTMLElement) {
        e.preventDefault(); // Prevent default if it were somehow still a link
        e.stopPropagation();
        const fileUrl = fileLink.getAttribute('data-file-url');
        const fileName = fileLink.getAttribute('data-file-name') || 'download';
        if (fileUrl) {
          downloadFile(fileUrl, fileName);
        }
        return; // Handled file download
      }

      // 4. Handle Custom Tag Header Click (for collapse/expand)
      const tagHeader = target.closest(`.${styles.tagHeader}`);
      if (tagHeader instanceof HTMLElement) {
        e.stopPropagation();
        const id = tagHeader.getAttribute('data-id');
        if (id) {
          toggleCollapse(id);
        }
        // Also handle Enter/Space key press for accessibility if focus is on the header
        // This requires adding onKeyDown to the header, or handling it here if appropriate.
        return; // Handled collapse toggle
      }

    };

    // Handle keyboard interactions for accessibility (e.g., collapsing tags)
    const handleKeyDown = (e: KeyboardEvent) => {
        const target = e.target as HTMLElement;
        // Check if focused element is a tag header and key is Enter or Space
        const tagHeader = target.closest(`.${styles.tagHeader}`);
        if (tagHeader instanceof HTMLElement && (e.key === 'Enter' || e.key === ' ')) {
             e.preventDefault(); // Prevent scrolling on space
             e.stopPropagation();
             const id = tagHeader.getAttribute('data-id');
             if (id) {
                 toggleCollapse(id);
             }
        }
    };

    currentContainer.addEventListener('click', handleClick);
    currentContainer.addEventListener('keydown', handleKeyDown);

    return () => {
      currentContainer.removeEventListener('click', handleClick);
      currentContainer.removeEventListener('keydown', handleKeyDown);
    };
    // Dependencies should include all functions called within the handlers and styles
  }, [styles, copyToClipboard, downloadFile, toggleCollapse, closeImagePreview, setSelectedImage, onHtmlSvgCodeDetected]);

  useEffect(() => {
    // 清除之前的计时器
    if (nodeTimerRef.current) {
      clearTimeout(nodeTimerRef.current);
    }
    
    // 短暂延迟后标记节点为已加载
    nodeTimerRef.current = setTimeout(() => {
      const nodeElements = document.querySelectorAll(`.${styles.customTag}[data-tag-id^="node-"]`);
      nodeElements.forEach(node => {
        node.classList.add(styles.nodeLoaded);
      });
      setNodesLoaded(true);
      console.log('nodesLoaded', nodesLoaded);
    }, 300); // 300ms延迟，可以根据需要调整
    
    return () => {
      if (nodeTimerRef.current) {
        clearTimeout(nodeTimerRef.current);
      }
    };
  }, [renderedContent, styles.customTag, styles.nodeLoaded]);

  return (
    <div className={`${styles.markdownRenderer} ${role === 'local' ? styles.userContent : styles.aiContent}`} ref={containerRef}>
      {/* Render the processed HTML */}
      <div dangerouslySetInnerHTML={{ __html: renderedContent }} />

      {/* Image Preview Modal */}
      {selectedImage && (
        <div
          className={`${styles.imageOverlay} ${styles.visible}`}
          onClick={closeImagePreview} // Click on overlay closes
          role="dialog"
          aria-modal="true"
          aria-label="Image preview"
        >
          {/* Use standard img tag if next/image optimizations aren't needed or applicable here */}
          <img
            src={selectedImage}
            className={styles.fullSizeImage}
            alt="Full size preview"
            onClick={(e) => e.stopPropagation()} // Prevent overlay click when clicking image
          />
          <button
            className={styles.closeButton}
            onClick={closeImagePreview}
            aria-label="Close image preview"
            title="Close (Esc)"
          >
            {/* Using HTML entity for 'times' symbol */}
            ×
          </button>
        </div>
      )}
    </div>
  );
};

export default MarkdownRenderer;