.markdownRenderer {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell;
    line-height: 1.6;
    color: #333;
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
    padding: 0px;
  }
  
  /* Custom tag containers */
  .customTag {
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    margin: 16px 0;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
  }
  
  .tagHeader {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f6f8fa;
    cursor: pointer;
    transition: background-color 0.2s;
    width: 100%;
    box-sizing: border-box;
  }
  
  .tagHeader:hover {
    background-color: #ebeff3;
  }
  
  .tagIcon {
    position: relative;
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  
  .tagIcon::before {
    content: "▶";
    position: absolute;
    transition: transform 0.2s;
    color: #586069;
  }
  
  .tagIcon.collapsedIcon::before {
    transform: rotate(90deg);
  }
  
  .tagName {
    font-weight: 600;
    color: #2c3e50;
    text-transform: capitalize;
  }
  
  /* Specific tag type styles */
  .toolCall {
    border-color: #2188ff;
  }
  
  .toolCall .tagHeader {
    background-color: #f0f6ff;
  }
  
  .observation {
    border-color: #28a745;
  }
  
  .observation .tagHeader {
    background-color: #f0fff4;
  }
  .node {
    border-color: #416b4b;
  }
  
  .node .tagHeader {
    background-color: #f0fff4;
  }
  .think {
    border-color: #d73a49;
  }
  
  .think .tagHeader {
    background-color: #fff0f0;
  }
  
  /* Tag content area */
  .tagContent {
    padding: 16px;
    background-color: white;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
    overflow: hidden;
    max-height: 100000px; /* Large value to ensure content is visible when not collapsed */
    width: 100%;
    box-sizing: border-box;
  }
  
  .tagContent.collapsed {
    max-height: 0 !important;
    padding-top: 0;
    padding-bottom: 0;
  }
  
  /* Code block styles */
  .codeBlockContainer {
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    margin: 16px 0;
    overflow: hidden;
  }
  
  .codeHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #f6f8fa;
    border-bottom: 1px solid #e1e4e8;
  }
  
  .codeLanguage {
    font-family: monospace;
    color: #6a737d;
    font-size: 0.9em;
  }
  
  .copyButton {
    display: flex;
    align-items: center;
    gap: 6px;
    background: none;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    transition: background 0.2s;
  }
  
  .copyButton:hover {
    background-color: #e9ecef;
  }
  
  .copyIcon {
    display: inline-block;
    vertical-align: middle;
  }
  
  .pre {
    margin: 0;
    padding: 16px;
    overflow-x: auto;
    background-color: white;
  }
  
  .code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 14px;
  }

  /* 图片相关样式 */
  .imageWrapper {
    position: relative;
    display: inline-block;
    margin: 10px 0;
    max-width: 100%;
  }

  .thumbnailImage {
    max-width: 300px;
    max-height: 200px;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s ease;
    object-fit: contain;
    border: 1px solid #e1e4e8;
  }

  .thumbnailImage:hover {
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .imageOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    cursor: zoom-out;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .imageOverlay.visible {
    opacity: 1;
    visibility: visible;
  }

  .fullSizeImage {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    border-radius: 4px;
  }

  .closeButton {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
  }

  .closeButton:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }

  /* 文件链接相关样式 */
  .fileLink {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    margin: 10px 0;
    color: #333;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
    max-width: 100%;
    cursor: pointer;
  }

  .fileLink:hover {
    background-color: #f0f2f5;
    border-color: #ccc;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .fileLink:active {
    background-color: #e9ecef;
    transform: translateY(1px);
  }

  .fileIcon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .fileInfo {
    overflow: hidden;
    flex: 1;
  }

  .fileName {
    font-weight: 500;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #0366d6;
  }

  .fileType {
    font-size: 0.8em;
    color: #666;
  }

  /* PDF预览样式 */
  .pdfPreview {
    width: 100%;
    height: 500px;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
  }

  /* 文档和PPT预览的占位样式 */
  .docPreview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    margin: 10px 0;
  }

  .previewUnavailable {
    font-style: italic;
    color: #666;
    margin-top: 10px;
  }

  .localPath {
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 2px 5px;
    border-radius: 3px;
    color: #333;
    word-break: break-all;
  }

  .agentName {
    margin-left: 8px;
    font-size: 14px;
    color: #555;
    display: inline-block;
    opacity: 0; /* 默认隐藏 */
    transition: opacity 0.3s ease;
  }

  /* 当父元素有loaded类时显示 */
  .nodeLoaded .agentName {
    opacity: 1;
  }

  /* 代码块卡片样式 */
  .codeBlockCard {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8fafc;
    border: 1px solid #e5e9ef;
    border-radius: 8px;
    margin: 12px 0;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .codeBlockCard:hover {
    background-color: #edf1f7;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
  }

  .codeBlockCardIcon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background-color: #e6f3ff;
    margin-right: 12px;
  }

  .codeBlockCardIcon svg {
    color: #0084ff;
  }

  .codeBlockCardInfo {
    flex: 1;
  }

  .codeBlockCardTitle {
    font-weight: 500;
    font-size: 15px;
    color: #333;
    margin-bottom: 4px;
  }

  .codeBlockCardDesc {
    font-size: 13px;
    color: #666;
  }

  .codePreview {
    font-size: 12px;
    color: #777;
    font-family: monospace;
    display: block;
    margin-top: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  /* 用户消息段落专用样式 */
  :global(.userParagraph) {
    text-align: right !important;
    width: fit-content !important;
    max-width: 100% !important;
    display: inline-block !important;
    margin-left: auto !important;
    margin-right: 0 !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
  }

  /* 用户和AI内容样式优化，确保与Chat.module.css配合良好 */
  .userContent {
    /* 用户消息样式容器 */
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
  }

  .userContent > div {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
    width: auto !important; /* 改为自适应宽度 */
    max-width: 100% !important;
  }

  /* 用户消息中的普通段落右对齐，并自适应宽度 */
  .userContent > div > p {
    text-align: right !important;
    align-self: flex-end !important;
    margin: 0 0 8px 0 !important;
    width: auto !important; /* 宽度自适应内容 */
    max-width: 100% !important;
    display: inline-block !important; /* 使元素宽度自适应内容 */
    word-wrap: break-word !important;
    word-break: break-word !important;
  }

  /* 用户消息中的最后一个段落 */
  .userContent > div > p:last-child {
    margin-bottom: 0 !important;
  }

  /* 用户消息中的内联元素右对齐 */
  .userContent > div span:not([class]),
  .userContent > div a,
  .userContent > div strong,
  .userContent > div em,
  .userContent > div u {
    text-align: right !important;
    direction: rtl !important;
    display: inline !important; /* 保持内联特性 */
  }

  /* 用户消息中的div内容右对齐，自适应宽度 */
  .userContent > div > div:not([class]) {
    text-align: right !important;
    align-self: flex-end !important;
    width: fit-content !important; /* 使用fit-content确保适应内容宽度 */
    max-width: 100% !important; /* 限制最大宽度 */
    display: inline-block !important; /* 使元素宽度自适应内容 */
  }

  /* 特殊内容始终左对齐，不管是用户还是AI消息 */
  .userContent > div pre,
  .userContent > div code,
  .userContent > div table,
  .userContent > div ul,
  .userContent > div ol,
  .userContent > div .customTag,
  .userContent > div .node,
  .userContent > div .think,
  .userContent > div .toolCall,
  .userContent > div .observation,
  .userContent > div [data-type="node"],
  .userContent > div [data-type="think"],
  .userContent > div [data-type="knowledge_base"],
  .userContent > div .knowledge_base {
    text-align: left !important;
    align-self: flex-start !important;
    width: 100% !important;
    direction: ltr !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .aiContent {
    /* AI消息相关样式 */
    width: 100% !important;
  }

  .aiContent > div {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100% !important;
  }

  /* AI消息中的内容左对齐 */
  .aiContent > div > * {
    text-align: left !important;
    align-self: flex-start !important;
    width: 100% !important;
    direction: ltr !important;
  }