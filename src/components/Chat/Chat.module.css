/* src/components/Chat/Chat.module.css */
.layout {
  display: flex;
  height: 100vh;
  /* 白色背景 + 黑色文字 */
  background-color: #fff;
  color: #000;
  overflow: hidden; /* 防止内容溢出 */
}

/* 左侧菜单栏 */
/* 移除了旧的侧边栏样式，由Sidebar.module.css接管 */

/* 右侧聊天区域 */
.chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 白色背景 + 黑色文字 */
  background-color: #fff;
  color: #000;
  min-width: 500px;
  transition: width 0.3s ease;
}

/* 聊天区域在显示Excel预览时的样式 */
.chatWithExcel {
  width: 60%;
  flex: none;
  border-right: 1px solid #e8e8e8;
}

/* 聊天区域在显示虚拟机时的样式 */
.chatWithVirtualMachine {
  width: calc(50vw - 8px);
  flex: none;
  margin-right: 16px;
  border-right: none;
}

/* 整体布局在显示虚拟机时的样式 */
.layoutWithVirtualMachine {
  margin-right: 0;
}

/* Excel预览容器 */
.excelViewerContainer {
  width: 40%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* Excel预览头部 */
.excelViewerHeader {
  display: flex;
  justify-content: flex-end;
  padding: 8px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

/* 关闭Excel预览按钮 */
.closeExcelButton {
  border: none;
  background: transparent;
  font-size: 16px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s;
}

.closeExcelButton:hover {
  color: #ff4d4f;
}

/* 中间内容面板 */
.contentPanel {
  position: fixed;
  top: 0;
  right: 70px; /* 与侧边栏宽度一致 */
  width: 600px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  z-index: 9;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.contentPanelVisible {
  transform: translateX(0);
}

.contentHeader {
  height: 56px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.contentHeaderTitle {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;
}

.contentHeaderTitle::before {
  content: '';
  display: inline-block;
  width: 3px;
  height: 16px;
  background-color: #4c63f6;
  margin-right: 10px;
  border-radius: 2px;
}

.contentBody {
  flex: 1;
  overflow: auto;
  padding: 0;
}

.closeButton {
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.closeButton:hover {
  color: #333;
  background-color: #f0f0f0;
}

.emptyContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #8c8c8c;
  padding: 20px;
  text-align: center;
}

/* 遮罩层 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 8;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease-in-out;
}

.overlayVisible {
  opacity: 1;
  pointer-events: auto;
}

/* 消息列表 */
.messages {
  flex: 1;
  overflow: auto;
  padding: 24px 16px; /* 减小两侧padding，由气泡组件控制边距 */
  /* 白色背景 + 黑色文字 */
  background-color: #fff;
  color: #000;
}

/* 调整气泡和头像的布局 */
.messages :global(.ant-bubble-item) {
  display: flex;
  flex-direction: column;
    width: 100%;
  margin-bottom: 24px; /* 增加气泡之间的间距 */
  position: relative; /* 设置相对定位 */
}

/* 气泡内容包装器样式 */
.messages :global(.ant-bubble) {
  width: auto !important;
  display: flex !important;
  align-items: flex-start !important;
}

/* AI头像样式调整 */
.messages :global(.ant-bubble-item[data-role="ai"] .ant-bubble-avatar) {
  margin-right: 16px !important;
  flex-shrink: 0 !important;
  position: static !important;
}

/* 用户头像样式调整 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-avatar) {
  margin-left: 16px !important;
  margin-right: 0 !important;
  flex-shrink: 0 !important;
  display: flex !important;
  order: 1 !important; /* 确保在右侧 */
  position: static !important; /* 覆盖之前的相对定位 */
}

/* 用户气泡样式调整 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble) {
  flex-direction: row-reverse !important;
}

/* 内容区域样式调整 */
.messages :global(.ant-bubble-content-wrapper) {
  width: auto !important;
  max-width: 90% !important;
  flex: 0 1 auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
}

/* AI消息内容区样式 */
.messages :global(.ant-bubble-item[data-role="ai"] .ant-bubble-content) {
  width: 100% !important;
  max-width: 100% !important;
  background-color: #fff !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

/* 用户气泡样式调整 - 自适应内容宽度 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-wrapper) {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
  width: auto !important;
  max-width: 90% !important;
  min-width: auto !important;
  flex: 0 1 auto !important;
  margin-right: 0 !important;
}

/* 用户消息内容区样式 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content) {
  width: auto !important;
  max-width: 100% !important;
  background-color: #f0f8ff !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 6px !important;
  display: inline-block !important;
  overflow: hidden !important;
  margin-left: auto !important;
  }

/* 用户消息内部文本区域 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner) {
  text-align: right !important;
  padding: 16px !important;
  word-break: break-word !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
  width: auto !important;
  min-width: 30px !important;
}

/* 用户消息内部段落和内联元素样式 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner > p),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner > span),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner > div:not([class])) {
  text-align: right !important;
  width: fit-content !important;
  max-width: 100% !important;
  display: inline-block !important;
  margin-left: auto !important;
  margin-right: 0 !important;
}

/* 用户消息工具栏 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-flex) {
  justify-content: flex-end !important;
    width: 100% !important;
  }

/* 代码块始终左对齐 */
.messages :global(.ant-bubble-content-inner pre),
.messages :global(.ant-bubble-content-inner code),
.messages :global(.ant-bubble-content-inner ul),
.messages :global(.ant-bubble-content-inner ol),
.messages :global(.ant-bubble-content-inner table) {
  text-align: left !important; 
  direction: ltr !important;
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* 商务风格的标题 */
.messages :global(h1), 
.messages :global(h2), 
.messages :global(h3), 
.messages :global(h4), 
.messages :global(h5) {
  position: relative;
  padding-left: 15px;
  margin-top: 16px !important;
  margin-bottom: 16px !important;
  font-weight: 500 !important;
  color: #333 !important;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 8px;
}

.messages :global(h1)::before, 
.messages :global(h2)::before, 
.messages :global(h3)::before, 
.messages :global(h4)::before, 
.messages :global(h5)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #4169E1; /* 蓝色左边框 */
  border-radius: 2px;
}

/* NODE和THINK标签统一样式 */
.messages :global(.custom-block), 
.messages :global(details), 
.messages :global(.node), 
.messages :global(.think),
.messages :global([data-type="node"]), 
.messages :global([data-type="think"]) {
  border: 1px solid #e8e8e8 !important;
  border-radius: 0 !important;
  overflow: hidden !important;
  margin-bottom: 16px !important;
  background-color: #fff !important;
  width: 100% !important;
  min-width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  box-sizing: border-box;
}

/* 用户消息中的块样式 */
.messages :global(.ant-bubble-item[data-role="local"] .custom-block),
.messages :global(.ant-bubble-item[data-role="local"] details),
.messages :global(.ant-bubble-item[data-role="local"] .node),
.messages :global(.ant-bubble-item[data-role="local"] .think),
.messages :global(.ant-bubble-item[data-role="local"] [data-type="node"]),
.messages :global(.ant-bubble-item[data-role="local"] [data-type="think"]) {
  max-width: 100% !important;
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* 统一标题样式 */
.messages :global(.custom-block summary), 
.messages :global(details summary),
.messages :global(.node summary), 
.messages :global(.think summary) {
  position: relative;
  padding: 12px 16px 12px 20px !important;
  background-color: #f5f9f5 !important; /* 淡绿色背景 */
  font-weight: 500 !important;
  color: #333 !important;
  border-bottom: 1px solid #e8e8e8 !important;
  cursor: pointer;
  display: flex !important;
  align-items: center !important;
  text-align: left !important;
}

/* 标题左侧蓝色条 */
.messages :global(.custom-block summary)::before, 
.messages :global(details summary)::before,
.messages :global(.node summary)::before, 
.messages :global(.think summary)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #4169E1; /* 蓝色左边框 */
}

/* 统一内容样式 */
.messages :global(.custom-block .content), 
.messages :global(details .content),
.messages :global(.node .content), 
.messages :global(.think .content) {
  padding: 16px !important;
  background-color: #fff !important;
  line-height: 1.6 !important;
  color: #333 !important;
  text-align: left !important;
  width: 100% !important; /* 确保宽度为100% */
  min-width: 100% !important; /* 确保最小宽度为100% */
  box-sizing: border-box !important;
}

/* 列表样式优化 */
.messages :global(ul), 
.messages :global(ol) {
  padding-left: 20px !important;
  margin: 10px 0 !important;
}

.messages :global(li) {
  margin-bottom: 8px !important;
  line-height: 1.6 !important;
  text-align: left !important;
}

/* 项目符号样式优化 */
.messages :global(ul li::before) {
  content: '•';
  color: #4169E1;
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}

/* 自定义标签大小调整 */
.messages :global(.ant-bubble-custom-tag) {
  width: 100% !important;
  max-width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  box-sizing: border-box;
  padding: 0 !important;
  }

/* 用户气泡的自定义标签 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-custom-tag) {
  width: auto !important;
  min-width: 100px !important;
  max-width: 80% !important;
  margin-left: auto !important;
  margin-right: 0 !important;
  box-sizing: border-box;
}

/* 无边框气泡样式 */
.messages :global(.ant-bubble-content-borderless) {
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  box-sizing: border-box;
}

/* 发送区域 */
.sender {
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  /* 白色背景 + 黑色文字 */
  background-color: #fff;
  color: #000;
}

/* 消息内容样式 */
.messageContent {
  font-size: 14px;
  line-height: 1.6;
}

/* 消息内容和特殊区块样式 */
:global(.specialSection) {
  margin: 8px 0;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fafafa;
}

:global(.sectionHeader) {
  padding: 8px 12px;
  background-color: #f5f5f5;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666;
  user-select: none;
  transition: background-color 0.2s;
}

:global(.sectionHeader:hover) {
  background-color: #f0f0f0;
}

:global(.expandIcon) {
  display: inline-block;
  margin-right: 8px;
  transition: transform 0.2s;
  font-size: 12px;
}

:global(.expandIcon.expanded) {
  transform: rotate(90deg);
}

:global(.sectionContent) {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  background-color: #fff;
}

:global(.sectionContent.expanded) {
  padding: 12px;
  max-height: 1000px;
}

/* 代码块包装器 */
:global(.code-block-wrapper) {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

/* 代码块头部 */
:global(.code-block-header) {
  display: flex;
  justify-content: flex-end;
  padding: 8px;
  position: absolute;
  right: 8px;
  top: 8px;
  background: transparent;
  z-index: 1;
}

/* 复制按钮 */
:global(.copy-button) {
  padding: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.45);
  background-color: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

:global(.copy-button:hover) {
  color: rgba(0, 0, 0, 0.85);
  background-color: rgba(0, 0, 0, 0.06);
}

:global(.copy-button svg) {
  width: 14px;
  height: 14px;
}

/* JSON 代码块样式 */
:global(.json-block) {
  margin: 0 !important;
  padding: 16px !important;
  font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre;
  tab-size: 2;
  position: relative;
}

/* NODE和TOOL_CALL区块样式 */
.messages :global(.knowledge_base),
.messages :global([data-type="knowledge_base"]),
.messages :global([data-tag="TOOL_CALL"]) {
  display: block;
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  margin: 16px 0 !important;
  padding: 0 !important;
  background-color: #f8fbff !important; 
  border: 1px solid #e1e8f0 !important; 
  border-radius: 6px !important;
  overflow: hidden !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

/* NODE标签头部样式 */
.messages :global(.knowledge_base) summary,
.messages :global([data-type="knowledge_base"]) summary,
.messages :global([data-tag="TOOL_CALL"] .tagHeader) {
  position: relative;
  display: flex !important;
  align-items: center !important;
  padding: 12px 16px 12px 20px !important;
  background-color: #edf5ff !important;
  color: #1a5cb0 !important;
  font-weight: 500 !important;
  cursor: pointer;
  border-bottom: 1px solid #e1e8f0 !important;
  user-select: none;
}

/* NODE标签左侧边框 */
.messages :global(.knowledge_base) summary::before,
.messages :global([data-type="knowledge_base"]) summary::before,
.messages :global([data-tag="TOOL_CALL"] .tagHeader)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #4285f4 !important;
}

/* NODE标签内容区域样式 */
.messages :global(.knowledge_base) .content,
.messages :global([data-type="knowledge_base"]) .content,
.messages :global([data-tag="TOOL_CALL"]) .tagContent {
  padding: 16px !important;
  background-color: #fff !important;
  line-height: 1.6 !important;
  color: #333 !important;
  text-align: left !important;
  font-size: 14px !important;
  width: 100% !important; /* 确保宽度为100% */
  min-width: 100% !important; /* 确保最小宽度为100% */
  box-sizing: border-box !important;
}

/* 确保在用户消息中的NODE区块也左对齐文本 */
.messages :global(.ant-bubble-item[data-role="local"]) :global(.knowledge_base) .content,
.messages :global(.ant-bubble-item[data-role="local"]) :global([data-type="knowledge_base"]) .content,
.messages :global(.ant-bubble-item[data-role="local"]) :global([data-tag="TOOL_CALL"]) .tagContent,
.messages :global(.ant-bubble-item[data-role="local"]) :global(.custom-block) .content {
  text-align: left !important;
}

/* 修复自定义内容的样式 */
.messages :global(.ant-bubble-custom) {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 修复代码块显示 */
.messages :global(.ant-bubble-content pre),
.messages :global(.ant-bubble-content code) {
  text-align: left !important;
  display: block;
  max-width: 100%;
  overflow-x: auto;
  direction: ltr !important;
}

/* NODE区块在 local 消息中的特殊样式 */
.messages :global(.ant-bubble-item[data-role="local"]) :global(.ant-bubble-custom) {
  text-align: right !important;
  margin-left: auto !important;
}

/* 确保NODE和TOOL_CALL等区块在local消息中左对齐且自动宽度 */
.messages :global(.ant-bubble-item[data-role="local"]) :global(.ant-bubble-custom) :global(.knowledge_base),
.messages :global(.ant-bubble-item[data-role="local"]) :global(.ant-bubble-custom) :global([data-type="knowledge_base"]),
.messages :global(.ant-bubble-item[data-role="local"]) :global(.ant-bubble-custom) :global([data-tag="TOOL_CALL"]),
.messages :global(.ant-bubble-item[data-role="local"]) :global(.ant-bubble-custom) :global(.custom-block),
.messages :global(.ant-bubble-item[data-role="local"]) :global(.ant-bubble-custom) :global(.node) {
  text-align: left !important;
  width: 100% !important;
  max-width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  align-self: flex-start !important;
}

/* NODE和TOOL_CALL内容区域始终左对齐 */
.messages :global(.ant-bubble-item[data-role="local"]) :global(.knowledge_base) .content,
.messages :global(.ant-bubble-item[data-role="local"]) :global([data-type="knowledge_base"]) .content,
.messages :global(.ant-bubble-item[data-role="local"]) :global([data-tag="TOOL_CALL"]) .tagContent,
.messages :global(.ant-bubble-item[data-role="local"]) :global(.node) .content,
.messages :global(.ant-bubble-item[data-role="local"]) :global(.custom-block) .content,
.messages :global(.ant-bubble-item[data-role="local"]) :global(.custom-block) .tagContent {
  text-align: left !important;
  padding: 16px !important;
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  direction: ltr !important;
}

/* 这些代码块和特殊区块样式已移至"特殊内容保持左对齐"部分 */

/* ============== 删除之前重复的样式 ============== */

/* ============== 添加全新的用户消息气泡样式，提高优先级 ============== */

/* 用户消息气泡 - 基本样式 */
.messages :global(.ant-bubble-item[data-role="local"]) {
  display: flex !important;
  flex-direction: row !important;
  justify-content: flex-end !important;
  width: 100% !important;
  margin-bottom: 24px !important;
}

/* 用户消息气泡容器 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble) {
  display: flex !important;
  flex-direction: row-reverse !important;
  align-items: flex-start !important;
  width: 100% !important;
  justify-content: flex-end !important;
}

/* 用户头像样式 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-avatar) {
  margin-left: 16px !important;
  margin-right: 0 !important;
  flex-shrink: 0 !important;
  display: flex !important;
  order: 1 !important;
  width: 40px !important;
}

/* 用户消息内容包装器 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-wrapper) {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
  width: auto !important;
  max-width: 80% !important;
  min-width: auto !important;
  flex: 0 1 auto !important;
  margin-right: 0 !important;
}

/* 用户消息内容区域 - 核心样式 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content) {
  width: auto !important;
  max-width: 100% !important;
  background-color: #f0f8ff !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 6px !important;
  margin-left: auto !important;
  display: inline-block !important;
  overflow: hidden !important;
}

/* 用户消息内部文本区域 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner) {
  text-align: right !important;
  padding: 16px !important;
  word-break: break-word !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
  width: auto !important;
  min-width: 30px !important;
}

/* 用户消息内部文本元素 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner > p),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner > span:not([class])),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner > div:not([class])) {
  display: inline-block !important;
  text-align: right !important;
  width: auto !important;
  max-width: 100% !important;
  white-space: pre-wrap !important;
  margin-left: auto !important;
  margin-right: 0 !important;
}

/* ============ 确保特殊内容左对齐 ============ */

/* 特殊内容保持左对齐 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner pre),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner code),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner ul),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner ol),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner table),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner .node),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner .knowledge_base),
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-inner [data-type="knowledge_base"]) {
  text-align: left !important;
  align-self: flex-start !important;
  width: 100% !important;
  max-width: 100% !important;
  margin-left: 0 !important;
  margin-right: auto !important;
  direction: ltr !important;
}

/* 移除自动生成的伪元素头像 */
.messages :global(.ant-bubble-item::before),
.messages :global(.ant-bubble-item::after) {
  display: none !important;
}

/* AI气泡 - 基础样式 */
.messages :global(.ant-bubble-content), 
.messages :global(.ant-bubble-content-filled) {
  width: 100% !important;
  max-width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  box-sizing: border-box;
  border-radius: 0 !important;
  box-shadow: none !important;
  background-color: #fff !important;
  border: 1px solid #e8e8e8 !important;
  overflow: hidden !important;
}

/* AI消息内容 */
.messages :global(.ant-bubble-content-inner) {
  width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
  box-sizing: border-box;
  padding: 16px !important;
  text-align: left !important; /* AI消息内容左对齐 */
}

/* ============ 删除重复的结尾部分用户消息样式 ============ */

/* ============ 用户消息样式优化 - 最终版本 ============ */

/* 用户气泡 - 基础样式 */
.messages :global(.ant-bubble-item[data-role="local"]) {
  /* 删除此处重复内容 */
}

/* 用户消息气泡容器 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble) {
  /* 删除此处重复内容 */
}

/* 用户头像样式 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-avatar) {
  /* 删除此处重复内容 */
}

/* 用户消息内容包装器 */
.messages :global(.ant-bubble-item[data-role="local"] .ant-bubble-content-wrapper) {
  /* 删除此处重复内容 */
}

/* 结尾保留特殊区块样式 */

/* 响应式设计 - 移动设备 */
@media (max-width: 768px) {
  .layout {
    flex-direction: column;
  }
  
  .layoutWithVirtualMachine {
    padding-right: 0;
  }
  
  .chat {
    min-width: 300px;
  }
  
  .chatWithVirtualMachine {
    width: 100%;
    border-right: none;
  }
  
  .chatWithExcel {
    width: 100%;
    border-right: none;
  }
  
  .excelViewerContainer {
    width: 100%;
    height: 50vh;
    position: fixed;
    bottom: 0;
    z-index: 999;
  }
}

/* 响应式设计 - 平板设备 */
@media (max-width: 1024px) and (min-width: 769px) {
  .chatWithVirtualMachine {
    width: calc(40vw - 8px);
    margin-right: 16px;
  }
}