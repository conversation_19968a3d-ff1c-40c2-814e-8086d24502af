.searchResults {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.queryInfo {
  margin-bottom: 8px;
}

.queryInfo .ant-typography {
  margin-right: 8px;
}

.resultCount {
  display: flex;
  justify-content: flex-end;
}

.resultsList {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
}

.resultCard {
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.resultCard:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.cardTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.titleLeft {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.itemNumber {
  color: #666;
  font-weight: 500;
  margin-right: 8px;
  font-size: 14px;
}

.titleWithIcon {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  gap: 8px;
}

.titleIcon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  flex-shrink: 0;
}

.defaultIcon {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
}

.titleText {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clickableTitle {
  cursor: pointer;
  color: #1890ff;
  transition: color 0.3s ease;
}

.clickableTitle:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.titleRight {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
}

.pageInfo {
  font-size: 12px;
  padding: 2px 6px;
  background: #f5f5f5;
  border-radius: 4px;
}

.cardActions {
  display: flex;
  gap: 4px;
}

.linkButton {
  color: #1890ff;
}

.linkButton:hover {
  color: #40a9ff;
}

.content {
  padding-top: 8px;
}

.contentText {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #333;
}

.contentText.ant-typography {
  margin-bottom: 8px;
}

.scoreInfo {
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.pagination {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .searchResults {
    padding: 12px;
  }
  
  .cardTitle {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .titleRight {
    margin-left: 0;
  }
  
  .titleText {
    white-space: normal;
    word-break: break-word;
  }
} 