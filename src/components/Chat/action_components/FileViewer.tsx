import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Spin, Alert, Typography } from 'antd';
import { ArrowLeftOutlined, FileTextOutlined, LinkOutlined, DownloadOutlined } from '@ant-design/icons';
import styles from './FileViewer.module.css';

const { Title, Text } = Typography;

interface FileViewerProps {
  fileUrl?: string;
  webUrl?: string;
  title: string;
  onBack: () => void;
}

const FileViewer: React.FC<FileViewerProps> = ({
  fileUrl,
  webUrl,
  title,
  onBack
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);

  useEffect(() => {
    if (fileUrl) {
      // 检查是否是PDF文件
      const isPdfFile = fileUrl.toLowerCase().includes('.pdf');
      setIsPdf(isPdfFile);
    }
  }, [fileUrl]);

  const handleLoad = () => {
    setLoading(false);
    setError(null);
  };

  const handleError = () => {
    setLoading(false);
    setError('文件加载失败，请检查文件链接是否有效');
  };

  const handleDownload = () => {
    if (fileUrl) {
      window.open(fileUrl, '_blank');
    }
  };

  const displayUrl = fileUrl || webUrl;
  const isFile = !!fileUrl;

  return (
    <div className={styles.fileViewer}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Button 
            type="text" 
            icon={<ArrowLeftOutlined />} 
            onClick={onBack}
            className={styles.backButton}
          >
            返回搜索结果
          </Button>
        </div>
        
        <div className={styles.headerCenter}>
          <div className={styles.titleContainer}>
            {isFile ? <FileTextOutlined /> : <LinkOutlined />}
            <Title level={4} className={styles.title}>
              {title}
            </Title>
          </div>
        </div>
        
        <div className={styles.headerRight}>
          {fileUrl && (
            <Button 
              type="primary" 
              icon={<DownloadOutlined />}
              onClick={handleDownload}
              size="small"
            >
              下载文件
            </Button>
          )}
        </div>
      </div>

      <div className={styles.content}>
        {loading && (
          <div className={styles.loadingContainer}>
            <Spin size="large" />
            <Text>正在加载{isFile ? '文件' : '网页'}...</Text>
          </div>
        )}

        {error && (
          <div className={styles.errorContainer}>
            <Alert
              message="加载失败"
              description={error}
              type="error"
              showIcon
              action={
                <Button size="small" onClick={() => window.location.reload()}>
                  重试
                </Button>
              }
            />
          </div>
        )}

        {displayUrl && (
          <div className={styles.iframeContainer}>
            {isPdf ? (
              <iframe
                src={`${displayUrl}#toolbar=1&navpanes=1&scrollbar=1&page=1&view=FitH`}
                className={styles.iframe}
                onLoad={handleLoad}
                onError={handleError}
                title={title}
                style={{ display: loading || error ? 'none' : 'block' }}
              />
            ) : (
              <iframe
                src={displayUrl}
                className={styles.iframe}
                onLoad={handleLoad}
                onError={handleError}
                title={title}
                style={{ display: loading || error ? 'none' : 'block' }}
                sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
              />
            )}
          </div>
        )}

        {!displayUrl && (
          <div className={styles.errorContainer}>
            <Alert
              message="无效链接"
              description="未提供有效的文件或网页链接"
              type="warning"
              showIcon
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default FileViewer; 