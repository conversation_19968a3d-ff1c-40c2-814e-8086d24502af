import React, { useState } from 'react';
import { Pagin<PERSON>, Card, Button, Typography, Badge } from 'antd';
import { FileTextOutlined, ExpandAltOutlined, ShrinkOutlined, FileOutlined, GlobalOutlined } from '@ant-design/icons';
import styles from './SearchResults.module.css';

const { Text, Paragraph } = Typography;

interface SearchResultItem {
  title_SEP_chunk_SEP_semantic?: string;
  retrieval_score?: number;
  rerank_score?: number;
  content: string;
  title: string;
  file_url?: string;
  url?: string;
  icon?: string;
  page_id?: number;
  query?: string;
  uuid: string;
}

interface SearchResultsProps {
  data: SearchResultItem[];
  query: string;
  onFileClick: (fileUrl: string, title: string) => void;
  onUrlClick: (url: string, title: string) => void;
}

const SearchResults: React.FC<SearchResultsProps> = ({
  data,
  query,
  onFileClick,
  onUrlClick
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  
  const pageSize = 10;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = data.slice(startIndex, endIndex);

  const toggleExpand = (uuid: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(uuid)) {
      newExpanded.delete(uuid);
    } else {
      newExpanded.add(uuid);
    }
    setExpandedItems(newExpanded);
  };

  const handleLinkClick = (item: SearchResultItem) => {
    if (item.file_url) {
      onFileClick(item.file_url, item.title);
    } else if (item.url) {
      onUrlClick(item.url, item.title);
    }
  };

  const handleTitleClick = (item: SearchResultItem) => {
    if (item.file_url || item.url) {
      handleLinkClick(item);
    }
  };

  const getDefaultIcon = (item: SearchResultItem) => {
    if (item.file_url) {
      return <FileOutlined />;
    } else if (item.url) {
      return <GlobalOutlined />;
    }
    return <FileTextOutlined />;
  };

  const truncateContent = (content: string, maxLength: number = 100) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const getScoreColor = (score: number) => {
    if (score >= 7) return '#52c41a'; // 绿色
    if (score >= 5) return '#fadb14'; // 黄色
    return '#ff4d4f'; // 红色
  };

  return (
    <div className={styles.searchResults}>
      <div className={styles.header}>
        <div className={styles.queryInfo}>
          <Text strong>🔍 搜索查询：</Text>
          <Text code>{query}</Text>
        </div>
        <div className={styles.resultCount}>
          <Text type="secondary">共找到 {data.length} 条结果</Text>
        </div>
      </div>

      <div className={styles.resultsList}>
        {currentData.map((item, _index) => {
          const isExpanded = expandedItems.has(item.uuid);
          const displayContent = isExpanded 
            ? item.content 
            : truncateContent(item.content);
          
          return (
            <Card
              key={item.uuid}
              className={styles.resultCard}
              size="small"
              title={
                <div className={styles.cardTitle}>
                  <div className={styles.titleLeft}>
                    <div className={styles.titleWithIcon}>
                      {item.icon ? (
                        <img 
                          src={item.icon} 
                          alt="" 
                          className={styles.titleIcon}
                          onError={(e) => {
                            // 如果图片加载失败，显示默认图标
                            e.currentTarget.style.display = 'none';
                            const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                            if (nextElement) {
                              nextElement.style.display = 'inline-block';
                            }
                          }}
                        />
                      ) : null}
                      <span 
                        className={styles.defaultIcon}
                        style={{ display: item.icon ? 'none' : 'inline-block' }}
                      >
                        {getDefaultIcon(item)}
                      </span>
                      <Text 
                        strong 
                        className={`${styles.titleText} ${(item.file_url || item.url) ? styles.clickableTitle : ''}`}
                        onClick={() => handleTitleClick(item)}
                      >
                        {item.title}
                      </Text>
                    </div>
                  </div>
                  <div className={styles.titleRight}>
                    {item.rerank_score && (
                      <Badge 
                        count={item.rerank_score.toFixed(2)} 
                        style={{ 
                          backgroundColor: getScoreColor(item.rerank_score),
                          fontSize: '10px'
                        }}
                      />
                    )}
                    {item.page_id && (
                      <Text type="secondary" className={styles.pageInfo}>
                        第{item.page_id}页
                      </Text>
                    )}
                  </div>
                </div>
              }
              extra={
                <div className={styles.cardActions}>
                  {item.content.length > 100 && (
                    <Button
                      type="text"
                      size="small"
                      icon={isExpanded ? <ShrinkOutlined /> : <ExpandAltOutlined />}
                      onClick={() => toggleExpand(item.uuid)}
                    >
                      {isExpanded ? '收起' : '展开'}
                    </Button>
                  )}
                </div>
              }
            >
              <div className={styles.content}>
                <Paragraph className={styles.contentText}>
                  {displayContent}
                </Paragraph>
                
                {item.retrieval_score && (
                  <div className={styles.scoreInfo}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      检索分数: {item.retrieval_score.toFixed(3)}
                      {item.rerank_score && ` | 重排分数: ${item.rerank_score.toFixed(3)}`}
                    </Text>
                  </div>
                )}
              </div>
            </Card>
          );
        })}
      </div>

      {data.length > pageSize && (
        <div className={styles.pagination}>
          <Pagination
            current={currentPage}
            total={data.length}
            pageSize={pageSize}
            onChange={setCurrentPage}
            showSizeChanger={false}
            showQuickJumper
            showTotal={(total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }
          />
        </div>
      )}
    </div>
  );
};

export default SearchResults; 