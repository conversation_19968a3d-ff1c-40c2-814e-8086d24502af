.fileViewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  min-height: 60px;
}

.headerLeft {
  flex: 0 0 auto;
}

.headerCenter {
  flex: 1;
  display: flex;
  justify-content: center;
  min-width: 0;
}

.headerRight {
  flex: 0 0 auto;
}

.backButton {
  color: #1890ff;
  padding: 4px 8px;
}

.backButton:hover {
  color: #40a9ff;
  background: rgba(24, 144, 255, 0.1);
}

.titleContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 500px;
}

.title {
  margin: 0 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 16px;
  color: #333;
}

.titleContainer .anticon {
  color: #666;
  font-size: 16px;
}

.content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
}

.loadingContainer .ant-typography {
  color: #666;
}

.errorContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.iframeContainer {
  height: 100%;
  width: 100%;
}

.iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 8px 12px;
    min-height: 50px;
  }
  
  .headerCenter {
    margin: 0 8px;
  }
  
  .titleContainer {
    max-width: 200px;
  }
  
  .title {
    font-size: 14px;
  }
  
  .backButton {
    font-size: 12px;
  }
  
  .headerRight .ant-btn {
    font-size: 12px;
  }
} 