import React from 'react';

// 新增对话图标 - 现代化的聊天泡泡+加号
export const NewChatIcon: React.FC = () => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="icon-glow">
    <filter id="glow1" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
    <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 13.9021 3.5901 15.6665 4.59721 17.1199C4.70168 17.2707 4.73667 17.4653 4.68274 17.6428L3.90087 20.099C3.78174 20.5127 4.16901 20.8856 4.58837 20.7538L7.2251 19.9487C7.376 19.9035 7.54244 19.9191 7.67962 19.9916C8.88599 20.6408 10.3051 21 11.8195 21C11.8792 21 11.9386 20.9989 11.9979 20.9967" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" filter="url(#glow1)"/>
    <path d="M15 11H17M15 15H17M7 11H12M7 15H12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" filter="url(#glow1)"/>
    <path d="M19 8L16 5M16 8L19 5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" filter="url(#glow1)"/>
  </svg>
);

// 数据集图标 - 数据可视化样式
export const DatasetIcon: React.FC = () => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="icon-glow">
    <filter id="glow2" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
    <rect x="4" y="5" width="16" height="14" rx="2" stroke="currentColor" strokeWidth="1.5" filter="url(#glow2)"/>
    <path d="M8 10H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" filter="url(#glow2)"/>
    <path d="M8 14H12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" filter="url(#glow2)"/>
    <path d="M15 14H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" filter="url(#glow2)"/>
  </svg>
);

// 知识库图标 - 3D立体知识库
export const KnowledgeBaseIcon: React.FC = () => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 3L20 7.5V16.5L12 21L4 16.5V7.5L12 3Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 12L20 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 12V21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 12L4 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16 5.25L8 9.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// 工作流图标 - 网络节点连接
export const WorkflowIcon: React.FC = () => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.5 7.5C5.88071 7.5 7 6.38071 7 5C7 3.61929 5.88071 2.5 4.5 2.5C3.11929 2.5 2 3.61929 2 5C2 6.38071 3.11929 7.5 4.5 7.5Z" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M12 13.5C13.3807 13.5 14.5 12.3807 14.5 11C14.5 9.61929 13.3807 8.5 12 8.5C10.6193 8.5 9.5 9.61929 9.5 11C9.5 12.3807 10.6193 13.5 12 13.5Z" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M19.5 7.5C20.8807 7.5 22 6.38071 22 5C22 3.61929 20.8807 2.5 19.5 2.5C18.1193 2.5 17 3.61929 17 5C17 6.38071 18.1193 7.5 19.5 7.5Z" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M19.5 21.5C20.8807 21.5 22 20.3807 22 19C22 17.6193 20.8807 16.5 19.5 16.5C18.1193 16.5 17 17.6193 17 19C17 20.3807 18.1193 21.5 19.5 21.5Z" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M4.5 21.5C5.88071 21.5 7 20.3807 7 19C7 17.6193 5.88071 16.5 4.5 16.5C3.11929 16.5 2 17.6193 2 19C2 20.3807 3.11929 21.5 4.5 21.5Z" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M7 5L17 5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M7 19H17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M14 9L18 7" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M14 13L18 17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M10 13L6 17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M10 9L6 7" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

// 智能体图标 - 科技感脑图
export const AgentIcon: React.FC = () => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 16.5C14.4853 16.5 16.5 14.4853 16.5 12C16.5 9.51472 14.4853 7.5 12 7.5C9.51472 7.5 7.5 9.51472 7.5 12C7.5 14.4853 9.51472 16.5 12 16.5Z" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M12 16.5V21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M12 7.5V3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M16.5 12H21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M3 12H7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M15 15L18 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M6 18L9 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M15 9L18 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M6 6L9 9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <circle cx="12" cy="12" r="2" fill="currentColor"/>
  </svg>
);

// 历史会话图标 - 高科技时钟
export const HistoryIcon: React.FC = () => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M12 7V12L15 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 21V19" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M21 12H19" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M12 3V5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M3 12H5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M18.5 5.5L17 7" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M5.5 5.5L7 7" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

// 设置图标 - 现代化齿轮
export const SettingsIcon: React.FC = () => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16 12C16 14.2091 14.2091 16 12 16C9.79086 16 8 14.2091 8 12C8 9.79086 9.79086 8 12 8C14.2091 8 16 9.79086 16 12Z" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M12.9046 3L12.9046 3.10002C12.9046 3.59624 13.3083 4 13.8045 4L13.9062 4C14.2955 4 14.615 4.28292 14.6645 4.66941L14.8159 6.04857C14.8781 6.53314 15.2368 6.92067 15.7048 7.0699L15.8848 7.11391C16.1487 7.19383 16.4413 7.14825 16.6621 6.97353L17.4248 6.37549C17.6973 6.15788 18.0834 6.18089 18.3306 6.4281L18.3306 6.4281C18.5784 6.67579 18.6014 7.06447 18.3827 7.33716L17.8071 8.063C17.6484 8.26252 17.6013 8.52958 17.6745 8.77228L17.7198 8.92693C17.8562 9.36095 18.243 9.6731 18.6936 9.72329L20.0465 9.85092C20.4329 9.89448 20.7227 10.2145 20.7227 10.6031V10.6031C20.7227 10.99 20.4361 11.3091 20.0511 11.3562L18.6909 11.5234C18.2417 11.5842 17.8587 11.8949 17.7198 12.3273L17.6902 12.4292C17.6016 12.7223 17.6558 13.0397 17.8374 13.2859L18.3734 13.9966C18.5775 14.2641 18.5551 14.6397 18.3207 14.8741V14.8741C18.0821 15.1127 17.7001 15.1336 17.4356 14.9221L16.6735 14.3209C16.4565 14.1511 16.1726 14.0985 15.9101 14.1725L15.7378 14.2152C15.2736 14.3554 14.9144 14.7357 14.8426 15.2096L14.6688 16.6285C14.6194 17.0151 14.2999 17.298 13.9105 17.298L13.8099 17.298C13.3137 17.298 12.9101 16.8942 12.9101 16.398L12.9101 14.9801C12.9101 14.5034 12.5798 14.0968 12.1085 14.0201L12.0041 14.0069C11.5141 13.9278 11.0283 14.0217 10.6021 14.2779L9.38678 14.9954C9.04376 15.2012 8.61906 15.1417 8.33919 14.8618V14.8618C8.08752 14.6101 8.05231 14.2399 8.25507 13.9482L8.89211 13.0158C9.09179 12.7259 9.13947 12.354 9.01959 12.0193L8.9961 11.9601C8.84605 11.6032 8.53139 11.34 8.14878 11.2717L6.60065 11.0162C6.21492 10.9479 5.93384 10.6081 5.93384 10.216V10.216C5.93384 9.8261 6.21228 9.48768 6.59666 9.41683L8.12967 9.13144C8.52162 9.05947 8.84395 8.7867 8.98777 8.41301L9.01896 8.37641C9.12246 8.04941 9.08268 7.68577 8.89214 7.39179L8.28224 6.49757C8.06912 6.18876 8.11394 5.77653 8.38584 5.52062V5.52062C8.65775 5.26471 9.06998 5.24513 9.36756 5.47424L10.2341 6.08759C10.5343 6.30791 10.9211 6.38515 11.2912 6.29093L11.4261 6.25216C11.8019 6.15216 12.0821 5.8433 12.1669 5.46148L12.4465 4.12088C12.5078 3.84779 12.7448 3.64642 13.0235 3.62978L13.0235 3.62978C13.3744 3.60882 13.6855 3.83819 13.7488 4.18404L13.9543 5.455C14.0094 5.76286 14.2163 6.01764 14.5026 6.13735L14.7378 6.23519C15.0986 6.38635 15.5148 6.33222 15.8216 6.09648L16.8339 5.31033C17.1193 5.0921 17.5155 5.11775 17.7721 5.3744L17.7721 5.3744C18.0287 5.63105 18.0544 6.02722 17.8361 6.31262L17.0501 7.32496C16.8143 7.63176 16.7602 8.04798 16.9114 8.40879L16.9515 8.50261C17.0878 8.82739 17.3711 9.06125 17.7095 9.12482L19.0705 9.34669C19.4271 9.41274 19.6879 9.72439 19.6879 10.0868V10.0868C19.6879 10.4492 19.4271 10.7609 19.0705 10.8269L17.7095 11.0488C17.3711 11.1124 17.0878 11.3462 16.9515 11.671L16.9114 11.7648C16.7602 12.1256 16.8143 12.5419 17.0501 12.8487L17.8354 13.8603C18.0537 14.1457 18.0281 14.5419 17.7714 14.7985V14.7985C17.5148 15.0552 17.1186 15.0808 16.8332 14.8626L15.821 14.0764C15.5141 13.8407 15.098 13.7866 14.7371 13.9377L14.5019 14.0356C14.2156 14.1553 14.0087 14.4101 13.9536 14.7179L13.7481 15.9889C13.6848 16.3347 13.3738 16.5641 13.0228 16.5431L13.0228 16.5431C12.7441 16.5265 12.5071 16.3251 12.4458 16.052L12.1663 14.7114C12.0814 14.3296 11.8013 14.0208 11.4254 13.9208L11.2905 13.882C10.9204 13.7878 10.5336 13.865 10.2334 14.0853L9.36693 14.6987C9.06934 14.9278 8.65711 14.9082 8.3852 14.6523V14.6523C8.11329 14.3964 8.06848 13.9842 8.2816 13.6754L8.89149 12.7811C9.08203 12.4872 9.12182 12.1235 8.99936 11.7965L8.98713 11.7599C8.84331 11.3862 8.52099 11.1135 8.12903 11.0415L6.59603 10.7561C6.21165 10.6852 5.93321 10.3468 5.93321 9.95691V9.95691C5.93321 9.56488 6.21429 9.2251 6.60002 9.15678L8.14815 8.90123C8.53076 8.83295 8.84541 8.56975 8.99547 8.21283L9.01896 8.15371C9.13883 7.81898 9.09116 7.44711 8.89148 7.15717L8.25444 6.22482C8.05168 5.93312 8.08689 5.56295 8.33855 5.31129V5.31129C8.61843 5.03141 9.04313 4.97185 9.38614 5.17767L10.6015 5.8952C11.0277 6.15138 11.5135 6.24528 12.0034 6.16623L12.1079 6.15301C12.5791 6.07626 12.9095 5.66968 12.9095 5.19298L12.9095 3.77507C12.9095 3.27885 13.3131 2.875 13.8093 2.875L13.9099 2.875C14.2993 2.875 14.6188 3.15792 14.6682 3.54441L14.8419 4.96335C14.9138 5.43726 15.2729 5.81759 15.7372 5.95773L15.9094 6.00047C16.172 6.07444 16.4559 6.02188 16.6729 5.85205L17.435 5.25082C17.6995 5.03941 18.0815 5.06027 18.3201 5.29887V5.29887C18.5545 5.5333 18.5769 5.90892 18.3728 6.1764L17.8368 6.8871C17.6552 7.13332 17.601 7.45066 17.6896 7.74376L17.7192 7.84571C17.8581 8.27813 18.2411 8.58887 18.6903 8.64959L20.0505 8.81682C20.4354 8.86397 20.7221 9.18303 20.7221 9.56996V9.56996C20.7221 9.95857 20.4322 10.2786 20.0459 10.3222L18.693 10.4498C18.2424 10.5 17.8556 10.8121 17.7192 11.2461L17.6738 11.4008C17.6007 11.6435 17.6478 11.9105 17.8064 12.11L18.382 12.8359C18.6008 13.1086 18.5778 13.4973 18.33 13.745V13.745C18.0828 13.9922 17.6967 14.0152 17.4242 13.7976L16.6615 13.1995C16.4406 13.0248 16.1481 12.9792 15.8842 13.0591L15.7042 13.1031C15.2361 13.2524 14.8775 13.6399 14.8153 14.1245L14.6639 15.5037C14.6143 15.8901 14.2949 16.173 13.9055 16.173L13.8038 16.173C13.3076 16.173 12.9039 15.7693 12.9039 15.273L12.9039 15.173" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

// 返回图标 - 流畅的箭头
export const BackIcon: React.FC = () => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M19 12H5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 19L5 12L12 5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// 折叠/展开图标 - 更智能的箭头
export const CollapseIcon: React.FC<{collapsed: boolean}> = ({ collapsed }) => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" 
    style={{
      transform: collapsed ? 'rotate(180deg)' : 'none', 
      transition: 'transform 0.3s cubic-bezier(0.2, 0, 0, 1)'
    }}
    className="icon-glow">
    <filter id="glow-collapse" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="1.5" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
    <path d="M15 6L9 12L15 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" filter="url(#glow-collapse)"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M15.7071 5.29289C16.0976 5.68342 16.0976 6.31658 15.7071 6.70711L10.4142 12L15.7071 17.2929C16.0976 17.6834 16.0976 18.3166 15.7071 18.7071C15.3166 19.0976 14.6834 19.0976 14.2929 18.7071L8.29289 12.7071C7.90237 12.3166 7.90237 11.6834 8.29289 11.2929L14.2929 5.29289C14.6834 4.90237 15.3166 4.90237 15.7071 5.29289Z" fill="currentColor" filter="url(#glow-collapse)"/>
  </svg>
);

// 聊天图标 - 科技感消息
export const ConversationIcon: React.FC = () => (
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 10.5H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M8 14H13.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M14.5 19L12 21.5L9.5 19H5.1C4.76863 19 4.5 18.7314 4.5 18.4V5.6C4.5 5.26863 4.76863 5 5.1 5H18.9C19.2314 5 19.5 5.26863 19.5 5.6V18.4C19.5 18.7314 19.2314 19 18.9 19H14.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M14.5 3V5.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M9.5 3V5.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
); 