/* 聊天输入框自定义样式 */
.ant-sender {
  margin-bottom: 20px !important;
  width: 80% !important;
  max-width: 900px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  border-radius: 16px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(24, 144, 255, 0.2) !important;
  overflow: hidden !important;
}

/* 输入框内容区 */
.ant-sender .ant-sender-content {
  padding: 8px 16px !important;
  display: flex !important;
  align-items: center !important;
}

/* 确保输入框内部元素垂直居中 */
.ant-sender-middle {
  align-items: center !important;
  display: flex !important;
}

/* 输入框本身 */
.ant-sender-input {
  background: transparent !important;
  margin: 8px 0 !important;
  padding: 6px 0 !important;
  min-height: 48px !important;
  font-size: 15px !important;
}

/* 输入框工具栏 */
.ant-sender-toolbar {
  padding: 8px !important;
  border-top: 1px solid rgba(0, 0, 0, 0.06) !important;
  display: flex !important;
  align-items: center !important;
  height: 56px !important; /* 固定高度确保一致性 */
}

/* 发送按钮 */
.ant-sender-action-button {
  background: linear-gradient(135deg, #1890ff, #096dd9) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.5) !important;
  margin-left: 8px !important;
}

/* 发送按钮悬停 */
.ant-sender-action-button:hover {
  background: linear-gradient(135deg, #40a9ff, #1890ff) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.6) !important;
}

/* 发送按钮图标 */
.ant-sender-action-button svg,
.ant-sender-microphone svg,
.ant-sender-prefix button svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) !important;
  color: inherit !important;
  display: block !important;
  margin: 0 auto !important;
}

.ant-sender-action-button svg {
  color: white !important;
}

/* 输入框前缀图标样式 */
.ant-sender-prefix {
  padding-right: 12px !important;
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}

/* 前缀图标按钮居中 */
.ant-sender-prefix button,
.ant-sender-microphone,
.ant-sender-toolbar button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 8px !important;
  height: 36px !important;
  width: 36px !important;
  border-radius: 8px !important;
  margin: 0 4px !important;
}

/* 语音图标特别样式 */
.ant-sender-microphone {
  margin-right: 8px !important;
}

/* 确保在移动设备上的适配 */
@media (max-width: 768px) {
  .ant-sender {
    width: 90% !important;
  }
} 