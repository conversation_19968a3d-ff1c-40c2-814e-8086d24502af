.rightSidebar {
  display: flex;
  height: 100%;
  width: 70px;
  min-width: 70px;
  max-width: 70px;
  background-color: #f7f7f7;
  border-left: 1px solid #f0f0f0;
  z-index: 10;
  position: relative;
}

.rightSidebar.collapsed {
  width: 48px;
  min-width: 48px;
  max-width: 48px;
}

.navigation {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20px;
  position: relative;
  height: 100%;
}

.navItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  width: 100%;
  cursor: pointer;
  position: relative;
  color: #595959;
  transition: all 0.2s ease;
  height: 70px;
}

.navItem:hover:not(.disabled) {
  background-color: #f0f0f0;
  color: #1890ff;
}

.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1890ff;
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon {
  font-size: 22px;
  margin-bottom: 6px;
  position: relative;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 18px;
  height: 18px;
  background-color: #ff6b4c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.title {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  margin-top: 2px;
}

.collapseButton {
  width: 100%;
  margin-top: auto;
  margin-bottom: 20px;
}

.collapseButton .navItem {
  padding: 10px 0;
}

.collapseButton .icon {
  margin-bottom: 0;
  font-size: 14px;
}

.content {
  flex: 1;
  overflow: auto;
  padding: 16px;
}

.emptyContent {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #8c8c8c;
  font-size: 14px;
  text-align: center;
}

.attachmentItem {
  margin-bottom: 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
  padding: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.attachmentTitle {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.attachmentMeta {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.attachmentAbstract {
  font-size: 13px;
  color: #595959;
  margin-top: 4px;
  line-height: 1.5;
} 