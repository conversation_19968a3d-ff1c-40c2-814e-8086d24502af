// src/components/Chat/Chat.tsx

import React, { useEffect, useState, useRef } from 'react';
import {
  Attachments,
  Bubble,
  Prompts,
  Sender,
  useXAgent,
  useXChat
} from '@ant-design/x';
import {
  PaperClipOutlined,
  CloudUploadOutlined,
  CloseOutlined,
  // LeftOutlined
} from '@ant-design/icons';
import { Button, notification, Badge, type GetProp } from 'antd';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { MessageActions, UserMessageActions } from './MessageActions';

import createOpenAIClient from '../../utils/openaiClient';
import Sidebar from './Sidebar';
import PlaceholderNode from './PlaceholderNode';
import { senderPromptsItems, roles } from './constants';




import { v4 as uuidv4 } from 'uuid';
import styles from './Chat.module.css';
import {  SESSION_ENDPOINT, EVENT_ENDPOINT } from '../../Constant/RouterConstant';
import { fetchBulk, uploadFile } from '../api/api';
import 'highlight.js/styles/vs2015.css';
import MessageProcessor from './MessageProcessor';
import VirtualMachine from './VirtualMachine/index';
import './CustomSender.css'; // 导入自定义的Sender样式

import ExcelViewer from '../ExcelViewer';

type MessageStatus = 'local' | 'loading' | 'success' | 'error';
interface ConversationItem {
  id: string;
  messages: any[];
  title: string;
  time: string;
}

interface ChatMessage {
  id: string;
  message: string;
  status: MessageStatus;
}

// 定义附件项接口
interface AttachmentItem {
  content: string;
  url: string;
  filename: string;
}

// 扩展提示项接口，添加attachments字段
interface PromptItemWithAttachments {
  data: {
    description: React.ReactNode;
    [key: string]: any;
  };
  attachments?: AttachmentItem[];
  [key: string]: any;
}



const Chat: React.FC = () => {
  // 1. 从路由参数拿到 session_id 和 workflow_id，以及查询参数
  const { session_id = 'new', workflow_id='e740636a-e740-9728-12ac-713262e4312a' } = useParams();
  const [searchParams] = useSearchParams();
  const workflowOrAgent = searchParams.get('workflow_or_agent') || 'workflow';
  // 同时获取kb_id和mcp_id参数
  const kb_id = searchParams.get('kb_id');
  const mcp_id = searchParams.get('mcp_id');
  // console.log('路由参数:', { session_id, workflow_id, workflowOrAgent, kb_id, mcp_id });
  
  const navigate = useNavigate();
  // const location = useLocation();

  // 2. 用 state 来维护 activeKey，初始值为路由参数
  const [activeKey, setActiveKey] = useState<string>(session_id);
  // 使用ref保存activeKey的最新值，避免闭包陷阱
  const activeKeyRef = useRef<string>(session_id);
  
  // 更新activeKey时同步更新ref
  useEffect(() => {
    activeKeyRef.current = activeKey;
  }, [activeKey]);
  
  // 4. 其他必要的 state
  const [headerOpen, setHeaderOpen] = useState(false);
  const [content, setContent] = useState('');
  const [recording, setRecording] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<GetProp<typeof Attachments, 'items'>>([]);
  const [parseAttachments, setParseAttachments] = useState<Array<{content: string, url: string, filename: string}>>([]);
  // 使用ref保存parseAttachments的最新值
  const parseAttachmentsRef = useRef(parseAttachments);
  const [uploadResults, setUploadResults] = useState<Record<string, {content: string, url: string, filename: string}>>({});
  const [conversationItems, setItems] = useState<ConversationItem[]>([]);
  const [_shouldRefreshAttachments, setShouldRefreshAttachments] = useState(false);
  // 添加文件上传中状态
  const [isUploading, setIsUploading] = useState<boolean>(false);
  
  // 添加HTML/SVG代码块相关状态
  const [htmlSvgCodeBlocks, setHtmlSvgCodeBlocks] = useState<Array<{id: string, type: 'html' | 'svg', content: string}>>([]);
  // const [hasHtmlSvgCode, setHasHtmlSvgCode] = useState<boolean>(false);
  // 新增状态，用于跟踪用户点击的代码块ID
  const [_clickedCodeBlockId, setClickedCodeBlockId] = useState<string | null>(null);

  // 添加Excel文件相关状态
  const [excelFile, setExcelFile] = useState<{url: string, filename: string} | null>(null);
  const [showExcelViewer, setShowExcelViewer] = useState<boolean>(false);

  // 全局工具事件存储
  const [toolEvents, setToolEvents] = useState<Record<string, any>>({});
  
  // 虚拟机相关状态
  const [showVirtualMachine, setShowVirtualMachine] = useState<boolean>(false);
  const [virtualMachineData, setVirtualMachineData] = useState<{
    eventId: string;
    actionName: string;
    toolResult: any;
    toolInput: string;
    // 新增分组相关信息
    groupedEvents?: Array<{
      eventId: string;
      toolResult: any;
      toolInput: string;
      created_at: string;
    }>;
    currentPageIndex?: number;
    query?: string;
  } | null>(null);

  // 获取对话列表
  const fetchConversations = async () => {

    try {
      const response = await fetchBulk(`${SESSION_ENDPOINT}/bulk?workflow_id=${workflow_id}&sort_by=created_at&sort_order=desc`);
      
      // 将API返回的数据转换为ConversationItem格式
      const convertedData = response.data.map((item: any) => ({
        id: item.id,
        messages: [], // 初始化为空数组，实际消息会在点击会话时加载
        title: item.name, // 将name字段映射为title
        time: item.created_at // 将created_at映射为time
      }));
      
      setItems(convertedData);
      console.log(convertedData,'converted response.data')
      return convertedData;
    } catch (error) {
      console.error('获取对话列表失败:', error);
      notification.error({
        message: '错误',
        description: '获取对话列表失败'
      });
      return [];
    }
  };

  // 获取特定会话的消息
  const fetchConversationMessages = async (sessionId: string) => {
    try {
      const response = await fetchBulk(`${EVENT_ENDPOINT}/bulk?session_id=${sessionId}&sort_by=created_at&sort_order=desc`);
      console.log(response.data,'response.data111111')
      
      // 存储原始事件数据到toolEvents中
      const eventMap: Record<string, any> = {};
      response.data.forEach((event: any) => {
        eventMap[event.id] = event;
      });
      setToolEvents(eventMap);
      
      // 按query分组事件
      const groupedEvents = response.data.reduce((groups: any, event: any) => {
        const key = event.query;
        if (!groups[key]) {
          groups[key] = {
            query: key,
            events: [],
            firstEventTime: event.created_at
          };
        }
        groups[key].events.push(event);
        return groups;
      }, {});
      
      // 按第一个事件的时间对查询组进行排序
      const sortedGroups = Object.values(groupedEvents).sort((a: any, b: any) => {
        return new Date(a.firstEventTime).getTime() - new Date(b.firstEventTime).getTime();
      });
      
      // 生成消息列表
      const messages: any[] = [];
      sortedGroups.forEach((group: any, groupIndex: number) => {
        const { query, events } = group;
        
        // 添加用户消息
        messages.push({
          id: `user-${groupIndex}-${Date.now()}`,
          message: query,
          status: 'local' as const
        });
        
        // 对事件按时间排序（最早的在前）
        const sortedEvents = events.sort((a: any, b: any) => {
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        });
        
        // 添加AI消息，包含所有相关事件
        messages.push({
          id: `ai-${groupIndex}-${Date.now()}`,
          message: sortedEvents, // 传递事件数组，不是字符串
          status: 'success' as const
        });
      });
      
      setMessages(messages);
      return messages;
    } catch (error) {
      console.error('获取会话消息失败:', error);
      notification.error({
        message: '错误',
        description: '获取会话消息失败'
      });
      return [];
    }
  };

  // 初始化：获取对话列表并处理路由
  useEffect(() => {
    const init = async () => {
      try {
        console.log('初始化应用...');
        // 1. 清空原有状态
        setMessages([]);
        
        // 2. 获取对话列表
        const conversations = await fetchConversations();
        
        // 3. 根据路由参数确定当前会话
        if (session_id && session_id !== 'new') {
          // 检查请求的会话是否存在
          const existingConversation = conversations.find(
            (conv: ConversationItem) => conv.id === session_id
          );
          
          if (existingConversation) {
            console.log('加载现有会话:', session_id);
            // 设置当前会话ID
            setActiveKey(session_id);
            activeKeyRef.current = session_id;
            
            
            
            // 加载会话消息
            const messages = await fetchConversationMessages(session_id);
            setMessages(messages);
          } else {
            console.log('请求的会话不存在，重定向到新对话');
            // 会话不存在，重定向到新对话页面
            setActiveKey('new');
            activeKeyRef.current = 'new';
            
            // 导航时保留工作流ID（如果有）和工作流/智能体类型
            navigate(`/chat/${workflow_id}/new?workflow_or_agent=${workflowOrAgent}`, { replace: true });
          }
        } else {
          console.log('进入新会话模式');
          // 新会话模式
          setActiveKey('new');
          activeKeyRef.current = 'new';
        }
      } catch (error) {
        console.error('初始化失败:', error);
        notification.error({
          message: '错误',
          description: '加载对话失败，请刷新页面重试'
        });
      }
    };

    init();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  // 监听路由参数变化
  useEffect(() => {
    // 只在session_id确实发生变化时进行处理
    if (session_id === activeKey) return;
    
    console.log('路由参数变化:', session_id, '当前activeKey:', activeKey);
    
    // 对话切换时总是先清空消息列表，避免显示上一个对话的内容
    setMessages([]);
    
          // 切换会话时重置HTML/SVG代码块状态
      setHtmlSvgCodeBlocks([]);
      // setHasHtmlSvgCode(false);
      setClickedCodeBlockId(null); // 重置点击的代码块ID
    
    // 当URL参数为new时，重置状态
    if (session_id === 'new') {
      console.log('切换到新对话');
      setActiveKey('new');
      activeKeyRef.current = 'new';
    } 
    // 如果是切换到其他有效会话，加载该会话的消息
    else if (session_id !== 'new') {
      console.log('切换到会话:', session_id);
      const loadConversation = async () => {
        try {
          // 检查会话是否存在
          const conversationExists = conversationItems.some(item => item.id === session_id);
          
          if (conversationExists) {
            // 已存在的会话，加载消息
            console.log('加载现有会话消息');
            setActiveKey(session_id);
            activeKeyRef.current = session_id;
            
            // 加载新会话的消息
            const messages = await fetchConversationMessages(session_id);
            setMessages(messages);
          } else {
            // 会话不存在，重定向到新会话
            console.log('会话不存在，重定向到新对话');
            setActiveKey('new');
            activeKeyRef.current = 'new';
            
            // 导航到新会话页面，保留工作流ID（如果有）和工作流/智能体类型
            navigate(`/chat/${workflow_id}/new?workflow_or_agent=${workflowOrAgent}`, { replace: true });
          }
        } catch (error) {
          console.error('加载会话失败:', error);
          notification.error({
            message: '错误',
            description: '加载会话消息失败'
          });
        }
      };
      
      loadConversation();
    }
  }, [session_id]);

  // 修改导航方法，保留workflow_or_agent参数
  // const navigateWithParams = (path: string) => {
  //   navigate(`${path}${path.includes('?') ? '&' : '?'}workflow_or_agent=${workflowOrAgent}`);
  // };

  // 使用 useXAgent 来处理请求
  const [agent] = useXAgent({
    request: async (info, callbacks) => {
      const { message } = info;
      const { onSuccess, onUpdate, onError } = callbacks;

      try {
        // 使用ref获取最新的activeKey值
        let currentSessionId = activeKeyRef.current;
        
        // 如果是新会话，先生成新的 UUID
        if (!currentSessionId || currentSessionId === 'new') {
          // 创建新会话ID
          const newId = uuidv4();
          
          // 创建新会话项目
          const newConversationItem: ConversationItem = {
            id: newId,
            messages: [],
            title: message ? message.slice(0, 30) + (message.length > 30 ? '...' : '') : '新对话',
            time: Date.now().toString(), // 使用当前时间戳作为time字段
          };
          
          // 更新会话列表 - 将新会话添加到列表首位
          setItems(prevItems => [newConversationItem, ...prevItems]);
          
          // 更新活动会话ID
          setActiveKey(newId);
          activeKeyRef.current = newId;
          currentSessionId = newId;
          
          // 导航到新会话页面，保留工作流ID（如果有）和工作流/智能体类型，以及kb_id或mcp_id参数
          let url = `/chat/${workflow_id}/${newId}?workflow_or_agent=${workflowOrAgent}`;
          if (kb_id) url += `&kb_id=${kb_id}`;
          if (mcp_id) url += `&mcp_id=${mcp_id}`;
          navigate(url, { replace: true });
        }

        // 验证消息和会话 ID
        if (!message || !currentSessionId) {
          throw new Error('消息或会话ID未定义');
        }

        // 构建聊天内容并发送请求 - 使用ref获取最新的parseAttachments
        const chat_content: {
          content: string;
          session_id: string;
          attachments: Array<{content: string, url: string, filename: string}>;
          workflow_or_agent: string;
          source: string;
          kb_id?: string;
          mcp_id?: string;
        } = {
          content: message,
          session_id: currentSessionId,
          attachments: parseAttachmentsRef.current,
          workflow_or_agent: workflowOrAgent,
          source: "flint-chat"
        };
        
        // 如果参数存在，则添加到chat_content中
        if (kb_id) {
          chat_content.kb_id = kb_id;
        }
        
        if (mcp_id) {
          chat_content.mcp_id = mcp_id;
        }
        
        console.log(chat_content,'chat_content');
        const chatContentString = JSON.stringify(chat_content);
        
        // 动态创建OpenAI客户端，确保使用最新的token
        const client = createOpenAIClient();
        console.log(workflow_id,[{ role: 'user', content: chatContentString }],"[{ role: 'user', content: chatContentString }]")
        const stream = await client.chat.completions.create({
          model: workflow_id, // 使用动态模型名称
          messages: [{ role: 'user', content: chatContentString }],
          stream: true,
        });

        let accumulatedContent = '';
        for await (const chunk of stream) {
          const deltaContent = chunk.choices[0]?.delta?.content || '';
          if (deltaContent) {
            accumulatedContent += deltaContent;
            onUpdate(accumulatedContent);
          }
        }
        onSuccess(accumulatedContent);
      } catch (error) {
        notification.error({
          message: '错误',
          description: '请求处理过程中发生错误',
        });
        onError(error instanceof Error ? error : new Error('请求失败'));
      }
    },
  });

  // 使用 useXChat 来维护消息列表
  const { onRequest, messages, setMessages } = useXChat({ 
    agent
  });

  // ============== Event Handlers ==============
  /** 新增一个会话 */
  const onAddConversation = () => {
    // 清空消息列表
    setMessages([]);
    
    // 设置activeKey为'new'
    setActiveKey('new');
    activeKeyRef.current = 'new';
    
    // 重置HTML/SVG代码块状态
    setHtmlSvgCodeBlocks([]);
    // setHasHtmlSvgCode(false);
    setClickedCodeBlockId(null); // 重置点击状态
    
    // 构建URL，保留所有查询参数
    let url = `/chat/${workflow_id}/new?workflow_or_agent=${workflowOrAgent}`;
    if (kb_id) url += `&kb_id=${kb_id}`;
    if (mcp_id) url += `&mcp_id=${mcp_id}`;
    navigate(url);
  };

  /** 发送消息 */
  const handleSubmit = async (nextContent: string) => {
    if (!nextContent) return;
    
    // 检查workflow_id是否为"new"
    if (workflow_id === 'new') {
      notification.info({
        message: '请选择工作流',
        description: '请从左侧选择一个工作流、智能体、知识库或数据集开始对话',
        duration: 4,
      });
      return;
    }
    
    onRequest(nextContent);
    setContent('');
    setAttachedFiles([]);
    setUploadResults({});
    setHeaderOpen(false);
  };

  /** 让 AI 重新回答 */
  const handleRegenerateAIResponse = async (messageIndex: number) => {
    // 查找最近的用户消息
    let userMessageContent = '';
    for (let i = messageIndex - 1; i >= 0; i--) {
      if (messages[i].status === 'local') {
        userMessageContent = messages[i].message;
        break;
      }
    }

    // 如果找不到用户消息，显示错误
    if (!userMessageContent) {
      notification.error({
        message: '操作失败',
        description: '找不到原始用户消息，无法重新生成回复'
      });
      return;
    }

    // 保留当前需要重试之前的消息
    const updatedMessages = messages.slice(0, messageIndex);
    setMessages(updatedMessages);
    
    // 重新发送请求
    onRequest(userMessageContent);
  };

  /** 点击提示气泡的选项 */
  const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
    console.log('提示气泡点击信息:', JSON.stringify(info, null, 2));
    
    // 使用类型断言处理可能的attachments属性
    const infoWithAttachments = info as unknown as PromptItemWithAttachments;
    
    // 如果info中包含attachments，临时设置parseAttachments以便在chat_content中使用
    if (infoWithAttachments.attachments) {
      // console.log('接收到附件信息:', infoWithAttachments.attachments);
      setParseAttachments(infoWithAttachments.attachments);
      parseAttachmentsRef.current = infoWithAttachments.attachments;
      
      // 为了调试，在聊天界面展示附件信息
      // notification.info({
      //   message: '接收到附件',
      //   description: `接收到${infoWithAttachments.attachments.length}个附件`
      // });
    }
    
    // 确保description是字符串类型
    const description = String(info.data.description || '');
    onRequest(description);
  };

  /** 点击左侧会话列表，切换到对应会话 */
  const onConversationClick = (key: string) => {
    // 和原来的逻辑保持一致，只是在导航时添加参数
    if (key === 'new') {
      setMessages([]);
      setActiveKey('new');
      activeKeyRef.current = 'new';
              // 重置HTML/SVG代码块状态
        setHtmlSvgCodeBlocks([]);
        //  setHasHtmlSvgCode(false);
        setClickedCodeBlockId(null); // 重置点击状态
      
      // 构建URL，保留所有查询参数
      let url = `/chat/${workflow_id}/new?workflow_or_agent=${workflowOrAgent}`;
      if (kb_id) url += `&kb_id=${kb_id}`;
      if (mcp_id) url += `&mcp_id=${mcp_id}`;
      navigate(url);
    } else if (key !== activeKey) {
      setMessages([]);
      setActiveKey(key);
      activeKeyRef.current = key;
      // 重置HTML/SVG代码块状态
      setHtmlSvgCodeBlocks([]);
      // setHasHtmlSvgCode(false);
      setClickedCodeBlockId(null); // 重置点击状态
      
      // 构建URL，保留所有查询参数
      let url = `/chat/${workflow_id}/${key}?workflow_or_agent=${workflowOrAgent}`;
      if (kb_id) url += `&kb_id=${kb_id}`;
      if (mcp_id) url += `&mcp_id=${mcp_id}`;
      navigate(url);
    }
  };
  
  /** Conversation 组件用到的更新消息逻辑 */
  const handleMessagesUpdate = (messages: ChatMessage[]) => {
    // 重要：强制使用空数组来清空消息
    if (messages.length === 0) {
      console.log('清空消息列表');
      setMessages([]);
      return;
    }
    
    console.log('更新消息列表:', messages.length);
    setMessages(messages);
  };

  /** 上传附件回调 */
  const handleFileChange: GetProp<typeof Attachments, 'onChange'> = async (info) => {
    console.log(info.fileList,'info.fileList');
    
    // 通过对比文件列表长度来判断是添加还是删除
    const prevLength = attachedFiles.length;
    const currentLength = info.fileList.length;
    
    // 更新attachedFiles
    setAttachedFiles(info.fileList);
    
    // 如果是删除文件并且当前正在显示Excel预览，检查是否需要关闭预览
    if (currentLength < prevLength && showExcelViewer) {
      const excelFilename = excelFile?.filename;
      // 检查被删除的文件是否是当前正在预览的Excel文件
      const excelFileStillExists = info.fileList.some(file => file.name === excelFilename);
      if (!excelFileStillExists) {
        setShowExcelViewer(false);
        setExcelFile(null);
      }
    }
    
    // 添加了新文件
    if (currentLength > prevLength) {
      // 找出新添加的文件
      const prevFilenames = attachedFiles.map(file => file.name);
      const newFiles = info.fileList.filter(file => !prevFilenames.includes(file.name));
      
      if (newFiles.length > 0) {
        // 设置上传状态为 true
        setIsUploading(true);
        
        // 创建上传任务数组
        const uploadTasks = newFiles
          .filter(file => file.originFileObj)
          .map(async (file) => {
        try {
              const fileObj = file.originFileObj!;
          notification.info({
            message: '上传中',
                description: `正在上传文件：${fileObj.name}`,
            duration: 2,
          });
          
          // 上传文件
              const result = await uploadFile(fileObj);
          
          // 保存上传结果，使用文件名作为键
          setUploadResults(prev => ({
            ...prev,
                [fileObj.name]: {
              content: result.content || '',
              url: result.url || '',
                  filename: result.filename || fileObj.name
            }
          }));
          
          // 检查是否是Excel文件
          const isExcel = fileObj.name.match(/\.(xlsx|xls|csv)$/i);
          if (isExcel && result.url) {
            setExcelFile({
              url: result.url,
              filename: result.filename || fileObj.name
            });
            setShowExcelViewer(true);
          }
          
          notification.success({
            message: '上传成功',
                description: `文件 ${fileObj.name} 上传成功`,
            duration: 2,
          });
              
              return { success: true, filename: fileObj.name };
        } catch (error) {
              console.error(`文件 ${file.name} 上传失败:`, error);
          notification.error({
            message: '上传失败',
                description: `文件 ${file.name} 上传失败，请重试`,
            duration: 2,
          });
              
              return { success: false, filename: file.name };
            }
          });
        
        // 等待所有上传任务完成
        Promise.all(uploadTasks).finally(() => {
          // 所有文件上传完成后，设置上传状态为 false
          setIsUploading(false);
        });
      }
    }
  };

  // 使用useEffect来管理parseAttachments
  useEffect(() => {
    // 从当前文件列表和上传结果生成parseAttachments
    const newParseAttachments = attachedFiles
      .filter(file => uploadResults[file.name]) // 只包含已上传的文件
      .map(file => uploadResults[file.name]);   // 获取对应的上传结果
    
    setParseAttachments(newParseAttachments);
    parseAttachmentsRef.current = newParseAttachments; // <-- 更新 Ref
    // console.log(newParseAttachments, 'parseAttachments');
    
  }, [attachedFiles, uploadResults]);

  /** 开始/停止录音 */
  const handleRecordingChange = (nextRecording: boolean) => {
    setRecording(nextRecording);
    notification.info({
      message: `${nextRecording ? '开始' : '停止'}录音`,
    });
  };

  /** 复制消息内容 */
  const handleCopyMessage = (message: string) => {
    navigator.clipboard.writeText(message)
      .then(() => {
        notification.success({
          message: '成功',
          description: '内容已复制到剪贴板',
          duration: 2,
        });
      })
      .catch(() => {
        notification.error({
          message: '复制失败',
          description: '无法复制内容到剪贴板，请手动选择并复制',
          duration: 2,
        });
      });
  };

  // ============== 监控messages变化 ==============
  useEffect(() => {
    if (messages.length >= 2) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.status === 'success' || lastMessage.status === 'error') {
        // 每次成功生成回复时都尝试刷新参考资料，不再使用关键词检查
        setShouldRefreshAttachments(true);
      } else if (lastMessage.status === 'loading') {
        // 重置信号
        setShouldRefreshAttachments(false);
      }
    }
  }, [messages]);

  // 处理HTML/SVG代码块检测
  const handleHtmlSvgCodeDetected = (detectedBlocks: {id: string, type: 'html' | 'svg', content: string}[], isUserClick?: boolean) => {
    // console.log(detectedBlocks,"detectedBlocks");
    if (!isUserClick) {
      // 来自自动扫描，需要合并和去重
      console.log(`自动扫描检测到 ${detectedBlocks.length} 个潜在代码块`);

      // 使用Map进行合并和去重（基于内容）
      const codeBlocksMap = new Map<string, {id: string, type: 'html' | 'svg', content: string}>();
      
      // 1. 先添加当前状态中已有的代码块
      htmlSvgCodeBlocks.forEach(block => {
        const contentKey = `${block.type}:${block.content}`;
        if (!codeBlocksMap.has(contentKey)) {
          codeBlocksMap.set(contentKey, block);
        }
      });

      // 2. 再尝试添加新检测到的代码块
      let hasNewBlocks = false;
      detectedBlocks.forEach(newBlock => {
        const contentKey = `${newBlock.type}:${newBlock.content}`;
        if (!codeBlocksMap.has(contentKey)) {
          // console.log('添加新的唯一代码块:', newBlock.id);
          codeBlocksMap.set(contentKey, newBlock);
          hasNewBlocks = true;
        } else {
          console.log('忽略重复内容的代码块:', newBlock.id);
        }
      });

      // 3. 转换回数组并限制数量
      let updatedBlocks = Array.from(codeBlocksMap.values());
      const maxCodeBlocks = 100;
      if (updatedBlocks.length > maxCodeBlocks) {
        console.warn(`HTML/SVG代码块数量(${updatedBlocks.length})超过${maxCodeBlocks}，将截取最新的${maxCodeBlocks}个`);
        updatedBlocks = updatedBlocks.slice(-maxCodeBlocks);
      }

      // 4. 检查状态是否真的改变了
      const stateChanged = 
        htmlSvgCodeBlocks.length !== updatedBlocks.length || 
        !htmlSvgCodeBlocks.every(oldBlock => 
          updatedBlocks.some(newBlock => 
            oldBlock.content === newBlock.content && oldBlock.type === newBlock.type
          )
        );

      if (stateChanged) {
        console.log(`更新累积HTML/SVG代码块：从 ${htmlSvgCodeBlocks.length} 个变为 ${updatedBlocks.length} 个`);
        setHtmlSvgCodeBlocks(updatedBlocks);
        // setHasHtmlSvgCode(updatedBlocks.length > 0);
        
        // 新检测到代码块时的处理
        if (hasNewBlocks && updatedBlocks.length > 0) {
          // 选中最新的代码块
          const latestBlock = updatedBlocks[updatedBlocks.length - 1];
          setClickedCodeBlockId(null); // 先重置
          setTimeout(() => setClickedCodeBlockId(latestBlock.id), 10);
        }
      } else {
        // console.log('累积代码块无变化，跳过状态更新');
      }

          } else if (detectedBlocks.length > 0) {
        // 来自用户点击
        const clickedBlock = detectedBlocks[0];
        
        // 查找是否已存在（基于内容）
        const existingBlock = htmlSvgCodeBlocks.find(
          block => block.content === clickedBlock.content && block.type === clickedBlock.type
        );
        
        if (existingBlock) {
          // 如果已存在，使用已存在的ID来触发更新
          setClickedCodeBlockId(null); // 先重置
          setTimeout(() => setClickedCodeBlockId(existingBlock.id), 10);
        } else {
          // 如果是新的，添加到列表并设置ID
          const newBlocks = [...htmlSvgCodeBlocks, clickedBlock];
          setHtmlSvgCodeBlocks(newBlocks);
          setClickedCodeBlockId(null); // 先重置
          setTimeout(() => setClickedCodeBlockId(clickedBlock.id), 10);
        }
      }
  };

  // 处理工具事件点击
  const handleToolEventClick = (eventId: string, actionName: string, toolResult: any, toolInput: string) => {
    // 对于HTML和SVG类型的事件，收集同一query下的相同类型事件
    if (actionName === 'html' || actionName === 'svg') {
      try {
        // 找到当前点击的事件
        const currentEvent = toolEvents[eventId];
        if (!currentEvent) {
          console.warn('找不到当前事件:', eventId);
          return;
        }

        const currentQuery = currentEvent.query;
        
        // 收集同一query下的相同类型事件
        const filteredEvents = Object.values(toolEvents)
          .filter((event: any) => 
            event.query === currentQuery && 
            event.event_payload?.action_name === actionName
          )
          .sort((a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
          .map((event: any) => ({
            eventId: event.id,
            toolResult: event.event_payload?.content?.tool_result || '',
            toolInput: event.event_payload?.content?.input || event.query || '',
            created_at: event.created_at
          }));

        // 找到当前事件在分组中的索引
        const currentPageIndex = filteredEvents.findIndex(e => e.eventId === eventId);

        console.log(`${actionName.toUpperCase()}事件分组:`, {
          query: currentQuery,
          totalEvents: filteredEvents.length,
          currentPageIndex,
          events: filteredEvents
        });

        setVirtualMachineData({
          eventId,
          actionName,
          toolResult,
          toolInput,
          groupedEvents: filteredEvents,
          currentPageIndex: Math.max(0, currentPageIndex),
          query: currentQuery
        });
      } catch (error) {
        console.error(`处理${actionName}事件分组失败:`, error);
        // 降级到单个事件显示
        setVirtualMachineData({
          eventId,
          actionName,
          toolResult,
          toolInput
        });
      }
    } else {
      // 非HTML/SVG事件，保持原有逻辑
      setVirtualMachineData({
        eventId,
        actionName,
        toolResult,
        toolInput
      });
    }
    
    setShowVirtualMachine(true);
  };

  // 关闭虚拟机
  const closeVirtualMachine = () => {
    setShowVirtualMachine(false);
    setVirtualMachineData(null);
  };

  // 跳转到最新进展
  const jumpToLatestProgress = () => {
    try {
      const toolEventsList = Object.values(toolEvents).filter(event => 
        event.event_type === 'tool_call' // 只考虑 tool_call 事件，不包括 chat.completion 事件
      ).sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      
      if (toolEventsList.length > 0) {
        const latestEvent = toolEventsList[toolEventsList.length - 1];
        const actionName = latestEvent.event_payload?.action_name || 'unknown';
        const toolResult = latestEvent.event_payload?.content?.tool_result || '';
        const query = latestEvent.query || '';
        const toolInput = latestEvent.event_payload?.content?.input || query;
        
        console.log('跳转到最新进展:', {
          eventId: latestEvent.id,
          actionName,
          query
        });
        
        handleToolEventClick(
          latestEvent.id,
          actionName,
          toolResult,
          toolInput
        );
      } else {
        console.warn('没有找到工具事件');
      }
    } catch (error) {
      console.error('跳转到最新进展时发生错误:', error);
    }
  };

  // ============== 渲染消息列表 ==============
  const items : GetProp<typeof Bubble.List, 'items'> = messages.length > 0
  ? messages.map(({ id, message, status }, index) => ({
      key: id,
      role: status === 'local' ? 'local' : 'ai',
      content: (
        <MessageProcessor
          content={message}
          role={status === 'local' ? 'local' : 'ai'}
          onHtmlSvgCodeDetected={handleHtmlSvgCodeDetected}
          isStreaming={status === 'loading'}
          toolEvents={toolEvents}
          onToolEventClick={handleToolEventClick}
        />
      ),
      footer: status === 'local' ? (
        <UserMessageActions
          index={index}
          message={message}
          loading={false}
          onRegenerate={() => {}}
          onCopy={handleCopyMessage}
        />
      ) : (
        <MessageActions
          index={index}
          message={message}
          loading={agent.isRequesting()}
          onRegenerate={handleRegenerateAIResponse}
          onCopy={handleCopyMessage}
        />
      ),
    }))
  : [
      {
        content: <PlaceholderNode onPromptsItemClick={onPromptsItemClick} />,
        variant: 'borderless',
      },
    ];

  // ===================== prefix（回形针）=====================
  const attachmentsPrefix = (
    <Badge dot={attachedFiles.length > 0 && !headerOpen}>
      <Button
        type="text"
        icon={<PaperClipOutlined />}
        onClick={() => setHeaderOpen(!headerOpen)}
      />
    </Badge>
  );

  // ==================== senderHeader（附件面板） ====================
  const senderHeader = (
    <Sender.Header
      title="附件"
      open={headerOpen}
      onOpenChange={setHeaderOpen}
      styles={{
        content: {
          padding: 0,
        },
      }}
    >
      <Attachments
        beforeUpload={() => false}
        items={attachedFiles}
        onChange={handleFileChange}
        multiple={true}
        placeholder={(type) =>
          type === 'drop'
            ? { title: '删除文件' }
            : {
                icon: <CloudUploadOutlined />,
                title: '文件上传',
                description: '点击上传/拖拽文件',
              }
        }
      />
    </Sender.Header>
  );
  
  // 添加复制功能
  useEffect(() => {
    // 添加复制函数到 window 对象
    (window as any).copyCode = (id: string) => {
      const codeElement = document.getElementById(id);
      if (codeElement) {
        const text = codeElement.textContent || '';
        navigator.clipboard.writeText(text).then(() => {
          notification.success({
            message: '复制成功',
            description: '代码已复制到剪贴板',
            duration: 2,
          });
        }).catch(() => {
          notification.error({
            message: '复制失败',
            description: '请手动选择并复制代码',
            duration: 2,
          });
        });
      }
    };
  }, []);




  // 添加切换Excel预览功能
  const toggleExcelViewer = () => {
    setShowExcelViewer(!showExcelViewer);
  };

  return (
    <div className={`${styles.layout} ${showVirtualMachine ? styles.layoutWithVirtualMachine : ''}`}>
      {/* 左侧菜单 */}
      <Sidebar 
        conversationItems={conversationItems}
        activeKey={activeKey}
        workflowId={workflow_id}
        onConversationClick={onConversationClick}
        onAddConversation={onAddConversation}
        onMessagesUpdate={handleMessagesUpdate}
        setItems={setItems}
        collapsed={showVirtualMachine} // 当虚拟机显示时收缩sidebar
        fetchConversationMessages={fetchConversationMessages}
      />

      {/* 聊天区域 - 根据是否显示Excel预览或虚拟机调整宽度 */}
      <div className={`${styles.chat} ${showExcelViewer ? styles.chatWithExcel : ''} ${showVirtualMachine ? styles.chatWithVirtualMachine : ''}`}>
        {/* 消息列表 */}
        <Bubble.List items={items} roles={roles} className={styles.messages} />

        {/* 如果没有 activeKey，就显示提示信息 */}
        {!activeKey && (
          <Prompts items={senderPromptsItems} onItemClick={onPromptsItemClick} />
        )}

        {/* 发送器 */}
        <Sender
          value={content}
          onSubmit={handleSubmit}
          onChange={setContent}
          loading={agent.isRequesting()}
          disabled={isUploading} // 在上传文件时禁用发送按钮
          className={styles.sender}
          allowSpeech={{
            recording,
            onRecordingChange: handleRecordingChange,
          }}
          prefix={attachmentsPrefix}
          header={senderHeader}
        />
      </div>

      {/* Excel预览区域 */}
      {showExcelViewer && excelFile && (
        <div className={styles.excelViewerContainer}>
          <div className={styles.excelViewerHeader}>
            <Button 
              type="text" 
              icon={<CloseOutlined />} 
              onClick={toggleExcelViewer}
              className={styles.closeExcelButton}
            />
          </div>
          <ExcelViewer fileUrl={excelFile.url} fileName={excelFile.filename} />
        </div>
      )}





      {/* 虚拟机组件 */}
      {showVirtualMachine && virtualMachineData && (
        <VirtualMachine
          visible={showVirtualMachine}
          onClose={closeVirtualMachine}
          actionName={virtualMachineData.actionName}
          toolResult={virtualMachineData.toolResult}
          toolInput={virtualMachineData.toolInput}
          eventId={virtualMachineData.eventId}
          toolEvents={toolEvents}
          onJumpToLatest={jumpToLatestProgress}
          groupedEvents={virtualMachineData.groupedEvents}
          currentPageIndex={virtualMachineData.currentPageIndex}
          query={virtualMachineData.query}
        />
      )}
    </div>
  );
};

export default Chat;
