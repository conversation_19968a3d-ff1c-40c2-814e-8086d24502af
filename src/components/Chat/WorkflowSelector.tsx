import React, { useState, useEffect } from 'react';
import { Modal, Spin, Tooltip, Empty, message } from 'antd';
import { WORKFLOW_ENDPOINT } from '../../Constant/RouterConstant';
import { CHAT_API_NAME } from '../../Constant/Constant';
import { fetchBulk } from '../api/api';
import styles from './WorkflowSelector.module.css';
import SafeTooltip from '../common/SafeTooltip';
import { useSelector } from 'react-redux';
import { ShareAltOutlined } from '@ant-design/icons';

interface WorkflowType {
  type: string;
  description: string;
  icon: string;
}

// 工作流图标组件
const TypeIcon: React.FC<{ iconSvg: string }> = ({ iconSvg }) => {
  return (
    <div dangerouslySetInnerHTML={{ __html: iconSvg }} />
  );
};

interface Workflow {
  id: string;
  name: string;
  description: string;
  workflow_type: string;
  is_active: boolean;
  is_published: boolean;
  username?: string; // 添加username字段，表示工作流所属用户
}

interface WorkflowSelectorProps {
  visible: boolean;
  onClose: () => void;
  username: string;
}

const WorkflowSelector: React.FC<WorkflowSelectorProps> = ({ visible, onClose, username }) => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [workflowTypes, setWorkflowTypes] = useState<WorkflowType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  
  // 获取当前登录用户的信息
  const { userInfo } = useSelector((state: any) => state.user);
  const currentUsername = userInfo?.username; // 当前登录用户名

  // 获取工作流列表和工作流类型
  useEffect(() => {
    if (visible) {
      fetchWorkflows();
      fetchWorkflowTypes();
    }
  }, [visible, username]);

  const fetchWorkflows = async () => {
    setLoading(true);
    try {
      const endpoint = `${WORKFLOW_ENDPOINT}/bulk?username=${username}&is_published=true`;
      const result = await fetchBulk(endpoint);
      console.log(result);
      setWorkflows(result.data);
    } catch (error) {
      console.error('获取工作流列表失败:', error);
      message.error('获取工作流列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取工作流类型
  const fetchWorkflowTypes = async () => {
    try {
      const response = await fetch(`${WORKFLOW_ENDPOINT}/workflow_types`);
      if (response.ok) {
        const { data } = await response.json();
        const filteredData = data.filter((item: WorkflowType) => item.type !== '通用智能体');
        setWorkflowTypes(filteredData);
      } else {
        console.error('获取工作流类型失败');
        message.error('获取工作流类型失败');
      }
    } catch (error) {
      console.error('获取工作流类型失败:', error);
      message.error('获取工作流类型失败');
    }
  };

  // 按类型分组工作流
  const getWorkflowsByType = (type: string) => {
    return workflows.filter(workflow => workflow.workflow_type === type);
  };

  // 获取有数据的工作流类型
  const getTypesWithData = () => {
    const typesWithData = workflowTypes.filter(category => 
      workflows.some(workflow => workflow.workflow_type === category.type)
    );
    return typesWithData;
  };

  // 判断工作流是否属于当前登录用户
  const isCurrentUserWorkflow = (workflow: Workflow) => {
    return !workflow.username || workflow.username === currentUsername;
  };

  // 处理工作流点击，跳转到聊天页面
  const handleWorkflowClick = (workflow: Workflow) => {
    // 先关闭弹窗
    onClose();
    
    // 构建URL
    const url = `${CHAT_API_NAME}/${workflow.id}/new?workflow_or_agent=workflow`;
    
    // 使用window.location.href进行导航，这会导致页面完全刷新
    window.location.href = url;
    
    // 不再使用React Router的navigate，因为它不会重新加载组件
    // navigate(url);
  };

  return (
    <Modal
      title="工作流选择"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      className={styles.workflowSelectorModal}
    >
      <Spin spinning={loading}>
        <div className={styles.workflowCategoriesContainer}>
          {getTypesWithData().length > 0 ? (
            getTypesWithData().map((category, index) => (
              <div key={index} className={styles.categorySection}>
                <div className={styles.categoryHeader}>
                  <div className={styles.categoryIcon}>
                    <TypeIcon iconSvg={category.icon} />
                  </div>
                  <h3 className={styles.categoryTitle}>{category.type}</h3>
                </div>
                
                <Tooltip title={category.description} getPopupContainer={triggerNode => triggerNode.parentElement || document.body}>
                  <div className={styles.categoryDescription}>
                    {category.description}
                  </div>
                </Tooltip>
                
                <div className={styles.workflowCardGrid}>
                  {getWorkflowsByType(category.type).map(workflow => {
                    // 确定样式类：区分当前用户和其他用户的工作流
                    const isCurrentUser = isCurrentUserWorkflow(workflow);
                    const itemClassName = `${styles.workflowItem} ${
                      !isCurrentUser ? styles.workflowItemOtherUser : ''
                    }`;
                    
                    return (
                      <SafeTooltip 
                        key={workflow.id} 
                        title={
                          <div>
                            <div>{workflow.description}</div>
                            {!isCurrentUser && workflow.username && (
                              <div style={{ marginTop: 8, color: '#faad14' }}>
                                所有者: {workflow.username}
                              </div>
                            )}
                          </div>
                        } 
                        getPopupContainer={triggerNode => triggerNode.parentElement || document.body}
                      >
                        <div 
                          className={itemClassName}
                          onClick={() => handleWorkflowClick(workflow)}
                        >
                          {workflow.name}
                          {!isCurrentUser && (
                            <span style={{ 
                              position: 'absolute', 
                              top: 4, 
                              left: 4, 
                              fontSize: 10, 
                              backgroundColor: '#faad14', 
                              color: 'white',
                              padding: '1px 4px',
                              borderRadius: 4,
                              opacity: 0.8
                            }}>
                              <ShareAltOutlined /> 共享
                            </span>
                          )}
                        </div>
                      </SafeTooltip>
                    );
                  })}
                </div>
              </div>
            ))
          ) : (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE} 
              description="暂无可用的工作流" 
              className={styles.emptyWorkflow}
            />
          )}
        </div>
      </Spin>
    </Modal>
  );
};

export default WorkflowSelector; 