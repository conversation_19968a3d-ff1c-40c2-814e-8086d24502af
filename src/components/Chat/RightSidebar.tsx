import React, { useState, useEffect } from 'react';
import styles from './RightSidebar.module.css';
import SafeTooltip from '../common/SafeTooltip'; // 导入安全的Tooltip组件
import WorkflowCodeBlockProcessor, { WorkflowCodeBlock } from '../../utils/WorkflowCodeBlockProcessor';

// 导入图标组件
import { 
  FileTextOutlined, 
  CodeOutlined, 
  OrderedListOutlined
} from '@ant-design/icons';

interface RightSidebarProps {
  sessionId: string;
  activeTab: string;
  onTabChange: (tabKey: string) => void;
  shouldRefreshAttachments: boolean;
  htmlSvgCodeBlocks: Array<{id: string, type: 'html' | 'svg', content: string}>;
  // hasHtmlSvgCode: boolean;
  clickedCodeBlockId?: string | null;
}

const RightSidebar: React.FC<RightSidebarProps> = ({
  sessionId,
  activeTab,
  onTabChange,
  shouldRefreshAttachments,
  htmlSvgCodeBlocks,
  // hasHtmlSvgCode,
//   clickedCodeBlockId
}) => {
  // WorkflowGraph代码块状态
  const [workflowCodeBlocks, setWorkflowCodeBlocks] = useState<WorkflowCodeBlock[]>([]);
  const [workflowCodeLoading, setWorkflowCodeLoading] = useState<boolean>(false);

  // 获取WorkflowGraph代码块数量
  const fetchWorkflowCodeBlocksCount = async (sessionId: string) => {
    if (!sessionId || sessionId === 'new') {
      setWorkflowCodeBlocks([]);
      return;
    }

    try {
      setWorkflowCodeLoading(true);
      
      // 使用WorkflowCodeBlockProcessor获取代码块
      const codeBlocks = await WorkflowCodeBlockProcessor.getCodeBlocksBySessionId(sessionId);
      
      // // 去重和限制数量
      // codeBlocks = WorkflowCodeBlockProcessor.deduplicateCodeBlocks(codeBlocks);
      // codeBlocks = WorkflowCodeBlockProcessor.limitCodeBlocks(codeBlocks, 50);
      
      console.log(`RightSidebar获取到${codeBlocks.length}个WorkflowGraph代码块`);
      setWorkflowCodeBlocks(codeBlocks);
      
    } catch (error) {
      console.error('RightSidebar获取WorkflowGraph代码块失败:', error);
      setWorkflowCodeBlocks([]);
    } finally {
      setWorkflowCodeLoading(false);
    }
  };

  // 当sessionId变化时获取代码块数量
  useEffect(() => {
    if (sessionId && sessionId !== 'new') {
      fetchWorkflowCodeBlocksCount(sessionId);
    } else {
      setWorkflowCodeBlocks([]);
    }
  }, [sessionId]);

  // 处理标签页切换
  const handleTabChange = (tabKey: string) => {
    // 如果点击的是文件预览标签页，触发数据刷新
    if (tabKey === 'code') {
      fetchWorkflowCodeBlocksCount(sessionId);
    }
    onTabChange(tabKey);
  };

  // 计算总代码块数量（优先使用WorkflowGraph的数据）
  const totalCodeBlocks = workflowCodeBlocks.length > 0 ? workflowCodeBlocks.length : htmlSvgCodeBlocks.length;
  const hasAnyCode = totalCodeBlocks > 0;

  // 侧边栏选项
  const tabs = [
    {
      key: 'workflow',
      title: '工作流步骤',
      icon: <OrderedListOutlined />,
      badge: false,
      tooltip: '查看工作流步骤',
      disabled: false
    },
    {
      key: 'attachment',
      title: '参考资料',
      icon: <FileTextOutlined />,
      badge: shouldRefreshAttachments ? 1 : false,
      tooltip: '查看参考资料',
      disabled: false
    },
    {
      key: 'code',
      title: '文件预览',
      icon: <CodeOutlined />,
      badge: workflowCodeLoading ? '...' : (hasAnyCode ? totalCodeBlocks : false),
      tooltip: workflowCodeLoading ? '正在加载代码块...' : '点击查看文件预览' + (hasAnyCode ? `（${totalCodeBlocks}个代码块）` : ''),
      disabled: false // 始终可点击
    }
  ];

  return (
    <div className={styles.rightSidebar}>
      {/* 侧边栏导航 */}
      <div className={styles.navigation}>
        {tabs.map(tab => (
          <SafeTooltip 
            key={tab.key} 
            title={tab.tooltip} 
            placement="left"
          >
            <div
              className={`${styles.navItem} ${activeTab === tab.key ? styles.active : ''} ${tab.disabled ? styles.disabled : ''}`}
              onClick={() => !tab.disabled && handleTabChange(tab.key)}
            >
              <span className={styles.icon}>
                {tab.icon}
                {tab.badge && (
                  <span className={styles.badge}>
                    {typeof tab.badge === 'number' ? tab.badge : ''}
                  </span>
                )}
              </span>
              <span className={styles.title}>{tab.title}</span>
            </div>
          </SafeTooltip>
        ))}
      </div>
    </div>
  );
};

export default RightSidebar; 