// src/components/Independent/helpers.tsx
import React from 'react';
import { Space } from 'antd';

/**
 * Renders a title with an icon using Ant Design's Space component.
 *
 * @param icon - The icon to display.
 * @param title - The title text.
 * @returns A JSX element containing the icon and title.
 */
export const renderTitle = (icon: React.ReactElement, title: string) => (
  <Space align="start">
    {icon}
    <span>{title}</span>
  </Space>
);
