// src/components/Chat/MessageActions.tsx
import React from 'react';
import { Button, Flex } from 'antd';
import {
  SyncOutlined,
  SmileOutlined,
  FrownOutlined,
  CopyOutlined,
} from '@ant-design/icons';

interface MessageActionsProps {
  index: number;
  message: string;
  loading: boolean;
  onRegenerate: (index: number) => void;
  onCopy: (message: string) => void;
}

const MessageActions: React.FC<MessageActionsProps> = ({
  index,
  message,
  loading,
  onRegenerate,
  onCopy,
}) => {
  return (
    <Flex>
      <Button
        size="small"
        type="text"
        icon={<SyncOutlined />}
        style={{ marginInlineEnd: 'auto' }}
        onClick={() => onRegenerate(index)}
        loading={loading}
      />
      <Button size="small" type="text" icon={<SmileOutlined />} />
      <Button size="small" type="text" icon={<FrownOutlined />} />
      <Button
        size="small"
        type="text"
        icon={<CopyOutlined />}
        onClick={() => onCopy(message)}
      />
    </Flex>
  );
};

const UserMessageActions: React.FC<MessageActionsProps> = ({
  message,
  onCopy,
}) => {
  return (
    <Flex>
      <Button
        size="small"
        type="text"
        icon={<CopyOutlined />}
        onClick={() => onCopy(message)}
      />
    </Flex>
  );
};

export { MessageActions, UserMessageActions };
