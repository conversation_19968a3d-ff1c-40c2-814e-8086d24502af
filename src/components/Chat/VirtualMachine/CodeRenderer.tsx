import React, { useEffect, useRef } from 'react';
import hljs from 'highlight.js';
import 'highlight.js/styles/vs2015.css';
import styles from './CodeRenderer.module.css';

interface CodeRendererProps {
  content: string;
  language?: string;
  className?: string;
}

const CodeRenderer: React.FC<CodeRendererProps> = ({
  content,
  language = 'plaintext',
  className
}) => {
  const codeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (codeRef.current) {
      const rect = codeRef.current.getBoundingClientRect();
      console.log('CodeRenderer dimensions:', {
        width: rect.width,
        height: rect.height,
        scrollHeight: codeRef.current.scrollHeight,
        clientHeight: codeRef.current.clientHeight,
        hasOverflow: codeRef.current.scrollHeight > codeRef.current.clientHeight
      });
    }
  }, [content]);

  // 清理代码内容
  const cleanCode = (code: string): string => {
    // 移除markdown代码块标记
    return code
      .replace(/^```[a-zA-Z]*\s*/i, '')
      .replace(/```\s*$/, '')
      .trim();
  };

  const cleanedContent = cleanCode(content);

  // 高亮代码
  const highlightCode = (code: string, lang: string) => {
    try {
      if (hljs.getLanguage(lang)) {
        return hljs.highlight(code, { language: lang }).value;
      } else {
        return hljs.highlightAuto(code).value;
      }
    } catch (error) {
      console.warn('代码高亮失败:', error);
      return code;
    }
  };

  const highlightedCode = highlightCode(cleanedContent, language);

  return (
    <div 
      ref={codeRef}
      className={`${styles.codeRenderer} ${className || ''}`}
    >
      <pre className={styles.codeBlock}>
        <code 
          className={`hljs ${styles.codeContent}`}
          dangerouslySetInnerHTML={{ __html: highlightedCode }}
        />
      </pre>
    </div>
  );
};

export default CodeRenderer; 