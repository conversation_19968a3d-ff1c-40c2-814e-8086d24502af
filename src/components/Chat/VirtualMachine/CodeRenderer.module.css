.codeRenderer {
  width: 100%;
  height: 100%;
  min-height: 400px;
  max-height: 600px;
  overflow: auto !important;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.codeBlock {
  margin: 0;
  padding: 16px;
  background: transparent;
  border: none;
  border-radius: 0;
  overflow: visible;
  width: 100%;
  box-sizing: border-box;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #333333;
}

.codeContent {
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  display: block;
  width: 100%;
  white-space: inherit;
}

/* 确保高亮样式正确应用 */
.codeContent.hljs {
  background: transparent !important;
  color: #333333;
}

/* 代码内容的滚动条样式 */
.codeRenderer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.codeRenderer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.codeRenderer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.codeRenderer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .codeBlock {
    padding: 12px;
    font-size: 13px;
  }
} 