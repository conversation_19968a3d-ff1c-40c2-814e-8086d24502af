import React, { useState, useEffect, useRef } from 'react';
import { Input, <PERSON><PERSON>, Spin } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import MarkdownRenderer from '../MarkdownRenderer';
import { SearchResults, FileViewer } from '../action_components';
import CodePreview from './CodePreview';
import CodeRenderer from './CodeRenderer';
import styles from './index.module.css';

interface VirtualMachineProps {
  visible: boolean;
  onClose: () => void;
  actionName: string;
  toolResult: any;
  toolInput: string;
  eventId: string;
  toolEvents: Record<string, any>;
  onJumpToLatest?: () => void;
  groupedEvents?: Array<{
    eventId: string;
    toolResult: any;
    toolInput: string;
    created_at: string;
  }>;
  currentPageIndex?: number;
  query?: string;
}

const SEARCH_ACTION_NAME = "search";
const VIEW_ACTION_NAME = 'view';
const AGGREGATION_ACTION_NAME = 'aggregation';
const REFLECTION_ACTION_NAME = 'reflection';
const HTML_ACTION_NAME = 'html';
const SVG_ACTION_NAME = 'svg';
const REASONING_CONTENT_ACTION_NAME = 'reasoning_content';

const VirtualMachine: React.FC<VirtualMachineProps> = ({
  visible,
  onClose,
  actionName,
  toolResult,
  toolInput,
  eventId,
  toolEvents,
  onJumpToLatest,
  groupedEvents,
  currentPageIndex,
  query
}) => {
  const [searchUrl, setSearchUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [viewMode, setViewMode] = useState<'search' | 'file' | 'web'>('search');
  const [selectedFile, setSelectedFile] = useState<{url: string, title: string} | null>(null);
  // HTML分组页面导航状态
  const [currentHtmlPageIndex, setCurrentHtmlPageIndex] = useState<number>(0);
  // 添加ref用于监听滚轮事件
  const codePreviewRef = useRef<HTMLDivElement>(null);
  // 防抖状态
  const [isScrolling, setIsScrolling] = useState<boolean>(false);

  // 初始化HTML页面索引
  useEffect(() => {
    if (currentPageIndex !== undefined) {
      setCurrentHtmlPageIndex(currentPageIndex);
    }
  }, [currentPageIndex]);

  // 页面导航函数
  const handlePageChange = (direction: 'prev' | 'next') => {
    if (!groupedEvents || groupedEvents.length <= 1) return;
    
    if (direction === 'prev' && currentHtmlPageIndex > 0) {
      setCurrentHtmlPageIndex(prev => prev - 1);
    } else if (direction === 'next' && currentHtmlPageIndex < groupedEvents.length - 1) {
      setCurrentHtmlPageIndex(prev => prev + 1);
    }
  };

  // 滚轮事件处理
  const handleWheel = (event: WheelEvent) => {
    // 只在HTML/SVG分组模式下且有多个页面时生效
    if ((!groupedEvents || groupedEvents.length <= 1) || 
        (actionName !== HTML_ACTION_NAME && actionName !== SVG_ACTION_NAME)) {
      return;
    }

    // 防止过于频繁的切换
    if (isScrolling) return;

    event.preventDefault();
    event.stopPropagation();

    const delta = event.deltaY;
    const threshold = 50; // 滚动阈值

    if (Math.abs(delta) > threshold) {
      setIsScrolling(true);
      
      if (delta > 0) {
        // 向下滚动 - 下一页
        handlePageChange('next');
      } else {
        // 向上滚动 - 上一页
        handlePageChange('prev');
      }

      // 防抖，300ms后允许下次滚动
      setTimeout(() => {
        setIsScrolling(false);
      }, 300);
    }
  };

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent) => {
    // 只在HTML/SVG分组模式下且有多个页面时生效
    if ((!groupedEvents || groupedEvents.length <= 1) || 
        (actionName !== HTML_ACTION_NAME && actionName !== SVG_ACTION_NAME)) {
      return;
    }

    // 防止过于频繁的切换
    if (isScrolling) return;

    if (event.key === 'ArrowUp') {
      event.preventDefault();
      setIsScrolling(true);
      handlePageChange('prev');
      setTimeout(() => setIsScrolling(false), 300);
    } else if (event.key === 'ArrowDown') {
      event.preventDefault();
      setIsScrolling(true);
      handlePageChange('next');
      setTimeout(() => setIsScrolling(false), 300);
    }
  };

  // 添加滚轮事件监听器
  useEffect(() => {
    const element = codePreviewRef.current;
    if (element && (actionName === HTML_ACTION_NAME || actionName === SVG_ACTION_NAME) && 
        groupedEvents && groupedEvents.length > 1) {
      element.addEventListener('wheel', handleWheel, { passive: false });
      
      return () => {
        element.removeEventListener('wheel', handleWheel);
      };
    }
  }, [actionName, groupedEvents, currentHtmlPageIndex, isScrolling]);

  // 添加键盘事件监听器
  useEffect(() => {
    if ((actionName === HTML_ACTION_NAME || actionName === SVG_ACTION_NAME) && 
        groupedEvents && groupedEvents.length > 1 && visible) {
      document.addEventListener('keydown', handleKeyDown);
      
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [actionName, groupedEvents, currentHtmlPageIndex, isScrolling, visible]);

  // 获取当前页面的数据
  const getCurrentPageData = () => {
    if (groupedEvents && groupedEvents.length > 0 && 
        (actionName === HTML_ACTION_NAME || actionName === SVG_ACTION_NAME)) {
      const currentPage = groupedEvents[currentHtmlPageIndex];
      return {
        toolResult: currentPage.toolResult,
        toolInput: currentPage.toolInput,
        eventId: currentPage.eventId,
        totalPages: groupedEvents.length,
        currentPage: currentHtmlPageIndex + 1
      };
    }
    return {
      toolResult,
      toolInput,
      eventId,
      totalPages: 1,
      currentPage: 1
    };
  };

  // 计算进度条相关数据
  const getProgressData = () => {
    const toolEventsList = Object.values(toolEvents).filter(event => 
      event.event_type === 'tool_call' // 只包含 tool_call 事件，排除 chat.completion 事件
    ).sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    
    const currentIndex = toolEventsList.findIndex(event => event.id === eventId);
    const totalEvents = toolEventsList.length;
    const progressPercent = totalEvents > 0 ? ((currentIndex + 1) / totalEvents) * 100 : 0;
    const isLatest = currentIndex === totalEvents - 1;
    
    // 调试信息
    console.log('Progress Debug:', {
      eventId,
      currentIndex,
      totalEvents,
      isLatest,
      hasCallback: !!onJumpToLatest,
      shouldShowButton: !isLatest && !!onJumpToLatest,
      shouldShowProgressBar: totalEvents > 1
    });
    
    return {
      currentIndex: currentIndex + 1,
      totalEvents,
      progressPercent,
      toolEventsList,
      isLatest
    };
  };

  const { currentIndex, totalEvents, progressPercent, isLatest } = getProgressData();

  // 解析搜索结果数据
  const parseSearchData = () => {
    if (!toolResult) return [];
    
    try {
      // 如果toolResult是数组
      if (Array.isArray(toolResult)) {
        const firstItem = toolResult[0];
        if (firstItem && firstItem.tool_result && Array.isArray(firstItem.tool_result)) {
          return firstItem.tool_result;
        }
      }
      
      // 如果toolResult直接包含tool_result
      if (toolResult.tool_result && Array.isArray(toolResult.tool_result)) {
        return toolResult.tool_result;
      }
      
      // 如果toolResult本身就是搜索结果数组
      if (toolResult.length && toolResult[0].title) {
        return toolResult;
      }
      
      return [];
    } catch (error) {
      console.error('解析搜索数据失败:', error);
      return [];
    }
  };

  // 处理文件点击
  const handleFileClick = (fileUrl: string, title: string) => {
    setSelectedFile({ url: fileUrl, title });
    setViewMode('file');
  };

  // 处理网页点击
  const handleUrlClick = (url: string, title: string) => {
    setSelectedFile({ url, title });
    setViewMode('web');
  };

  // 返回搜索结果
  const handleBackToSearch = () => {
    setViewMode('search');
    setSelectedFile(null);
  };

  // 根据工具名称渲染不同内容
  const renderContent = () => {
    switch (actionName) {
      case SEARCH_ACTION_NAME:
        const searchData = parseSearchData();
        
        if (viewMode === 'file' && selectedFile) {
          return (
            <FileViewer
              fileUrl={selectedFile.url}
              title={selectedFile.title}
              onBack={handleBackToSearch}
            />
          );
        }
        
        if (viewMode === 'web' && selectedFile) {
          return (
            <FileViewer
              webUrl={selectedFile.url}
              title={selectedFile.title}
              onBack={handleBackToSearch}
            />
          );
        }
        
        return (
          <SearchResults
            data={searchData}
            query={toolInput}
            onFileClick={handleFileClick}
            onUrlClick={handleUrlClick}
          />
        );

      case VIEW_ACTION_NAME:
        return (
          <div className={styles.viewInterface}>
            <div className={styles.viewHeader}>
              <span className={styles.viewTitle}>网页浏览模拟器</span>
            </div>
            <div className={styles.urlBar}>
              <Input
                value={searchUrl || (typeof toolResult === 'string' ? toolResult : '')}
                onChange={(e) => setSearchUrl(e.target.value)}
                placeholder="输入URL地址"
                prefix="🌐"
                className={styles.urlInput}
              />
              <Button type="primary" onClick={() => setIsLoading(!isLoading)}>
                访问
              </Button>
            </div>
            <div className={styles.pageContent}>
              {isLoading ? (
                <div className={styles.loadingPage}>
                  <Spin size="large" />
                  <p>正在加载页面...</p>
                </div>
              ) : (
                <div className={styles.simulatedPage}>
                  <div className={styles.pageHeader}>
                    <div className={styles.pageTitle}>模拟页面内容</div>
                  </div>
                  <div className={styles.pageBody}>
                    {typeof toolResult === 'string' ? (
                      <CodeRenderer 
                        content={toolResult}
                        language="markdown"
                        className={styles.pageCodeRenderer}
                      />
                    ) : (
                      <CodeRenderer 
                        content={JSON.stringify(toolResult, null, 2)}
                        language="json"
                        className={styles.pageCodeRenderer}
                      />
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        );

      case AGGREGATION_ACTION_NAME:
        return (
          <div className={styles.aggregationInterface}>
            <div className={styles.aggregationHeader}>
              <span className={styles.aggregationTitle}>数据聚合结果</span>
            </div>
            <div className={styles.aggregationQuery}>
              <strong>聚合查询：</strong> {toolInput}
            </div>
            <div className={styles.aggregationResult}>
              {typeof toolResult === 'string' ? (
                <CodeRenderer 
                  content={toolResult}
                  language="markdown"
                  className={styles.aggregationCodeRenderer}
                />
              ) : (
                <CodeRenderer 
                  content={JSON.stringify(toolResult, null, 2)}
                  language="json"
                  className={styles.aggregationCodeRenderer}
                />
              )}
            </div>
          </div>
        );

      case REFLECTION_ACTION_NAME:
        return (
          <div className={styles.reflectionInterface}>
            {/* <div className={styles.reflectionHeader}>
              <span className={styles.reflectionTitle}>反思分析</span>
            </div> */}
            <div className={styles.reflectionQuery}>
              <strong>反思：</strong> {toolInput}
            </div>
            <div className={styles.reflectionResult}>
              {typeof toolResult === 'string' ? (
                <CodeRenderer 
                  content={toolResult}
                  language="markdown"
                  className={styles.reflectionCodeRenderer}
                />
              ) : (
                <CodeRenderer 
                  content={JSON.stringify(toolResult, null, 2)}
                  language="json"
                  className={styles.reflectionCodeRenderer}
                />
              )}
            </div>
          </div>
        );

      case HTML_ACTION_NAME:
        const htmlPageData = getCurrentPageData();
        return (
          <div className={styles.codePreviewContainer} ref={codePreviewRef}>
            {/* HTML分组页面导航 */}
            {htmlPageData.totalPages > 1 && (
              <div className={styles.pageNavigation}>
                <div className={styles.pageInfo}>
                  <span className={styles.pageTitle}>
                    {query && `${query} - `}页面 {htmlPageData.currentPage} / {htmlPageData.totalPages}
                  </span>
                  <span className={styles.scrollHint}>
                    滚轮滑动或方向键上下切换页面
                  </span>
                </div>
              </div>
            )}
            <CodePreview
              codeType="html"
              content={typeof htmlPageData.toolResult === 'string' ? htmlPageData.toolResult : JSON.stringify(htmlPageData.toolResult, null, 2)}
              title={htmlPageData.toolInput}
            />
          </div>
        );

      case SVG_ACTION_NAME:
        const svgPageData = getCurrentPageData();
        return (
          <div className={styles.codePreviewContainer} ref={codePreviewRef}>
            {/* SVG分组页面导航 */}
            {svgPageData.totalPages > 1 && (
              <div className={styles.pageNavigation}>
                <div className={styles.pageInfo}>
                  <span className={styles.pageTitle}>
                    {query && `${query} - `}页面 {svgPageData.currentPage} / {svgPageData.totalPages}
                  </span>
                  <span className={styles.scrollHint}>
                    滚轮滑动或方向键上下切换页面
                  </span>
                </div>
              </div>
            )}
            <CodePreview
              codeType="svg"
              content={typeof svgPageData.toolResult === 'string' ? svgPageData.toolResult : JSON.stringify(svgPageData.toolResult, null, 2)}
              title={svgPageData.toolInput}
            />
          </div>
        );

      default:
        return (
          <div className={styles.defaultInterface}>
            <div className={styles.toolQuery}>
              <strong>执行查询：</strong> {toolInput}
            </div>
            <div className={styles.toolResult}>
              {typeof toolResult === 'string' ? (
                <CodeRenderer 
                  content={toolResult}
                  language="markdown"
                  className={styles.defaultCodeRenderer}
                />
              ) : (
                <CodeRenderer 
                  content={JSON.stringify(toolResult, null, 2)}
                  language="json"
                  className={styles.defaultCodeRenderer}
                />
              )}
            </div>
          </div>
        );
    }
  };

  // 获取模态框标题
  const getModalTitle = () => {
    const pageData = getCurrentPageData();
    
    if (actionName === SEARCH_ACTION_NAME) {
      if (viewMode === 'file') {
        return '文件预览';
      } else if (viewMode === 'web') {
        return '网页预览';
      } else {
        return '搜索结果';
      }
    }
    if (actionName === AGGREGATION_ACTION_NAME) {
      return '数据聚合';
    }
    if (actionName === REFLECTION_ACTION_NAME) {
      return '反思分析';
    }
    if (actionName === HTML_ACTION_NAME) {
      const baseTitle = 'HTML 代码预览';
      if (pageData.totalPages > 1) {
        return `${baseTitle} (${pageData.currentPage}/${pageData.totalPages})`;
      }
      return baseTitle;
    }
    if (actionName === SVG_ACTION_NAME) {
      const baseTitle = 'SVG 代码预览';
      if (pageData.totalPages > 1) {
        return `${baseTitle} (${pageData.currentPage}/${pageData.totalPages})`;
      }
      return baseTitle;
    }
    return actionName;
  };

  if (!visible) return null;
  console.log(toolEvents,isLatest,onJumpToLatest,'onJumpToLatestonJumpToLatestonJumpToLatest')
  return (
    <div className={styles.virtualMachinePanel}>
      <div className={styles.panelHeader}>
        <span className={styles.panelTitle}>虚拟机 - {getModalTitle()}</span>
        <Button 
          type="text" 
          icon={<CloseOutlined />} 
          onClick={onClose}
          className={styles.closeButton}
        />
      </div>
      <div className={styles.panelContent}>
        {renderContent()}
      </div>
      {/* 进度条 */}
      {totalEvents > 1 && (
        <div className={styles.progressBar}>
          <div className={styles.progressInfo}>
            <span className={styles.progressText}>
              工具事件 {currentIndex} / {totalEvents}
            </span>
            {!isLatest && onJumpToLatest && (
              <Button 
                type="link" 
                size="small"
                onClick={onJumpToLatest}
                className={styles.backToLatestButton}
              >
                返回至最新进展
              </Button>
            )}
          </div>
          <div className={styles.progressTrack}>
            <div 
              className={styles.progressFill}
              style={{ width: `${progressPercent}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default VirtualMachine; 