import { fetchBulk } from '../../../api/api';
import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';
import { message } from 'antd';

// MCP服务端点
export const MCP_SERVER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/mcp-servers`;

// MCP服务接口
export interface MCPServer {
  id: string;
  name: string;
  description: string;
  username?: string;
}

// 获取MCP服务列表
export const fetchMcpServers = async (
  username: string,
  sortField: string = 'updated_at',
  sortOrder: string = 'desc',
  currentPage: number = 1,
  pageSize: number = 100
): Promise<{ data: MCPServer[], total: number }> => {
  try {
    const skip = (currentPage - 1) * pageSize;
    const limit = pageSize;
    let endpoint_api = `${MCP_SERVER_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${username}&is_published=true`;

    const result = await fetchBulk(endpoint_api);
    return {
      data: result.data || [],
      total: result.total || 0
    };
  } catch (error) {
    console.error('获取MCP服务列表失败:', error);
    message.error('获取MCP服务列表失败');
    return {
      data: [],
      total: 0
    };
  }
};

// 判断MCP服务是否属于当前用户
export const isCurrentUserMcpServer = (server: MCPServer, currentUsername: string): boolean => {
  return !server.username || server.username === currentUsername;
}; 