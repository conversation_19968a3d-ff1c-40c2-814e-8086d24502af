.mcpServerSelectorModal {
  font-family: 'Inter', system-ui, sans-serif;
}

.mcpServerSelectorModal :global(.ant-modal-content) {
  border-radius: 12px;
  overflow: hidden;
  background-color: #f5f7fa;
}

.mcpServerSelectorModal :global(.ant-modal-header) {
  padding: 16px 24px;
  background: linear-gradient(90deg, #001529, #002140);
  border-bottom: 1px solid rgba(0, 120, 255, 0.15);
}

.mcpServerSelectorModal :global(.ant-modal-title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.mcpServerSelectorModal :global(.ant-modal-close) {
  color: rgba(255, 255, 255, 0.65);
}

.mcpServerSelectorModal :global(.ant-modal-close:hover) {
  color: rgba(255, 255, 255, 0.95);
}

.mcpServerSelectorModal :global(.ant-modal-body) {
  padding: 24px;
}

/* 分类容器 */
.categoriesContainer {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 分类区域 */
.categorySection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 分类标题 */
.categoryHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.categoryIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: #FFF6E8;
  color: #FA8C16;
}

/* 不同类型使用不同的颜色 */
.categorySection:nth-child(1) .categoryIcon {
  background-color: #FFF6E8;
  color: #FA8C16;
}

.categorySection:nth-child(2) .categoryIcon {
  background-color: #FCF4F2;
  color: #F5222D;
}

.categorySection:nth-child(3) .categoryIcon {
  background-color: #F0F5FF;
  color: #2F54EB;
}

.categorySection:nth-child(4) .categoryIcon {
  background-color: #F6FFED;
  color: #52C41A;
}

.categorySection:nth-child(5) .categoryIcon {
  background-color: #E6F7FF;
  color: #1890FF;
}

.categorySection:nth-child(6) .categoryIcon {
  background-color: #F9F0FF;
  color: #722ED1;
}

.categoryTitle {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin: 0;
}

.categoryDescription {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  cursor: help;
  padding-left: 48px;
  margin-top: -12px;
}

/* 数据集卡片网格 */
.mcpServerCardGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  padding-left: 48px;
}

@media (max-width: 992px) {
  .mcpServerCardGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .mcpServerCardGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .mcpServerCardGrid {
    grid-template-columns: 1fr;
  }
}

/* 单个数据集项目 */
.mcpServerItem {
  padding: 12px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.06);
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  transition: all 0.3s;
  cursor: pointer;
  height: 56px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  overflow: hidden;
  gap: 8px;
}

/* 数据集图标样式 */
.mcpServerIcon {
  width: 24px;
  height: 24px;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mcpServerItem:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: rgba(0, 120, 255, 0.3);
}

/* 高亮显示被选中的数据集 */
.mcpServerItemActive {
  background-color: #e6f7ff;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
  font-weight: 600;
  color: #1890ff;
  transform: scale(1.02);
  position: relative;
  z-index: 1;
}

.mcpServerItemActive::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #1890ff;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.mcpServerItemActive:hover {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.4);
  transform: scale(1.02);
}

/* 禁用状态的数据集项目 */
.mcpServerItemDisabled {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.45);
  cursor: not-allowed;
  border-color: #d9d9d9;
}

.mcpServerItemDisabled:hover {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  transform: none;
  border-color: #d9d9d9;
}

/* 即将推出标签 */
.mcpServerItemComingSoon {
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 10px;
  background-color: #ff4d4f;
  color: white;
  padding: 1px 4px;
  border-radius: 4px;
  font-weight: normal;
  opacity: 0.8;
}

/* 空状态 */
.emptyMcpServer {
  grid-column: 1 / -1;
  padding: 24px;
}

/* 非当前用户的数据集项目样式 */
.mcpServerItemOtherUser {
  background-color: #fffbe6;
  border-color: #ffd666;
}

.mcpServerItemOtherUser:hover {
  border-color: #ffc107;
  box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
}

/* 非当前用户的数据集被选中时 */
.mcpServerItemOtherUserActive {
  background-color: #fff1c2;
  border-color: #faad14;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.3);
}

.mcpServerItemOtherUserActive::before {
  background-color: #faad14;
} 