import React, { useState, useEffect } from 'react';
import { Modal, Spin, Empty, message } from 'antd';
import { CHAT_API_NAME } from '../../../../Constant/Constant';
import { fetchBulk } from '../../../api/api';
import styles from './MCPServerSelector.module.css'; // 使用自己的CSS模块
import SafeTooltip from '../../../common/SafeTooltip';
import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';
import { useSelector } from 'react-redux';
import { ShareAltOutlined } from '@ant-design/icons';

// 数据集工作流选项
const MCP_WORKFLOW_IDS = [
  {
    id: '551eb1f4-bd3b-96d7-601b-af976ab84666',
    name: '智能问答',
    description: '基于数据集的智能问答系统，能够理解自然语言并返回准确答案，速度快',
    is_active: true,
    workflow_or_agent: 'workflow',
    icon: (
      <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        <line x1="9" y1="10" x2="15" y2="10"></line>
      </svg>
    )
  },
  {
    id: '2fc84bf6-d841-0bac-f194-d149876cb103',
    name: '推理问答',
    description: '具备逻辑推理能力的问答系统，能够处理复杂问题并给出推理过程，速度慢，准确度高',
    is_active: true,
    workflow_or_agent: 'workflow',
    icon: (
      <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
        <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
        <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
        <line x1="6" y1="1" x2="6" y2="4"></line>
        <line x1="10" y1="1" x2="10" y2="4"></line>
        <line x1="14" y1="1" x2="14" y2="4"></line>
      </svg>
    )
  },
  {
    id: '551eb1f4-bd3b-96d7-601b-af976ab84668',
    name: '数据智能分析',
    description: '对数据集进行智能分析，提取关键信息并生成分析报告',
    is_active: false,
    workflow_or_agent: 'agent',
    icon: (
      <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9 22 9 12 15 12 15 22"></polyline>
      </svg>
    )
  }
];

// MCP服务端点
const MCP_SERVER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/mcp-servers`;

interface MCPServer {
  id: string;
  name: string;
  description: string;
  username?: string; // 添加username字段，表示数据集所属用户
}

interface MCPServerSelectorProps {
  visible: boolean;
  onClose: () => void;
  username: string;
}

const MCPServerSelector: React.FC<MCPServerSelectorProps> = ({ visible, onClose, username }) => {
  const [mcpServers, setMcpServers] = useState<MCPServer[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [sortField] = useState<string>('updated_at');
  const [sortOrder] = useState<string>('desc');
  const [currentPage] = useState<number>(1);
  const [pageSize] = useState<number>(100); // 获取足够多的MCP服务
  // 添加状态来跟踪当前选中的工作流ID
  const [selectedWorkflowId, setSelectedWorkflowId] = useState<string>(MCP_WORKFLOW_IDS[0].id);

  // 获取当前登录用户的信息
  const { userInfo } = useSelector((state: any) => state.user);
  const currentUsername = userInfo?.username; // 当前登录用户名

  // 获取MCP服务列表
  useEffect(() => {
    if (visible) {
      fetchMcpServers();
    }
  }, [visible, username, currentPage, pageSize, sortField, sortOrder]);

  const fetchMcpServers = async () => {
    setLoading(true);
    try {
      const skip = (currentPage - 1) * pageSize;
      const limit = pageSize;
      let endpoint_api = `${MCP_SERVER_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${username}&is_published=true`;

      const result = await fetchBulk(endpoint_api);
      console.log(result);
      setMcpServers(result.data);
    } catch (error) {
      console.error('获取MCP服务列表失败:', error);
      message.error('获取MCP服务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 判断数据集是否属于当前登录用户
  const isCurrentUserMcpServer = (server: MCPServer) => {
    return !server.username || server.username === currentUsername;
  };

  // 处理工作流选择
  const handleWorkflowSelect = (workflow: typeof MCP_WORKFLOW_IDS[0]) => {
    if (workflow.is_active) {
      setSelectedWorkflowId(workflow.id);
    } else {
      message.info(`${workflow.name}功能正在开发中，敬请期待`);
    }
  };

  // 处理MCP服务点击，跳转到聊天页面
  const handleMcpServerClick = (mcpServer: MCPServer) => {
    // 先关闭弹窗
    onClose();
    
    // 构建URL - 使用选中的workflow_id，而不是固定的ID
    const url = `${CHAT_API_NAME}/${selectedWorkflowId}/new?workflow_or_agent=workflow&mcp_id=${mcpServer.id}`;
    
    // 使用window.location.href进行导航，这会导致页面完全刷新
    window.location.href = url;
  };

  return (
    <Modal
      title="数据集选择"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      className={styles.mcpServerSelectorModal}
    >
      <Spin spinning={loading}>
        <div className={styles.categoriesContainer}>
          {/* 工作流选择部分 */}
          <div className={styles.categorySection}>
            <div className={styles.categoryHeader}>
              <div className={styles.categoryIcon}>
                <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                </svg>
              </div>
              <h3 className={styles.categoryTitle}>对话模式</h3>
            </div>
            
            <div className={styles.categoryDescription}>
              选择一种对话模式，功能各有不同
            </div>
            
            <div className={styles.mcpServerCardGrid}>
              {MCP_WORKFLOW_IDS.map(workflow => (
                <SafeTooltip key={workflow.id} title={workflow.description || '暂无描述'}>
                  <div 
                    className={`${styles.mcpServerItem} ${selectedWorkflowId === workflow.id ? styles.mcpServerItemActive : ''} ${!workflow.is_active ? styles.mcpServerItemDisabled : ''}`}
                    onClick={() => handleWorkflowSelect(workflow)}
                  >
                    <div className={styles.mcpServerIcon}>
                      {workflow.icon}
                    </div>
                    {workflow.name}
                    {!workflow.is_active && <span className={styles.mcpServerItemComingSoon}>即将推出</span>}
                  </div>
                </SafeTooltip>
              ))}
            </div>
          </div>

          {/* 数据集选择部分 */}
          {mcpServers.length > 0 ? (
            <div className={styles.categorySection}>
              <div className={styles.categoryHeader}>
                <div className={styles.categoryIcon}>
                  <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="3" y1="9" x2="21" y2="9"></line>
                    <line x1="9" y1="21" x2="9" y2="9"></line>
                  </svg>
                </div>
                <h3 className={styles.categoryTitle}>数据集</h3>
              </div>
              
              <div className={styles.categoryDescription}>
                选择一个数据集进行对话
              </div>
              
              <div className={styles.mcpServerCardGrid}>
                {mcpServers.map(server => {
                  // 确定样式类：区分当前用户和其他用户的数据集
                  const isCurrentUser = isCurrentUserMcpServer(server);
                  const itemClassName = `${styles.mcpServerItem} ${
                    !isCurrentUser ? styles.mcpServerItemOtherUser : ''
                  }`;
                  
                  return (
                    <SafeTooltip 
                      key={server.id} 
                      title={
                        <div>
                          <div>{server.description || '暂无描述'}</div>
                          {!isCurrentUser && server.username && (
                            <div style={{ marginTop: 8, color: '#faad14' }}>
                              所有者: {server.username}
                            </div>
                          )}
                        </div>
                      }
                    >
                      <div 
                        className={itemClassName}
                        onClick={() => handleMcpServerClick(server)}
                      >
                        {server.name}
                        {!isCurrentUser && (
                          <span style={{ 
                            position: 'absolute', 
                            top: 4, 
                            left: 4, 
                            fontSize: 10, 
                            backgroundColor: '#faad14', 
                            color: 'white',
                            padding: '1px 4px',
                            borderRadius: 4,
                            opacity: 0.8
                          }}>
                            <ShareAltOutlined /> 共享
                          </span>
                        )}
                      </div>
                    </SafeTooltip>
                  );
                })}
              </div>
            </div>
          ) : (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE} 
              description="暂无可用的数据集" 
              className={styles.emptyMcpServer}
            />
          )}
        </div>
      </Spin>
    </Modal>
  );
};

export default MCPServerSelector; 