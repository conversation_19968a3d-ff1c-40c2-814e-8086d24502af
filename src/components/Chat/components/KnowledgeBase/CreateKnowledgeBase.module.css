.createKnowledgeBaseModal {
  font-family: 'Inter', system-ui, sans-serif;
}

.createKnowledgeBaseModal :global(.ant-modal-content) {
  border-radius: 12px;
  overflow: hidden;
  background-color: #f5f7fa;
}

.createKnowledgeBaseModal :global(.ant-modal-header) {
  padding: 16px 24px;
  background: linear-gradient(90deg, #001529, #002140);
  border-bottom: 1px solid rgba(0, 120, 255, 0.15);
}

.createKnowledgeBaseModal :global(.ant-modal-title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.createKnowledgeBaseModal :global(.ant-modal-close) {
  color: rgba(255, 255, 255, 0.65);
}

.createKnowledgeBaseModal :global(.ant-modal-close:hover) {
  color: rgba(255, 255, 255, 0.95);
}

.createKnowledgeBaseModal :global(.ant-modal-body) {
  padding: 24px;
}

.formContainer {
  margin-top: 12px;
}

.formItem {
  margin-bottom: 20px;
}

.formLabel {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.advancedSettings {
  margin-top: 20px;
}

.advancedSettingsTitle {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 16px;
}

.footerActions {
  margin-top: 24px;
  text-align: right;
}

.cancelButton {
  margin-right: 12px;
}

.submitButton {
  background-color: #1890ff;
  border-color: #1890ff;
}

.submitButton:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 折叠面板样式 */
.collapsePanel {
  background-color: transparent;
  border: none;
}

.collapsePanel :global(.ant-collapse-header) {
  color: rgba(0, 0, 0, 0.65);
  font-weight: 500;
}

.collapsePanel :global(.ant-collapse-content) {
  background-color: transparent;
}

/* 输入框样式 */
.input {
  border-radius: 6px;
  border-color: #d9d9d9;
}

.input:hover, .input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.textArea {
  border-radius: 6px;
  border-color: #d9d9d9;
  resize: vertical;
}

.textArea:hover, .textArea:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 示例相关样式 */
.examplesContainer {
  margin-bottom: 24px;
}

.addExampleButton {
  width: 100%;
  height: 40px;
  border-radius: 6px;
  border-style: dashed;
  color: #1890ff;
}

.addExampleButton:hover {
  color: #40a9ff;
  border-color: #40a9ff;
}

.addExampleButton[disabled] {
  color: rgba(0, 0, 0, 0.25);
  border-color: #d9d9d9;
  cursor: not-allowed;
}

.disabledTip {
  margin-top: 8px;
  color: #ff4d4f;
  font-size: 14px;
}

.emptyExamples {
  text-align: center;
  padding: 32px;
  background-color: #f9f9f9;
  border-radius: 8px;
  color: #999;
  font-size: 14px;
}

/* 示例卡片相关样式 */
.exampleCard {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s;
}

.exampleCard:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.exampleCardExpanded {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
}

.exampleCard :global(.ant-card-head) {
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
}

.exampleCard :global(.ant-card-body) {
  padding: 16px;
}

.exampleCardTitle {
  display: flex;
  align-items: center;
}

.examplePreview {
  margin-left: 12px;
  color: #999;
  font-size: 14px;
  font-weight: normal;
  font-style: italic;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
}

.exampleCardExtra {
  display: flex;
  align-items: center;
}

.expandButton {
  margin-right: 8px;
}

.exampleFormItem {
  margin-bottom: 16px;
}

.exampleTextArea {
  border-radius: 6px;
  resize: vertical;
  min-height: 80px;
}

.exampleTextArea:hover, .exampleTextArea:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 上传拖拽区域样式 */
.uploadDragger {
  width: 100% !important;
  height: auto !important;
  margin-bottom: 16px;
}

.uploadDragger :global(.ant-upload-drag) {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
}

.uploadDragger :global(.ant-upload-drag:hover) {
  border-color: #40a9ff;
}

.uploadDragger :global(.ant-upload-drag-icon) {
  color: #40a9ff;
  font-size: 48px;
  margin-bottom: 8px;
}

.uploadDragger :global(.ant-upload-text) {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  margin: 0 0 4px;
}

.uploadDragger :global(.ant-upload-hint) {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

.uploadDragger :global(.ant-upload-list-picture-card-container),
.uploadDragger :global(.ant-upload-list-picture-card .ant-upload-list-item) {
  width: 104px;
  height: 104px;
  margin: 8px;
} 