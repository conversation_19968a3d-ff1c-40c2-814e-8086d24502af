import React, { useState, useMemo } from 'react';
import { Modal, Form, Input, Button, Switch, Select, message, Divider, Collapse, InputNumber, Row, Col, Tooltip, Card, Upload } from 'antd';
import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';
import { useSelector } from 'react-redux';
import styles from './CreateKnowledgeBase.module.css';
import { InfoCircleOutlined, PlusOutlined, DeleteOutlined, QuestionCircleOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';
import { createData, uploadImageToServer } from '../../../api/api';

const { Panel } = Collapse;

// 示例数据结构
interface WorkflowExample {
  text: string;
  conversable: boolean;
  attachments?: {
    content: string;
    url: string;
    filename: string;
  }[];
}

interface CreateKnowledgeBaseProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateKnowledgeBase: React.FC<CreateKnowledgeBaseProps> = ({ 
  visible, 
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [examples, setExamples] = useState<WorkflowExample[]>([]);
  const [expandedExampleIds, setExpandedExampleIds] = useState<number[]>([]);
  
  // 获取当前登录用户的信息
  const { userInfo } = useSelector((state: any) => state.user);
  const username = userInfo?.username;

  // 检查是否应该禁用"添加示例"按钮
  const isAddButtonDisabled = useMemo(() => {
    if (examples.length === 0) return false;
    return examples[examples.length - 1].text.trim() === '';
  }, [examples]);

  const handleCancel = () => {
    form.resetFields();
    setExamples([]);
    setExpandedExampleIds([]);
    onClose();
  };

  // 创建新的示例对象
  const createNewExample = (): WorkflowExample => {
    return {
      text: '',
      conversable: true,
      attachments: []
    };
  };

  // 添加示例
  const handleAddExample = () => {
    if (isAddButtonDisabled) return;
    
    // 创建新示例并添加到列表
    const newExamples = [...examples, createNewExample()];
    setExamples(newExamples);
    
    // 只展开最新添加的示例，折叠其他所有示例
    const newExampleIndex = newExamples.length - 1;
    setExpandedExampleIds([newExampleIndex]);
  };

  // 删除示例
  const handleDeleteExample = (index: number) => {
    const newExamples = [...examples];
    newExamples.splice(index, 1);
    setExamples(newExamples);
    
    // 更新展开状态，移除已删除示例的ID
    setExpandedExampleIds(prev => prev.filter(id => id !== index).map(id => id > index ? id - 1 : id));
  };

  // 切换示例展开/折叠状态
  const toggleExampleExpand = (index: number) => {
    setExpandedExampleIds(prev => 
      prev.includes(index) 
        ? prev.filter(id => id !== index) 
        : [...prev, index]
    );
  };

  // 更新示例文本
  const handleExampleTextChange = (index: number, text: string) => {
    const newExamples = [...examples];
    newExamples[index].text = text;
    setExamples(newExamples);
  };

  // 更新示例可对话状态
  const handleExampleConversableChange = (index: number, conversable: boolean) => {
    const newExamples = [...examples];
    newExamples[index].conversable = conversable;
    setExamples(newExamples);
  };

  // 移除示例附件
  const handleRemoveAttachment = (exampleIndex: number, fileUid: string) => {
    const newExamples = [...examples];
    if (newExamples[exampleIndex].attachments) {
      newExamples[exampleIndex].attachments = newExamples[exampleIndex].attachments!.filter(
        att => att.url !== fileUid
      );
      setExamples(newExamples);
    }
    return true;
  };

  // 上传示例附件
  const handleUploadAttachment = async (exampleIndex: number, file: File) => {
    try {
      setLoading(true);
      const url = await uploadImageToServer(URL.createObjectURL(file), file.name);
      
      const newExamples = [...examples];
      if (!newExamples[exampleIndex].attachments) {
        newExamples[exampleIndex].attachments = [];
      }
      
      newExamples[exampleIndex].attachments!.push({
        content: file.name,
        url: url,
        filename: file.name
      });
      
      setExamples(newExamples);
      message.success(`${file.name} 上传成功`);
    } catch (error) {
      console.error('上传失败:', error);
      message.error(`${file.name} 上传失败`);
    } finally {
      setLoading(false);
    }
    return false; // 阻止自动上传
  };

  // 显示示例帮助信息
  const showExampleHelp = () => {
    Modal.info({
      title: '示例配置说明',
      content: (
        <div>
          <p><strong>示例配置用于展示知识库的功能和效果</strong></p>
          <p>您可以添加多个示例，每个示例包含以下内容：</p>
          <ul>
            <li><strong>示例文本</strong>：用户可能会问的问题或指令</li>
            <li><strong>可对话</strong>：该示例是否支持多轮对话</li>
            <li><strong>附件</strong>：可以上传图片作为示例的参考材料</li>
          </ul>
          <p>添加优质的示例可以帮助用户更好地理解知识库的功能和限制。</p>
        </div>
      ),
      width: 550,
    });
  };

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      // 过滤掉无效的示例（text为空且attachments不存在或为空的示例）
      const validExamples = examples.filter(example => {
        const hasText = example.text && example.text.trim() !== '';
        const hasAttachments = example.attachments && example.attachments.length > 0;
        return hasText || hasAttachments; // 只要有文本或有附件，就是有效示例
      });
      
      // 从表单获取可变值，其他使用默认值
      const knowledgeBaseData = {
        ...values,
        username,
        languages: ['zh'],
        search_fields: ['title', 'chunk'],
        retrieval_types: ['qq','bm25','hybrid'],
        version: false,
        medical_term_filter: false,
        search_task: "search",
        rerank_batch_size: 5,
        chunk_max_len: 448,
        chunk_min_len: 32,
        qq_retrieval_threshold: 0.6,
        llm_search_algorithm_url: "bge",
        rerank_model_name: "bge-reranker-large",
        examples: validExamples // 使用过滤后的有效示例
      };

      // 使用createData函数创建知识库
      const endpoint = `${MANAGER_API_BASE_URL}/api/v1/knowledge_bases/create_model_and_vector_model/`;
      const response = await createData(endpoint, knowledgeBaseData);

      if (response && response.id) {
        message.success('知识库创建成功');
        form.resetFields();
        setExamples([]);
        setExpandedExampleIds([]);
        onSuccess(); // 通知父组件更新列表
        onClose(); // 关闭弹窗
      } else {
        message.error('创建失败：' + (response?.detail || '未知错误'));
      }
    } catch (error) {
      console.error('创建知识库时出错:', error);
      message.error('创建知识库失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="创建新知识库"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      className={styles.createKnowledgeBaseModal}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        className={styles.formContainer}
        initialValues={{
          retrieval_topK: 50,
          top_chunk: 3,
          rerank_threshold: 3.0,
          generator_threshold: 3.0,
          is_active: true,
          is_published: true,
          multi_tenancy: false,
          llm_search_algorithm_url: "bge",
          rerank_model_name: "bge-reranker-large",
        }}
      >
        <Form.Item
          name="name"
          label="知识库名称"
          rules={[{ required: true, message: '请输入知识库名称' }]}
          className={styles.formItem}
        >
          <Input 
            placeholder="输入知识库名称" 
            className={styles.input}
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="知识库描述"
          rules={[{ required: true, message: '请输入知识库描述' }]}
          className={styles.formItem}
        >
          <Input.TextArea 
            placeholder="描述此知识库的内容和用途" 
            rows={3}
            className={styles.textArea}
          />
        </Form.Item>

        <Form.Item
          name="domain"
          label="领域"
          rules={[{ required: false, message: '请输入知识库领域' }]}
          className={styles.formItem}
        >
          <Input 
            placeholder="例如：医疗、教育、金融等" 
            className={styles.input}
          />
        </Form.Item>

        {/* 示例配置区域 */}
        <Divider orientation="left" className={styles.advancedSettingsTitle}>
          示例配置 {examples.length > 0 ? `(${examples.length})` : ''}
          <Tooltip title="查看示例配置说明">
            <QuestionCircleOutlined 
              onClick={showExampleHelp} 
              style={{ marginLeft: '8px', cursor: 'pointer' }}
            />
          </Tooltip>
        </Divider>

        <div className={styles.examplesContainer}>
          <div style={{ marginBottom: 16 }}>
            <Button 
              type="dashed" 
              onClick={handleAddExample}
              icon={<PlusOutlined />}
              className={styles.addExampleButton}
              disabled={isAddButtonDisabled}
            >
              添加示例
            </Button>
            {isAddButtonDisabled && (
              <div className={styles.disabledTip}>
                请先填写当前示例的文本内容
              </div>
            )}
          </div>
          
          {examples.length === 0 && (
            <div className={styles.emptyExamples}>
              暂无示例数据，请点击"添加示例"按钮创建
            </div>
          )}
          
          {examples.map((example, index) => {
            const isExpanded = expandedExampleIds.includes(index);
            
            return (
              <Card 
                key={index} 
                className={`${styles.exampleCard} ${isExpanded ? styles.exampleCardExpanded : ''}`}
                title={
                  <div 
                    className={styles.exampleCardTitle}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleExampleExpand(index);
                    }}
                  >
                    <span>{`示例 ${index + 1}`}</span>
                    <span className={styles.examplePreview}>
                      {!isExpanded && example.text.trim() ? 
                        (example.text.length > 30 ? example.text.slice(0, 30) + '...' : example.text) : 
                        ''
                      }
                    </span>
                  </div>
                }
                extra={
                  <div className={styles.exampleCardExtra}>
                    <Button
                      type="text"
                      icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleExampleExpand(index);
                      }}
                      className={styles.expandButton}
                    />
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteExample(index);
                      }}
                    >
                      删除
                    </Button>
                  </div>
                }
              >
                {isExpanded && (
                  <>
                    <Form.Item
                      label="示例文本"
                      className={styles.exampleFormItem}
                    >
                      <Input.TextArea
                        value={example.text}
                        onChange={(e) => handleExampleTextChange(index, e.target.value)}
                        rows={3}
                        placeholder="请输入示例文本，例如用户可能会问的问题"
                        className={styles.exampleTextArea}
                        onMouseDown={(e: React.MouseEvent) => e.stopPropagation()}
                      />
                    </Form.Item>
                    
                    <Form.Item
                      label="可对话"
                      className={styles.exampleFormItem}
                    >
                      <Switch
                        checked={example.conversable}
                        onChange={(checked) => {
                          // 阻止事件冒泡并更新状态
                          handleExampleConversableChange(index, checked);
                        }}
                      />
                    </Form.Item>
                    
                    <Form.Item
                      label="附件"
                      className={styles.exampleFormItem}
                    >
                      <Upload.Dragger
                        listType="picture-card"
                        fileList={(example.attachments || []).map(att => ({
                          uid: att.url,
                          name: att.filename,
                          status: 'done',
                          url: att.url,
                          thumbUrl: att.url
                        }))}
                        onRemove={(file) => handleRemoveAttachment(index, file.uid)}
                        beforeUpload={(file) => handleUploadAttachment(index, file)}
                        multiple={true}
                        accept="image/*"
                        showUploadList={{
                          showPreviewIcon: true,
                          showRemoveIcon: true
                        }}
                        className={styles.uploadDragger}
                      >
                        <div onClick={(e: React.MouseEvent) => e.stopPropagation()}>
                          <p className="ant-upload-drag-icon">
                            <PlusOutlined />
                          </p>
                          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                          <p className="ant-upload-hint">支持单个或批量上传图片</p>
                        </div>
                      </Upload.Dragger>
                    </Form.Item>
                  </>
                )}
              </Card>
            )
          })}
        </div>
        
        <Divider orientation="left" className={styles.advancedSettingsTitle}>高级设置</Divider>

        <Collapse 
          ghost 
          className={styles.collapsePanel} 
          defaultActiveKey={['1', '2']} // 默认展开两个面板
        >
          <Panel header="向量库设置" key="1">
            {/* <Form.Item label="默认设置" extra="知识库使用默认向量库配置">
              <Switch disabled defaultChecked />
            </Form.Item> */}
            
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="retrieval_topK"
                  label={
                    <span>
                      检索TopK
                      <Tooltip title="检索返回的最大文档数量">
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                  rules={[{ required: true, message: '请输入检索TopK值' }]}
                >
                  <InputNumber 
                    min={1} 
                    max={1000} 
                    className={styles.input}
                    defaultValue={50}
                    placeholder="50"
                  />
                </Form.Item>
              </Col>
              
              <Col span={12}>
                <Form.Item
                  name="top_chunk"
                  label={
                    <span>
                      返回块数
                      <Tooltip title="返回给大模型的文本块数量">
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                  rules={[{ required: true, message: '请输入返回块数' }]}
                >
                  <InputNumber 
                    min={1} 
                    max={20} 
                    className={styles.input}
                    defaultValue={3}
                    placeholder="3"
                  />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="rerank_threshold"
                  label={
                    <span>
                      重排阈值
                      <Tooltip title="重排模型的分数阈值，小于该阈值的文档会被过滤">
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                  rules={[{ required: true, message: '请输入重排阈值' }]}
                >
                  <InputNumber 
                    min={0} 
                    max={10} 
                    step={1} 
                    precision={1}
                    className={styles.input}
                    defaultValue={3.0}
                    placeholder="3.0"
                  />
                </Form.Item>
              </Col>
              
              <Col span={12}>
                <Form.Item
                  name="generator_threshold"
                  label={
                    <span>
                      生成器阈值
                      <Tooltip title="生成器的分数阈值，控制答案生成的质量">
                        <InfoCircleOutlined style={{ marginLeft: 8 }} />
                      </Tooltip>
                    </span>
                  }
                  rules={[{ required: true, message: '请输入生成器阈值' }]}
                >
                  <InputNumber 
                    min={0} 
                    max={10} 
                    step={0.5} 
                    precision={1}
                    className={styles.input}
                    defaultValue={3.0}
                    placeholder="3.0"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Panel>
          
          <Panel header="其他配置参数" key="2">
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item label="向量模型">
                  <Select
                    className={styles.input}
                    value="bge"
                    disabled
                  >
                    <Select.Option value="bge">BGE</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item label="重排模型">
                  <Select
                    className={styles.input}
                    value="bge-reranker-large"
                    disabled
                  >
                    <Select.Option value="bge-reranker-large">BGE Reranker (大)</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item label="语言">
                  <Input value="中文" disabled />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item label="检索字段">
                  <Input value="标题、块" disabled />
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item label="检索类型">
                  <Input value="混合检索(QQ+BM25+Hybrid)" disabled />
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item label="重排批次大小">
                  <InputNumber value={5} disabled />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item label="QQ检索阈值">
                  <InputNumber value={0.6} disabled />
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item label="块最大长度">
                  <InputNumber value={448} disabled />
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item label="块最小长度">
                  <InputNumber value={32} disabled />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item label="搜索任务">
                  <Input value="search" disabled />
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item label="版本控制">
                  <Switch checked={false} disabled />
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item label="医学术语过滤">
                  <Switch checked={false} disabled />
                </Form.Item>
              </Col>
            </Row>
          </Panel>
        </Collapse>

        <Divider orientation="left" className={styles.advancedSettingsTitle}>状态设置</Divider>
        
        <Form.Item
          name="is_active"
          label="是否激活"
          valuePropName="checked"
          extra="激活状态的知识库可以被检索和使用"
          className={styles.formItem}
        >
          <Switch disabled/>
        </Form.Item>

        <Form.Item
          name="is_published"
          label="是否公开"
          valuePropName="checked"
          extra="公开的知识库可以被其他用户查看和使用"
          className={styles.formItem}
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="multi_tenancy"
          label="多租户模式"
          valuePropName="checked"
          extra="是否支持多租户访问（默认不开启）"
          className={styles.formItem}
        >
          <Switch disabled />
        </Form.Item>

        <div className={styles.footerActions}>
          <Button 
            onClick={handleCancel} 
            className={styles.cancelButton}
          >
            取消
          </Button>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={loading}
            className={styles.submitButton}
          >
            创建
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default CreateKnowledgeBase; 