import React, { useState, useEffect } from 'react';
import { Mo<PERSON>, Spin, Empty, message, But<PERSON>, Popconfirm, Card, Select } from 'antd';
import { CHAT_API_NAME } from '../../../../Constant/Constant';
import { fetchBulk, deleteData } from '../../../api/api';
import styles from './KnowledgeBaseSelector.module.css'; // 使用自己的CSS模块
import SafeTooltip from '../../../common/SafeTooltip';
import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';
import { useSelector } from 'react-redux';
import CreateKnowledgeBase from './CreateKnowledgeBase';
import UploadFile from './UploadFile';
import { DeleteOutlined, BookOutlined, ShareAltOutlined, UploadOutlined } from '@ant-design/icons';
import { fetchMcpServers, MCPServer, isCurrentUserMcpServer as checkMcpOwnership } from '../services/McpService';

const KNOWLEDGE_BASE_WORKFLOW_IDS = [
  {
    id: '3ebcb1e3-25b7-3b4f-fa10-76a3bfa727cf',
    name: '智能问答',
    description: '基于知识库的智能问答系统，能够理解自然语言并返回准确答案,速度快',
    is_active: true,
    workflow_or_agent: 'workflow',
    icon: (
      <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        <line x1="9" y1="10" x2="15" y2="10"></line>
      </svg>
    )
  },
  {
    id: 'a9c87333-5355-caf3-2cee-9fe095bc2d2b',
    name: '推理问答',
    description: '具备逻辑推理能力的问答系统，能够处理复杂问题并给出推理过程，速度慢，准确度高',
    is_active: true,
    workflow_or_agent: 'workflow',
    icon: (
      <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
        <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
        <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
        <line x1="6" y1="1" x2="6" y2="4"></line>
        <line x1="10" y1="1" x2="10" y2="4"></line>
        <line x1="14" y1="1" x2="14" y2="4"></line>
      </svg>
    )
  },
  {
    id: '5c5681e9-6ad8-b323-42bb-f52d0c4a01a7',
    name: 'PDF智能生成工作流',
    description: '大纲->搜索->PDF（基于知识库的智能PDF生成）',
    is_active: true,
    workflow_or_agent: 'workflow',
    icon: (
      <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
        <polyline points="14 2 14 8 20 8"></polyline>
        <line x1="16" y1="13" x2="8" y2="13"></line>
        <line x1="16" y1="17" x2="8" y2="17"></line>
        <polyline points="10 9 9 9 8 9"></polyline>
      </svg>
    )
  }
]

interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  username?: string; // 添加username字段，表示知识库所属用户
}

interface KnowledgeBaseSelectorProps {
  visible: boolean;
  onClose: () => void;
  username: string;
}

const KnowledgeBaseSelector: React.FC<KnowledgeBaseSelectorProps> = ({ visible, onClose, username }) => {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [sortField] = useState<string>('updated_at');
  const [sortOrder] = useState<string>('desc');
  const [currentPage] = useState<number>(1);
  const [pageSize] = useState<number>(100); // 获取足够多的知识库
  // 添加状态来跟踪当前选中的工作流ID
  const [selectedWorkflowId, setSelectedWorkflowId] = useState<string>(KNOWLEDGE_BASE_WORKFLOW_IDS[0].id);
  // 创建知识库弹窗状态
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  // 上传文件弹窗状态
  const [uploadModalVisible, setUploadModalVisible] = useState<boolean>(false);
  const [currentKbId, setCurrentKbId] = useState<string>('');
  
  // MCP服务相关状态
  const [mcpServers, setMcpServers] = useState<MCPServer[]>([]);
  const [mcpServerLoading, setMcpServerLoading] = useState<boolean>(false);
  const [selectedMcpServers, setSelectedMcpServers] = useState<Record<string, string>>({});

  // 获取当前登录用户的信息
  const { userInfo } = useSelector((state: any) => state.user);
  const currentUsername = userInfo?.username; // 当前登录用户名

  // 获取知识库列表
  useEffect(() => {
    if (visible) {
      fetchKnowledgeBases();
      fetchMcpServerList();
    }
  }, [visible, username, currentPage, pageSize, sortField, sortOrder]);

  const fetchKnowledgeBases = async () => {
    setLoading(true);
    try {
      const skip = (currentPage - 1) * pageSize;
      const limit = pageSize;
      const KNOWLEDGE_BASE_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/knowledge_bases`;
      let endpoint_api = `${KNOWLEDGE_BASE_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${username}&is_published=true`;

      const result = await fetchBulk(endpoint_api);
      console.log(result);
      setKnowledgeBases(result.data);
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error('获取知识库列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取MCP服务列表
  const fetchMcpServerList = async () => {
    setMcpServerLoading(true);
    try {
      const result = await fetchMcpServers(username);
      setMcpServers(result.data);
    } catch (error) {
      console.error('获取MCP服务列表失败:', error);
    } finally {
      setMcpServerLoading(false);
    }
  };

  // 判断知识库是否属于当前登录用户
  const isCurrentUserKnowledgeBase = (kb: KnowledgeBase) => {
    return !kb.username || kb.username === currentUsername;
  };

  // 处理工作流选择
  const handleWorkflowSelect = (workflow: typeof KNOWLEDGE_BASE_WORKFLOW_IDS[0]) => {
    if (workflow.is_active) {
      setSelectedWorkflowId(workflow.id);
    } else {
      message.info(`${workflow.name}功能正在开发中，敬请期待`);
    }
  };

  // 处理MCP服务选择变化
  const handleMcpServerChange = (mcpServerId: string, kbId: string) => {
    setSelectedMcpServers(prev => ({
      ...prev,
      [kbId]: mcpServerId
    }));
  };

  // 处理知识库点击，跳转到聊天页面
  const handleKnowledgeBaseClick = (knowledgeBase: KnowledgeBase) => {
    // 先关闭弹窗
    onClose();
    
    // 获取选中的MCP服务ID（如果有）
    const mcpId = selectedMcpServers[knowledgeBase.id];
    
    // 构建URL - 使用选中的workflow_id，并传递知识库ID和MCP服务ID作为参数
    let url = `${CHAT_API_NAME}/${selectedWorkflowId}/new?workflow_or_agent=workflow&kb_id=${knowledgeBase.id}`;
    
    // 如果选择了MCP服务，添加到URL
    if (mcpId) {
      url += `&mcp_id=${mcpId}`;
    }
    
    // 使用window.location.href进行导航，这会导致页面完全刷新
    window.location.href = url;
  };

  // 打开创建知识库弹窗
  const handleOpenCreateModal = () => {
    setCreateModalVisible(true);
  };

  // 关闭创建知识库弹窗
  const handleCloseCreateModal = () => {
    setCreateModalVisible(false);
  };

  // 创建知识库成功的回调
  const handleCreateSuccess = () => {
    fetchKnowledgeBases(); // 重新获取知识库列表
  };

  // 处理知识库删除
  const handleDeleteKnowledgeBase = async (kb: KnowledgeBase, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击

    setLoading(true);
    try {
      const endpoint = `${MANAGER_API_BASE_URL}/api/v1/knowledge_bases/remove_mode_and_vector_model/${kb.id}`;
      const response = await deleteData(endpoint);
      
      if (response) {
        message.success(`知识库"${kb.name}"删除成功`);
        // 重新获取知识库列表
        fetchKnowledgeBases();
      } else {
        message.error('删除失败，请稍后再试');
      }
    } catch (error) {
      console.error('删除知识库时出错:', error);
      message.error('删除知识库失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理上传文件按钮点击
  const handleUploadClick = (kb: KnowledgeBase, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击
    setCurrentKbId(kb.id);
    setUploadModalVisible(true);
  };

  // 关闭上传文件弹窗
  const handleCloseUploadModal = () => {
    setUploadModalVisible(false);
  };

  // 上传文件成功后的回调
  const handleUploadSuccess = () => {
    fetchKnowledgeBases(); // 重新获取知识库列表
  };

  // 停止事件冒泡的通用函数
  const stopPropagation = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <Modal
      title="知识库选择"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      className={styles.knowledgeBaseSelectorModal}
    >
      <Spin spinning={loading}>
        <div className={styles.categoriesContainer}>
          {/* 工作流选择部分 */}
          <div className={styles.categorySection}>
            <div className={styles.categoryHeader}>
              <div className={styles.categoryIcon}>
                <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                </svg>
              </div>
              <h3 className={styles.categoryTitle}>对话模式</h3>
            </div>
            
            <div className={styles.categoryDescription}>
              选择一种对话模式，功能各有不同
            </div>
            
            <div className={styles.cardGrid}>
              {KNOWLEDGE_BASE_WORKFLOW_IDS.map(workflow => (
                <SafeTooltip key={workflow.id} title={workflow.description || '暂无描述'}>
                  <div 
                    className={`${styles.item} ${selectedWorkflowId === workflow.id ? styles.itemActive : ''} ${!workflow.is_active ? styles.itemDisabled : ''}`}
                    onClick={() => handleWorkflowSelect(workflow)}
                  >
                    <div className={styles.icon}>
                      {workflow.icon}
                    </div>
                    {workflow.name}
                    {!workflow.is_active && <span className={styles.itemComingSoon}>即将推出</span>}
                  </div>
                </SafeTooltip>
              ))}
            </div>
          </div>

          {/* 知识库选择部分 */}
          {knowledgeBases.length > 0 ? (
            <div className={styles.categorySection}>
              <div className={styles.categoryHeader}>
                <div className={styles.categoryTitleContainer}>
                  <div className={styles.categoryIcon}>
                    <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                    </svg>
                  </div>
                  <h3 className={styles.categoryTitle}>知识库</h3>
                </div>
                
                {/* 添加按钮 */}
                <button 
                  className={styles.addButton}
                  onClick={handleOpenCreateModal}
                  aria-label="添加知识库"
                >
                  <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </button>
              </div>
              
              <div className={styles.categoryDescription}>
                选择一个知识库进行对话（共享知识库接收用户上传文件）
              </div>
              
              <div className={styles.knowledgeBaseCardGrid}>
                {knowledgeBases.map(kb => {
                  // 确定是否为当前用户的知识库
                  const isCurrentUser = isCurrentUserKnowledgeBase(kb);
                  
                  return (
                    <Card
                      key={kb.id}
                      className={`${styles.knowledgeBaseCard} ${styles.knowledgeBaseCardHeight} ${!isCurrentUser ? styles.knowledgeBaseCardShared : ''}`}
                      onClick={() => handleKnowledgeBaseClick(kb)}
                      hoverable
                    >
                      <div className={styles.knowledgeBaseCardContent}>
                        <div className={styles.knowledgeBaseCardHeader}>
                          <BookOutlined className={styles.knowledgeBaseCardIcon} />
                          <span className={styles.knowledgeBaseCardTitle}>{kb.name}</span>
                          {!isCurrentUser && (
                            <span style={{ 
                              position: 'absolute', 
                              top: 4, 
                              left: 4, 
                              fontSize: 10, 
                              backgroundColor: '#faad14', 
                              color: 'white',
                              padding: '1px 4px',
                              borderRadius: 4,
                              opacity: 0.8
                            }}>
                              <ShareAltOutlined /> 共享
                            </span>
                          )}
                        </div>
                        
                        <SafeTooltip 
                          title={
                            <div>
                              <div>{kb.description || '暂无描述'}</div>
                              {!isCurrentUser && kb.username && (
                                <div style={{ marginTop: 8, color: '#faad14' }}>
                                  所有者: {kb.username}
                                </div>
                              )}
                            </div>
                          }
                        >
                          <div className={styles.knowledgeBaseCardDescription}>
                            {kb.description || '暂无描述'}
                          </div>
                        </SafeTooltip>
                        
                        {/* MCP服务选择器 */}
                        <div className={styles.mcpServerSelector} onClick={stopPropagation}>
                          <Select
                            placeholder="选择数据集,补充知识"
                            style={{ width: '100%' }}
                            value={selectedMcpServers[kb.id]}
                            onChange={(value) => handleMcpServerChange(value, kb.id)}
                            loading={mcpServerLoading}
                            allowClear
                          >
                            {mcpServers.map(server => (
                              <Select.Option key={server.id} value={server.id}>
                                {server.name} {!checkMcpOwnership(server, currentUsername) && '(共享)'}
                              </Select.Option>
                            ))}
                          </Select>
                        </div>
                        
                        {/* 底部操作按钮 - 仅对当前用户的知识库显示 */}
                        <div className={styles.knowledgeBaseCardFooter}>
                          {isCurrentUser && (
                            <>
                              <Popconfirm
                                title="确定要删除此知识库吗？"
                                description="此操作不可逆，删除后数据将无法恢复"
                                okText="删除"
                                cancelText="取消"
                                okButtonProps={{ danger: true }}
                                onConfirm={(e) => handleDeleteKnowledgeBase(kb, e as React.MouseEvent)}
                                onCancel={(e) => e?.stopPropagation()}
                                placement="topLeft"
                              >
                                <Button 
                                  danger 
                                  type="text"
                                  size="small"
                                  icon={<DeleteOutlined />} 
                                  className={styles.knowledgeBaseCardDeleteBtn}
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  删除
                                </Button>
                              </Popconfirm>
                              
                              <Button
                                type="text"
                                size="small"
                                icon={<UploadOutlined />}
                                className={styles.knowledgeBaseCardUploadBtn}
                                onClick={(e) => handleUploadClick(kb, e)}
                              >
                                上传文件
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>
          ) : (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE} 
              description="暂无可用的知识库" 
              className={styles.empty}
            />
          )}
        </div>
      </Spin>

      {/* 创建知识库弹窗 */}
      <CreateKnowledgeBase
        visible={createModalVisible}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {/* 上传文件弹窗 */}
      <UploadFile
        visible={uploadModalVisible}
        onClose={handleCloseUploadModal}
        kbId={currentKbId}
        onSuccess={handleUploadSuccess}
      />
    </Modal>
  );
};

export default KnowledgeBaseSelector; 