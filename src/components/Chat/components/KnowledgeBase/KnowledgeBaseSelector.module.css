/* 复用workflowSelector的大部分样式，增加特定于KnowledgeBase的样式 */
.knowledgeBaseSelectorModal {
  font-family: 'Inter', system-ui, sans-serif;
}

.knowledgeBaseSelectorModal :global(.ant-modal-content) {
  border-radius: 12px;
  overflow: hidden;
  background-color: #f5f7fa;
}

.knowledgeBaseSelectorModal :global(.ant-modal-header) {
  padding: 16px 24px;
  background: linear-gradient(90deg, #001529, #002140);
  border-bottom: 1px solid rgba(0, 120, 255, 0.15);
}

.knowledgeBaseSelectorModal :global(.ant-modal-title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.knowledgeBaseSelectorModal :global(.ant-modal-close) {
  color: rgba(255, 255, 255, 0.65);
}

.knowledgeBaseSelectorModal :global(.ant-modal-close:hover) {
  color: rgba(255, 255, 255, 0.95);
}

.knowledgeBaseSelectorModal :global(.ant-modal-body) {
  padding: 24px;
}

/* 分类容器 */
.categoriesContainer {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 分类区域 */
.categorySection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 分类标题和操作区 */
.categoryHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.categoryTitleContainer {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.categoryIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: #FFF6E8;
  color: #FA8C16;
}

/* 不同类型使用不同的颜色 */
.categorySection:nth-child(1) .categoryIcon {
  background-color: #FFF6E8;
  color: #FA8C16;
}

.categorySection:nth-child(2) .categoryIcon {
  background-color: #FCF4F2;
  color: #F5222D;
}

.categoryTitle {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin: 0;
}

.addButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  outline: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.addButton:hover {
  background-color: #40a9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.addButton svg {
  width: 18px;
  height: 18px;
}

.categoryDescription {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  cursor: help;
  padding-left: 48px;
  margin-top: -12px;
}

/* 项目卡片网格 */
.cardGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  padding-left: 48px;
}

@media (max-width: 992px) {
  .cardGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .cardGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .cardGrid {
    grid-template-columns: 1fr;
  }
}

/* 单个项目 */
.item {
  padding: 12px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.06);
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  transition: all 0.3s;
  cursor: pointer;
  height: 56px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  overflow: hidden;
  gap: 8px;
}

.item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: rgba(0, 120, 255, 0.3);
}

/* 高亮显示被选中的项目 */
.itemActive {
  background-color: #e6f7ff;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
  font-weight: 600;
  color: #1890ff;
  transform: scale(1.02);
  position: relative;
  z-index: 1;
}

.itemActive::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #1890ff;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

/* 图标样式 */
.icon {
  width: 24px;
  height: 24px;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 非当前用户的项目样式 */
.itemOtherUser {
  background-color: #fffbe6;
  border-color: #ffd666;
}

.itemOtherUser:hover {
  border-color: #ffc107;
  box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
}

/* 非当前用户的项被选中时 */
.itemOtherUserActive {
  background-color: #fff1c2;
  border-color: #faad14;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.3);
}

.itemOtherUserActive::before {
  background-color: #faad14;
}

/* 空状态 */
.empty {
  grid-column: 1 / -1;
  padding: 24px;
}

/* 知识库卡片相关样式 */
.knowledgeBaseCardGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding-left: 48px;
}

@media (max-width: 992px) {
  .knowledgeBaseCardGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .knowledgeBaseCardGrid {
    grid-template-columns: repeat(1, 1fr);
  }
}

.knowledgeBaseCard {
  width: 100%;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.knowledgeBaseCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: rgba(0, 120, 255, 0.3);
}

.knowledgeBaseCardShared {
  background-color: #fffbe6;
  border-color: #ffd666;
}

.knowledgeBaseCardShared:hover {
  border-color: #ffc107;
  box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
}

.knowledgeBaseCardContent {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px;
}

.knowledgeBaseCardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.knowledgeBaseCardIcon {
  font-size: 20px;
  color: #1890ff;
  margin-right: 8px;
}

.knowledgeBaseCardTitle {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.knowledgeBaseCardSharedTag {
  font-size: 12px;
  padding: 2px 6px;
  background-color: #faad14;
  color: white;
  border-radius: 4px;
  margin-left: 8px;
}

.knowledgeBaseCardDescription {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  flex: 1;
}

.knowledgeBaseCardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
  width: 100%;
}

.knowledgeBaseCardDeleteBtn {
  color: #ff4d4f;
}

.knowledgeBaseCardUploadBtn {
  color: #1890ff;
}

/* MCP服务选择器样式 */
.mcpServerSelector {
  margin-top: auto;
  margin-bottom: 12px;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
  width: 100%;
}

.mcpServerLabel {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.knowledgeBaseCardHeight {
  height: 220px; /* 增加卡片高度以容纳MCP选择器 */
} 