import React, { useState } from 'react';
import { Modal, Upload, Button, message, Form, Switch, Spin } from 'antd';
import { InboxOutlined, LoadingOutlined } from '@ant-design/icons';
import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';
import type { UploadFile as AntdUploadFile } from 'antd/es/upload/interface';

export const FILE_MANAGER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/file-managers`;

interface UploadFileProps {
  visible: boolean;
  onClose: () => void;
  kbId: string;
  onSuccess?: () => void;
}

const UploadFile: React.FC<UploadFileProps> = ({ visible, onClose, kbId, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<AntdUploadFile[]>([]);

  const uploadProps = {
    name: 'file',
    multiple: false,
    action: `${FILE_MANAGER_ENDPOINT}/upload_file_v2/`,
    headers: {
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
    data: {
      is_background: form.getFieldValue('is_background') || 'true',
      kb_id: kbId,
    },
    fileList: fileList,
    onChange(info: any) {
      // 更新 fileList 状态
      let newFileList = [...info.fileList];
      
      // 限制只显示最新上传的一个文件
      newFileList = newFileList.slice(-1);
      
      // 设置文件列表
      setFileList(newFileList);
      
      if (info.file.status === 'uploading') {
        setLoading(true);
      }
      if (info.file.status === 'done') {
        setLoading(false);
        // 上传成功时也清空文件列表
        setFileList([]);
        message.success(`${info.file.name} 文件上传成功`);
        if (onSuccess) onSuccess();
        onClose();
      } else if (info.file.status === 'error') {
        setLoading(false);
        message.error(`${info.file.name} 文件上传失败`);
        // 上传失败时立即移除文件
        setFileList([]);
      }
    },
    onRemove: () => {
      setFileList([]);
      return true;
    },
    beforeUpload(file: File) {
      // 检查文件类型
      const allowedTypes = [
        'application/pdf', 
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-powerpoint',
        'text/plain'
      ];
      
      const isAllowedType = allowedTypes.includes(file.type);
      if (!isAllowedType) {
        message.error('只支持上传PDF、Excel、Word、PPT和文本文件！');
      }
      
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('文件大小不能超过100MB！');
      }
      
      return isAllowedType && isLt100M;
    }
  };

  // 关闭弹窗时清空文件列表
  const handleClose = () => {
    if (loading) {
      message.warning('文件正在上传中，请稍候...');
      return;
    }
    setFileList([]);
    onClose();
  };

  // 上传加载图标
  const uploadLoadingIcon = (
    <LoadingOutlined style={{ fontSize: 50, color: '#1890ff' }} spin />
  );

  return (
    <Modal
      title="上传文件到知识库"
      open={visible}
      onCancel={handleClose}
      confirmLoading={loading}
      footer={[
        <Button key="cancel" onClick={handleClose} disabled={loading}>
          取消
        </Button>
      ]}
      width={600}
      maskClosable={!loading}
      closable={!loading}
    >
      <Form form={form} layout="vertical" initialValues={{ is_background: true }}>
        <Form.Item
          name="is_background"
          label="后台处理"
          valuePropName="checked"
          tooltip="开启后台处理可以在文件处理过程中继续其他操作"
        >
          <Switch defaultChecked disabled={loading} />
        </Form.Item>
        
        <Form.Item>
          <Spin spinning={loading} indicator={uploadLoadingIcon} tip="文件上传中，请稍候...">
            <Upload.Dragger {...uploadProps} disabled={loading}>
              {loading ? (
                <div style={{ padding: '20px 0' }}>
                  <p className="ant-upload-text">文件上传中，请稍候...</p>
                </div>
              ) : (
                <>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持PDF、Excel、Word、PPT和文本文件，单个文件不超过100MB
                  </p>
                </>
              )}
            </Upload.Dragger>
          </Spin>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UploadFile; 