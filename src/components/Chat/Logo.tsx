// src/components/Logo/Logo.tsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './Logo.module.css';
import { WORKFLOWS_API_NAME } from '../../Constant/Constant';

const Logo: React.FC = () => {
  const navigate = useNavigate();
  
  const handleLogoClick = () => {
    navigate(`${WORKFLOWS_API_NAME}`); // 返回上一页
  };
  
  return (
    <div className={styles.logo} onClick={handleLogoClick}>
      <img
        src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*eco6RrQhxbMAAAAAAAAAAAAADgCCAQ/original"
        draggable={false}
        alt="logo"
      />
      <span>燧石</span>
    </div>
  );
};

export default Logo;

