/* src/components/Chat/PlaceholderNode.module.css */
.placeholder {
    /* 根据你原先在 Chat.module.css 里对 placeholderNode 的样式进行修改或复制 */
    max-width: 100% !important;
    min-width: 600px !important;
    margin: 0 auto !important;
    text-align: center !important;
    padding: 20px !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    width: 100% !important;
}

/* 设置提示项的宽度 */
.customPrompts {
    width: 100% !important;
    min-width: 600px !important;
    max-width: 700px !important;
    margin: 0 auto !important;
}

/* 覆盖Ant Design样式 */
.customPrompts :global(.ant-prompts-item),
.customPrompts :global(.ant-prompts-item-has-nest) {
    width: 100% !important;
}

.welcomeContainer {
    width: 100% !important;
    min-width: 600px !important;
    max-width: 700px !important;
    margin: 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    box-sizing: border-box !important;
}

/* 确保Welcome组件内部元素也居中 */
.welcomeContainer :global(.ant-welcome) {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    width: 100% !important;
    min-width: 600px !important;
    box-sizing: border-box !important;
}

.welcomeContainer :global(.ant-welcome-title),
.welcomeContainer :global(.ant-welcome-description) {
    text-align: center !important;
    width: 100% !important;
}

.truncatedDescription {
    cursor: pointer !important;
    position: relative !important;
    display: inline-block !important;
    color: rgba(0, 0, 0, 0.88) !important;
    text-align: center !important;
    width: 100% !important;
    min-width: 600px !important;
    max-width: 600px !important;
    margin: 0 auto !important;
    box-sizing: border-box !important;
}

.truncatedDescription:hover {
    color: #1677ff !important;
    text-decoration: underline !important;
}
  