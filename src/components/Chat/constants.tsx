// src/components/Independent/constants.tsx

// import { renderTitle } from './helpers';
import {
  FireOutlined,
  ReadOutlined,
  // HeartOutlined,
  // SmileOutlined,
  // CommentOutlined,
} from '@ant-design/icons';
import { GetProp } from 'antd';
import { Prompts, Bubble } from '@ant-design/x';
import { UserOutlined } from '@ant-design/icons';



// 发送者提示项
export const senderPromptsItems: GetProp<typeof Prompts, 'items'> = [
  {
    key: '1',
    description: 'Hot Topics',
    icon: <FireOutlined style={{ color: '#FF4D4F' }} />,
  },
  {
    key: '2',
    description: 'Design Guide',
    icon: <ReadOutlined style={{ color: '#1890FF' }} />,
  },
];

// 消息角色配置
export const roles: GetProp<typeof Bubble.List, 'roles'> = {
  ai: {
    placement: 'start',
    avatar: { 
      icon: <FireOutlined />, 
      style: { 
        background: '#fde3cf',
        fontSize: '16px'
      }
    },
    style: {
      maxWidth: '100%',
    }
  },
  local: {
    placement: 'end',
    avatar: { 
      icon: <UserOutlined />, 
      style: { 
        background: '#87d068',
        fontSize: '16px'
      }
    },
    style: {
      maxWidth: '100%',
    }
  },
};
