import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from 'antd';
import Conversations from '../Conversations/Conversations';
import { 
  KnowledgeBaseIcon,
  DatasetIcon, 
  WorkflowIcon, 
  AgentIcon, 
  HistoryIcon, 
  NewChatIcon,
  BackIcon,
  CollapseIcon,
  SettingsIcon
} from './SidebarIcons';
import styles from './Sidebar.module.css';
import { WORKFLOWS_API_NAME, AGENTS_API_NAME } from '../../Constant/Constant';
import { LogoutOutlined, HomeOutlined, MessageOutlined } from '@ant-design/icons';
import { fetchData } from '../api/api';
import { WORKFLOW_ENDPOINT, AGENT_ENDPOINT } from '../../Constant/RouterConstant';
import WorkflowSelector from './WorkflowSelector';
import AgentSelector from './AgentSelector';
import KnowledgeBaseSelector from './components/KnowledgeBase/KnowledgeBaseSelector';
import MCPServerSelector from './components/McpServer/MCPServerSelector';
import { useSelector } from 'react-redux';
import SafeTooltip from '../common/SafeTooltip';
import SafeDropdown from '../common/SafeDropdown';

interface ConversationItem {
  id: string;
  messages: any[];
  title: string;
  time: string;
}

interface SidebarProps {
  conversationItems: ConversationItem[];
  activeKey: string;
  workflowId: string;
  onConversationClick: (key: string) => void;
  onAddConversation: () => void;
  onMessagesUpdate: (messages: any[]) => void;
  setItems: React.Dispatch<React.SetStateAction<ConversationItem[]>>;
  collapsed?: boolean; // 可选的外部控制的折叠状态
  fetchConversationMessages?: (sessionId: string) => Promise<any[]>;
}

// 侧边栏选项卡类型
type TabType = 'conversation' | 'dataset' | 'kb' | 'workflow' | 'agent' | 'history';

const Sidebar: React.FC<SidebarProps> = ({
  conversationItems,
  activeKey,
  workflowId,
  onConversationClick,
  onAddConversation,
  onMessagesUpdate,
  setItems,
  collapsed: externalCollapsed,
  fetchConversationMessages
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('conversation');
  const [showHistoryView, setShowHistoryView] = useState<boolean>(false);
  const [internalCollapsed, setInternalCollapsed] = useState<boolean>(false);
  // 使用外部传入的collapsed状态或内部状态
  const collapsed = externalCollapsed !== undefined ? externalCollapsed : internalCollapsed;
  const [workflowAgentName, setWorkflowAgentName] = useState<string>('');
  const [workflowAgentDesc, setWorkflowAgentDesc] = useState<string>('');
  // const [workflowOrAgent, setWorkflowOrAgent] = useState<'workflow' | 'agent'>('workflow');
  const [showWorkflowSelector, setShowWorkflowSelector] = useState<boolean>(false);
  const [showAgentSelector, setShowAgentSelector] = useState<boolean>(false);
  const [showKnowledgeBaseSelector, setShowKnowledgeBaseSelector] = useState<boolean>(false);
  const [showMCPServerSelector, setShowMCPServerSelector] = useState<boolean>(false);
  
  // 从Redux获取用户信息
  const { userInfo } = useSelector((state: any) => state.user);
  const username = userInfo?.username || '';

  // 从localStorage获取之前的折叠状态
  useEffect(() => {
    const savedCollapsed = localStorage.getItem('sidebar_collapsed');
    if (savedCollapsed !== null) {
      setInternalCollapsed(savedCollapsed === 'true');
    }
    
    // 添加ESC键切换侧边栏折叠状态的功能（仅在开发环境中使用）
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        toggleCollapse();
      }
    };
    
    if (process.env.NODE_ENV === 'development') {
      window.addEventListener('keydown', handleKeyDown);
    }
    
    // 清理事件监听器
    return () => {
      if (process.env.NODE_ENV === 'development') {
        window.removeEventListener('keydown', handleKeyDown);
      }
    };
  }, [collapsed]); // 添加collapsed到依赖数组中

  // 获取工作流或智能体信息
  useEffect(() => {
    if (!workflowId || workflowId === 'new') return;

    // 从URL中获取workflow_or_agent参数
    const urlParams = new URLSearchParams(window.location.search);
    const typeParam = urlParams.get('workflow_or_agent') || 'workflow';
    // setWorkflowOrAgent(typeParam as 'workflow' | 'agent');
    
    const fetchWorkflowOrAgentInfo = async () => {
      try {
        // 根据类型决定使用哪个端点
        const endpoint = typeParam === 'workflow' ? WORKFLOW_ENDPOINT : AGENT_ENDPOINT;
        const data = await fetchData(`${endpoint}/${workflowId}`);
        
        if (data) {
          setWorkflowAgentName(data.name || '');
          setWorkflowAgentDesc(data.description || '');
        }
      } catch (error) {
        console.error(`获取${typeParam}信息失败:`, error);
      }
    };

    fetchWorkflowOrAgentInfo();
  }, [workflowId]);

  const handleTabClick = (tab: TabType) => {
    setActiveTab(tab);
    // 处理不同选项卡的跳转逻辑
    switch (tab) {
      case 'conversation':
        // 点击"新增对话"时，直接创建新对话
        onAddConversation();
        break;
      case 'workflow':
        // 显示工作流选择器弹窗
        setShowWorkflowSelector(true);
        break;
      case 'agent':
        // 显示智能体选择器弹窗
        setShowAgentSelector(true);
        break;
      case 'kb':
        // 显示知识库选择器弹窗
        setShowKnowledgeBaseSelector(true);
        break;
      case 'dataset':
        // 显示MCP服务选择器弹窗
        setShowMCPServerSelector(true);
        break;
      case 'history':
        // 显示历史会话视图
        setShowHistoryView(true);
        break;
      // 其他选项卡暂时先保持在当前页面，只切换状态
      default:
        break;
    }
  };
  
  // 处理返回按钮点击，返回主侧边栏
  const handleBackToMain = () => {
    setShowHistoryView(false);
    // 回到会话选项卡
    setActiveTab('conversation');
  };

  // 折叠/展开侧边栏
  const toggleCollapse = () => {
    // 只有在没有外部控制时才允许切换
    if (externalCollapsed === undefined) {
      const newCollapsedState = !collapsed;
      setInternalCollapsed(newCollapsedState);
      // 保存折叠状态到localStorage
      localStorage.setItem('sidebar_collapsed', String(newCollapsedState));
      
      // 触发一个自定义事件，这样其他组件可以监听到状态变化
      window.dispatchEvent(new Event('storage'));
    }
  };

  // 处理项目管理相关事件
  const handleLogout = () => {
    localStorage.removeItem('access_token');
    window.location.href = '/login';
  };

  const handleBackHome = () => {
    window.location.href = '/home';
  };
  const handleChat = () => {
    window.location.href = '/chat/new/new?workflow_or_agent=workflow';
  };
  const handleWorkSpace = () => {
    window.location.href = `${WORKFLOWS_API_NAME}`;
  };
  
  const handleAgents = () => {
    window.location.href = `${AGENTS_API_NAME}`;
  };

  const settingsMenuItems = [
    {
      key: 'chat',
      icon: <MessageOutlined />,
      label: '会话',
      onClick: handleChat,
    },
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '返回主页',
      onClick: handleBackHome,
    },
    {
      key: 'workspace',
      icon: <WorkflowIcon />,
      label: '工作空间',
      onClick: handleWorkSpace,
    },
    {
      key: 'agents',
      icon: <AgentIcon />,
      label: '智能体',
      onClick: handleAgents,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <div className={`${styles.sidebar} ${collapsed ? styles.collapsed : ''}`}>
      {showHistoryView ? (
        // 历史会话视图
        <>
          <div className={styles.historyHeader}>
            <Button 
              type="text" 
              icon={<BackIcon />}
              className={styles.backButton} 
              onClick={handleBackToMain}
            >
              {!collapsed && <span className={styles.buttonText}>返回菜单</span>}
            </Button>
          </div>
          <div className={styles.historyContent}>
            <Button
              onClick={onAddConversation}
              type="default"
              className={styles.historyAddBtn}
              icon={<NewChatIcon />}
            >
              {!collapsed && <span>新对话</span>}
            </Button>
            <Conversations
              items={conversationItems}
              activeKey={activeKey}
              className={styles.historyConversations}
              onActiveChange={onConversationClick}
              onMessagesUpdate={onMessagesUpdate}
              setItems={setItems}
              fetchConversationMessages={fetchConversationMessages}
            />
          </div>
        </>
      ) : (
        // 主侧边栏视图
        <>
          <div className={styles.sidebarHeader}>
            {!collapsed && (
              <>
                <h3 className={styles.sidebarTitle}>慧助手</h3>
                {workflowAgentName && (
                  <SafeTooltip title={`${workflowAgentName}${workflowAgentDesc ? `: ${workflowAgentDesc}` : ''}`}>
                    <span className={styles.workflowAgentLabel}>
                      {workflowAgentName.length > 20 ? 
                        `${workflowAgentName.substring(0, 20)}...` : 
                        workflowAgentName}
                    </span>
                  </SafeTooltip>
                )}
              </>
            )}
          </div>
          <div className={styles.sidebarTabs}>
            <SafeTooltip title="新增对话" placement={collapsed ? "right" : "bottom"}>
              <div
                className={`${styles.tabItem} ${activeTab === 'conversation' ? styles.active : ''}`}
                onClick={() => handleTabClick('conversation')}
              >
                <NewChatIcon />
                {!collapsed && <span className={styles.tabText}>新增对话</span>}
              </div>
            </SafeTooltip>
            
            <SafeTooltip title="数据集" placement={collapsed ? "right" : "bottom"}>
              <div
                className={`${styles.tabItem} ${activeTab === 'dataset' ? styles.active : ''}`}
                onClick={() => handleTabClick('dataset')}
              >
                <DatasetIcon />
                {!collapsed && <span className={styles.tabText}>数据集</span>}
              </div>
            </SafeTooltip>
            
            <SafeTooltip title="知识库" placement={collapsed ? "right" : "bottom"}>
              <div
                className={`${styles.tabItem} ${activeTab === 'kb' ? styles.active : ''}`}
                onClick={() => handleTabClick('kb')}
              >
                <KnowledgeBaseIcon />
                {!collapsed && <span className={styles.tabText}>知识库</span>}
              </div>
            </SafeTooltip>
            
            <SafeTooltip title="工作流" placement={collapsed ? "right" : "bottom"}>
              <div
                className={`${styles.tabItem} ${activeTab === 'workflow' ? styles.active : ''}`}
                onClick={() => handleTabClick('workflow')}
              >
                <WorkflowIcon />
                {!collapsed && <span className={styles.tabText}>工作流</span>}
              </div>
            </SafeTooltip>
            
            <SafeTooltip title="智能体" placement={collapsed ? "right" : "bottom"}>
              <div
                className={`${styles.tabItem} ${activeTab === 'agent' ? styles.active : ''}`}
                onClick={() => handleTabClick('agent')}
              >
                <AgentIcon />
                {!collapsed && <span className={styles.tabText}>智能体</span>}
              </div>
            </SafeTooltip>
            
            <SafeTooltip title="历史会话" placement={collapsed ? "right" : "bottom"}>
              <div
                className={`${styles.tabItem} ${activeTab === 'history' ? styles.active : ''}`}
                onClick={() => handleTabClick('history')}
              >
                <HistoryIcon />
                {!collapsed && <span className={styles.tabText}>历史会话</span>}
              </div>
            </SafeTooltip>
          </div>
        </>
      )}
      
      {/* 折叠/展开按钮 */}
      <div className={styles.collapseButton} onClick={toggleCollapse}>
        <CollapseIcon collapsed={collapsed} />
      </div>
      
      {/* 项目管理固定在底部，不管哪个视图都显示 */}
      <div className={styles.sidebarFooter}>
        <div className={styles.footerSettings}>
          <SafeTooltip title="主菜单" placement={collapsed ? 'right' : 'top'}>
            <SafeDropdown menu={{ items: settingsMenuItems }} placement="topRight">
              <div className={styles.tabItem}>
                <SettingsIcon />
                {!collapsed && <span className={styles.tabText}>主菜单</span>}
              </div>
            </SafeDropdown>
          </SafeTooltip>
        </div>
      </div>
      
      {/* 添加工作流选择器组件 */}
      <WorkflowSelector 
        visible={showWorkflowSelector}
        onClose={() => setShowWorkflowSelector(false)}
        username={username}
      />

      {/* 添加智能体选择器组件 */}
      <AgentSelector 
        visible={showAgentSelector}
        onClose={() => setShowAgentSelector(false)}
        username={username}
      />

      {/* 添加知识库选择器组件 */}
      <KnowledgeBaseSelector
        visible={showKnowledgeBaseSelector}
        onClose={() => setShowKnowledgeBaseSelector(false)}
        username={username}
      />

      {/* 添加MCP服务选择器组件 */}
      <MCPServerSelector
        visible={showMCPServerSelector}
        onClose={() => setShowMCPServerSelector(false)}
        username={username}
      />
    </div>
  );
};

export default Sidebar; 