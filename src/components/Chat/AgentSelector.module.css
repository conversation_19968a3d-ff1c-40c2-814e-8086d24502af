.agentSelectorModal {
  font-family: 'Inter', system-ui, sans-serif;
}

.agentSelectorModal :global(.ant-modal-content) {
  border-radius: 12px;
  overflow: hidden;
  background-color: #f5f7fa;
}

.agentSelectorModal :global(.ant-modal-header) {
  padding: 16px 24px;
  background: linear-gradient(90deg, #001529, #002140);
  border-bottom: 1px solid rgba(0, 120, 255, 0.15);
}

.agentSelectorModal :global(.ant-modal-title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.agentSelectorModal :global(.ant-modal-close) {
  color: rgba(255, 255, 255, 0.65);
}

.agentSelectorModal :global(.ant-modal-close:hover) {
  color: rgba(255, 255, 255, 0.95);
}

.agentSelectorModal :global(.ant-modal-body) {
  padding: 24px;
}

/* 分类容器 */
.agentCategoriesContainer {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 分类区域 */
.categorySection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 分类标题 */
.categoryHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.categoryIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: #FFF6E8;
  color: #FA8C16;
}

/* 不同类型使用不同的颜色 */
.categorySection:nth-child(1) .categoryIcon {
  background-color: #FFF6E8;
  color: #FA8C16;
}

.categorySection:nth-child(2) .categoryIcon {
  background-color: #FCF4F2;
  color: #F5222D;
}

.categorySection:nth-child(3) .categoryIcon {
  background-color: #F0F5FF;
  color: #2F54EB;
}

.categorySection:nth-child(4) .categoryIcon {
  background-color: #F6FFED;
  color: #52C41A;
}

.categorySection:nth-child(5) .categoryIcon {
  background-color: #E6F7FF;
  color: #1890FF;
}

.categorySection:nth-child(6) .categoryIcon {
  background-color: #F9F0FF;
  color: #722ED1;
}

.categoryTitle {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin: 0;
}

.categoryDescription {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  cursor: help;
  padding-left: 48px;
  margin-top: -12px;
}

/* 智能体卡片网格 */
.agentCardGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding-left: 48px;
}

@media (max-width: 768px) {
  .agentCardGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .agentCardGrid {
    grid-template-columns: 1fr;
  }
}

/* 单个智能体项目 */
.agentItem {
  padding: 16px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.06);
  color: rgba(0, 0, 0, 0.85);
  font-size: 15px;
  transition: all 0.3s;
  cursor: pointer;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.agentItem:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: rgba(0, 120, 255, 0.3);
}

/* 非当前用户的智能体项目样式 */
.agentItemOtherUser {
  background-color: #fffbe6;
  border-color: #ffd666;
}

.agentItemOtherUser:hover {
  border-color: #ffc107;
  box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
}

/* 非当前用户的智能体被选中时 */
.agentItemOtherUserActive {
  background-color: #fff1c2;
  border-color: #faad14;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.3);
}

/* 空状态 */
.emptyAgent {
  grid-column: 1 / -1;
  padding: 24px;
} 