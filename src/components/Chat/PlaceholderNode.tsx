// src/components/Chat/PlaceholderNode.tsx

import React, { useEffect, useState } from 'react';
import { Space, Spin } from 'antd';
import { Welcome, Prompts } from '@ant-design/x';
import { useParams, useSearchParams } from 'react-router-dom';

// 你可能需要这个常量：
// import { placeholderPromptsItems } from './constants';

// 样式文件
import styles from './PlaceholderNode.module.css';
import { fetchData } from '../api/api';
import { renderTitle } from './helpers';
// import { placeholderPromptsItems } from './constants';
import {
  FireOutlined,
} from '@ant-design/icons';
import { WORKFLOW_ENDPOINT, AGENT_ENDPOINT, KNOWLEDGE_BASE_ENDPOINT, MCP_SERVERS_ENDPOINT } from '../../Constant/RouterConstant';
import SafeTooltip from '../common/SafeTooltip';

interface WorkflowExample {
  text: string;
  conversable: boolean;
  attachments?: {
    content: string;
    url: string;
    filename: string;
  }[];
}

/**
 * 接口：根据实际情况决定是否需要更多 props
 */
interface PlaceholderNodeProps {
  onPromptsItemClick: (info: any) => void;
}

const PlaceholderNode: React.FC<PlaceholderNodeProps> = ({ onPromptsItemClick }) => {
  const [promptsItems, setPromptsItems] = useState<any>([]);
  const [welcomeTitle, setWelcomeTitle] = useState<string>("欢迎使用Fire LLM助手");
  const [welcomeDescription, setWelcomeDescription] = useState<string>("从左侧选择一个工作流，智能体，知识库或数据集开始对话");
  const [loading, setLoading] = useState<boolean>(false);
  const [examples, setExamples] = useState<WorkflowExample[]>([]);
  
  // 获取URL参数
  const { workflow_id } = useParams<{ workflow_id: string }>();
  const [searchParams] = useSearchParams();
  const workflowOrAgent = searchParams.get('workflow_or_agent') || 'workflow';
  // 添加获取kb_id和mcp_id参数的逻辑
  const kb_id = searchParams.get('kb_id');
  const mcp_id = searchParams.get('mcp_id');
  
  // console.log(workflow_id, 'workflow_id1111111');
  // console.log(workflowOrAgent, 'workflowOrAgent');
  
  // 截取描述前50个字符
  const truncateDescription = (description: string): string => {
    if (!description) return '';
    if (description.length <= 50) return description;
    return `${description.slice(0, 100)}...`;
  };

  // 处理提示点击事件
  const handlePromptsItemClick = (info: any) => {
    // 确保兼容不同的info结构
    const description = info.description || (info.data && info.data.description);
    console.log('处理提示点击事件，description:', description);
    console.log('完整info对象:', info);
    
    // 查找对应的example
    if (description && examples.length > 0) {
      const matchedExample = examples.find(example => example.text === description);
      console.log('matched example:', matchedExample);
      // 如果找到匹配的example并且有attachments，将其添加到info中
      if (matchedExample && matchedExample.attachments) {
        console.log('附件信息:', matchedExample.attachments);
        const enhancedInfo = {
          ...info,
          attachments: matchedExample.attachments
        };
        console.log('增强后的info对象:', enhancedInfo);
        onPromptsItemClick(enhancedInfo);
        return;
      }
    }
    
    // 如果没有找到匹配的example或没有attachments，直接传递原始info
    console.log('没有找到匹配的example或无附件');
    onPromptsItemClick(info);
  };
  
  useEffect(() => {
    // 优先检查kb_id和mcp_id
    if (kb_id || mcp_id) {
      const getDataByType = async () => {
        setLoading(true);
        try {
          // 根据优先级决定使用哪个ID获取数据 (kb_id优先)
          const endpoint = kb_id ? `${KNOWLEDGE_BASE_ENDPOINT}/${kb_id}` : `${MCP_SERVERS_ENDPOINT}/${mcp_id}`;
          const dataType = kb_id ? "知识库" : "数据集";
          
          console.log(`获取${dataType}数据从: ${endpoint}`);
          const data = await fetchData(endpoint);
          
          if (data) {
            // 设置欢迎信息
            setWelcomeTitle(data.name);
            setWelcomeDescription(data.description || `这是一个${dataType}`);
            
            // 如果有examples，处理并设置
            if (data.examples?.length > 0) {
              setExamples(data.examples);
              
              // 设置推荐问题
              const compatiblePrompts = [{
                key: '1',
                label: renderTitle(<FireOutlined style={{ color: '#FF4D4F' }} />, '推荐问题'),
                children: data.examples.map((example: WorkflowExample, index: number) => ({
                  key: `1-${index+1}`,
                  description: example.text
                }))
              }];
              setPromptsItems(compatiblePrompts);
            }
          }
        } catch (error) {
          console.error(`获取${kb_id ? '知识库' : '数据集'}数据失败:`, error);
        } finally {
          setLoading(false);
        }
      };
      
      getDataByType();
      return; // 提前返回，不执行后面的workflow/agent数据获取
    }

    // 原有的workflow或agent逻辑（当kb_id和mcp_id都不存在时执行）
    if (!workflow_id || workflow_id === 'new') {
      console.log('无有效ID');
      return; // Early return pattern to avoid nesting
    }
  
    const getWorkflowData = async () => {
      setLoading(true);
      try {
        // Determine endpoint based on type
        const endpoint = workflowOrAgent === 'workflow' ? WORKFLOW_ENDPOINT : AGENT_ENDPOINT;
        const data = await fetchData(`${endpoint}/${workflow_id}`);
        
        if (data) {
          // Set welcome message
          setWelcomeTitle(data.name);
          setWelcomeDescription(data.description || '这是一个智能体助手');
          
          // 保存原始examples数据，以便后续查找attachments
          if (data.examples?.length > 0) {
            setExamples(data.examples);
            
            // Set recommended questions if available
            const compatiblePrompts = [{
              key: '1',
              label: renderTitle(<FireOutlined style={{ color: '#FF4D4F' }} />, '推荐问题'),
              children: data.examples.map((example: WorkflowExample, index: number) => ({
                key: `1-${index+1}`,
                description: example.text
              }))
            }];
            setPromptsItems(compatiblePrompts);
          }
        }
      } catch (error) {
        console.error(`获取${workflowOrAgent === 'workflow' ? '工作流' : '智能体'}数据失败:`, error);
      } finally {
        setLoading(false);
      }
    };
    
    getWorkflowData();
  }, [workflow_id, workflowOrAgent, kb_id, mcp_id]); // 添加依赖：kb_id和mcp_id
    
  return (
    <Space 
      direction="vertical" 
      size={16} 
      className={styles.placeholder}
      style={{ 
        margin: '0 auto', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        minWidth: '600px',
        width: '100%'
      }}
    >
      {/* 加载指示器 */}
      {loading && <Spin size="small" tip={`加载数据中...`} />}
      
      {/* 放在第一位，是显示在页面顶部的组件，显示欢迎信息 */}
      <div className={styles.welcomeContainer}>
        <Welcome
          title={welcomeTitle}
          description={
            <SafeTooltip title={welcomeDescription} placement="bottom">
              <div className={styles.truncatedDescription}>
                {truncateDescription(welcomeDescription)}
              </div>
            </SafeTooltip>
          }
        />
      </div>

      {/* 提示组件，显示推荐的提示 */}
      <Prompts
        items={promptsItems}
        onItemClick={handlePromptsItemClick}
        className={styles.customPrompts}
      />
    </Space>
  );
};

export default PlaceholderNode;
