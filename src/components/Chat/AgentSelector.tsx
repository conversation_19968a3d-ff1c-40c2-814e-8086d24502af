import React, { useState, useEffect } from 'react';
import { Modal, Spin, Tooltip, Empty, message } from 'antd';
import { AGENT_ENDPOINT } from '../../Constant/RouterConstant';
import { CHAT_API_NAME } from '../../Constant/Constant';
import { fetchBulk } from '../api/api';
import styles from './AgentSelector.module.css';
import SafeTooltip from '../common/SafeTooltip';
import { useSelector } from 'react-redux';
import {ShareAltOutlined } from '@ant-design/icons';

interface AgentType {
  type: string;
  description: string;
  icon: string;
}

// 智能体图标组件
const TypeIcon: React.FC<{ iconSvg: string }> = ({ iconSvg }) => {
  return (
    <div dangerouslySetInnerHTML={{ __html: iconSvg }} />
  );
};

interface Agent {
  id: string;
  name: string;
  description: string;
  agent_type: string;
  is_active: boolean;
  is_published: boolean;
  username?: string; // 添加username字段，表示智能体所属用户
}

interface AgentSelectorProps {
  visible: boolean;
  onClose: () => void;
  username: string;
}

const AgentSelector: React.FC<AgentSelectorProps> = ({ visible, onClose, username }) => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [agentTypes, setAgentTypes] = useState<AgentType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 获取当前登录用户的信息
  const { userInfo } = useSelector((state: any) => state.user);
  const currentUsername = userInfo?.username; // 当前登录用户名

  // 获取智能体列表和智能体类型
  useEffect(() => {
    if (visible) {
      fetchAgents();
      fetchAgentTypes();
    }
  }, [visible, username]);

  const fetchAgents = async () => {
    setLoading(true);
    try {
      const endpoint = `${AGENT_ENDPOINT}/bulk?username=${username}&is_published=true`;
      const result = await fetchBulk(endpoint);
      console.log(result);
      setAgents(result.data);
    } catch (error) {
      console.error('获取智能体列表失败:', error);
      message.error('获取智能体列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取智能体类型
  const fetchAgentTypes = async () => {
    try {
      const response = await fetch(`${AGENT_ENDPOINT}/agent_types`);
      if (response.ok) {
        const { data } = await response.json();
        const filteredData = data.filter((item: AgentType) => item.type !== '通用智能体');
        setAgentTypes(filteredData);
      } else {
        console.error('获取智能体类型失败');
        message.error('获取智能体类型失败');
      }
    } catch (error) {
      console.error('获取智能体类型失败:', error);
      message.error('获取智能体类型失败');
    }
  };

  // 按类型分组智能体
  const getAgentsByType = (type: string) => {
    return agents.filter(agent => agent.agent_type === type);
  };

  // 获取有数据的智能体类型
  const getTypesWithData = () => {
    const typesWithData = agentTypes.filter(category => 
      agents.some(agent => agent.agent_type === category.type)
    );
    return typesWithData;
  };

  // 判断智能体是否属于当前登录用户
  const isCurrentUserAgent = (agent: Agent) => {
    return !agent.username || agent.username === currentUsername;
  };

  // 处理智能体点击，跳转到聊天页面
  const handleAgentClick = (agent: Agent) => {
    // 先关闭弹窗
    onClose();
    
    // 构建URL
    const url = `${CHAT_API_NAME}/${agent.id}/new?workflow_or_agent=agent`;
    
    // 使用window.location.href进行导航，这会导致页面完全刷新
    window.location.href = url;
    
    // 不再使用React Router的navigate，因为它不会重新加载组件
    // navigate(url);
  };

  return (
    <Modal
      title="智能体选择"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      className={styles.agentSelectorModal}
    >
      <Spin spinning={loading}>
        <div className={styles.agentCategoriesContainer}>
          {getTypesWithData().length > 0 ? (
            getTypesWithData().map((category, index) => (
              <div key={index} className={styles.categorySection}>
                <div className={styles.categoryHeader}>
                  <div className={styles.categoryIcon}>
                    <TypeIcon iconSvg={category.icon} />
                  </div>
                  <h3 className={styles.categoryTitle}>{category.type}</h3>
                </div>
                
                <Tooltip title={category.description} getPopupContainer={triggerNode => triggerNode.parentElement || document.body}>
                  <div className={styles.categoryDescription}>
                    {category.description}
                  </div>
                </Tooltip>
                
                <div className={styles.agentCardGrid}>
                  {getAgentsByType(category.type).map(agent => {
                    // 确定样式类：区分当前用户和其他用户的智能体
                    const isCurrentUser = isCurrentUserAgent(agent);
                    const itemClassName = `${styles.agentItem} ${
                      !isCurrentUser ? styles.agentItemOtherUser : ''
                    }`;
                    
                    return (
                      <SafeTooltip 
                        key={agent.id} 
                        title={
                          <div>
                            <div>{agent.description}</div>
                            {!isCurrentUser && agent.username && (
                              <div style={{ marginTop: 8, color: '#faad14' }}>
                                所有者: {agent.username}
                              </div>
                            )}
                          </div>
                        } 
                        getPopupContainer={triggerNode => triggerNode.parentElement || document.body}
                      >
                        <div 
                          className={itemClassName}
                          onClick={() => handleAgentClick(agent)}
                        >
                          {agent.name}
                          {!isCurrentUser && (
                            <span style={{ 
                              position: 'absolute', 
                              top: 4, 
                              left: 4, 
                              fontSize: 10, 
                              backgroundColor: '#faad14', 
                              color: 'white',
                              padding: '1px 4px',
                              borderRadius: 4,
                              opacity: 0.8
                            }}>
                              <ShareAltOutlined /> 共享
                            </span>
                          )}
                        </div>
                      </SafeTooltip>
                    );
                  })}
                </div>
              </div>
            ))
          ) : (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE} 
              description="暂无可用的智能体" 
              className={styles.emptyAgent}
            />
          )}
        </div>
      </Spin>
    </Modal>
  );
};

export default AgentSelector; 