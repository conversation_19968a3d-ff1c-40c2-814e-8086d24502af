import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Table, Pagination, Spin, Empty, Select, Tooltip, Button, Modal, Input } from 'antd';
import * as XLSX from 'xlsx';
import { SearchOutlined, FullscreenOutlined, FullscreenExitOutlined, ColumnWidthOutlined, EyeOutlined} from '@ant-design/icons';
import styles from './ExcelViewer.module.css';

const { Option } = Select;
const { TextArea } = Input;

interface ExcelViewerProps {
  fileUrl: string;
  fileName: string;
}

interface SheetData {
  headers: string[];
  rows: any[][];
  name: string;
}

interface DetailViewProps {
  visible: boolean;
  title: string;
  content: any;
  onClose: () => void;
}

// 单元格详情查看组件
const DetailView: React.FC<DetailViewProps> = ({ visible, title, content, onClose }) => {
  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>关闭</Button>,
        <Button 
          key="copy" 
          type="primary" 
          onClick={() => {
            navigator.clipboard.writeText(String(content));
            onClose();
          }}
        >
          复制内容
        </Button>
      ]}
      width={700}
      className={styles.detailModal}
    >
      <TextArea
        value={String(content)}
        autoSize={{ minRows: 3, maxRows: 20 }}
        readOnly
        className={styles.detailContent}
      />
    </Modal>
  );
};

const ExcelViewer: React.FC<ExcelViewerProps> = ({ fileUrl, fileName }) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [sheetNames, setSheetNames] = useState<string[]>([]);
  const [currentSheet, setCurrentSheet] = useState<string>('');
  const [sheets, setSheets] = useState<Record<string, SheetData>>({});
  const [error, setError] = useState<string | null>(null);
  const [pageSize, setPageSize] = useState<number>(20);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [highlightedCells, setHighlightedCells] = useState<Record<string, boolean>>({});
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [detailView, setDetailView] = useState<{
    visible: boolean;
    title: string;
    content: any;
  }>({
    visible: false,
    title: '',
    content: ''
  });

  const [draggingColumn, setDraggingColumn] = useState<string | null>(null);
  const [startX, setStartX] = useState<number>(0);
  const [startWidth, setStartWidth] = useState<number>(0);
  
  const excelViewerRef = useRef<HTMLDivElement>(null);
  
  // 加载Excel文件
  useEffect(() => {
    const loadExcelFile = async () => {
      setLoading(true);
      try {
        // 从URL获取Excel文件
        const response = await fetch(fileUrl);
        const arrayBuffer = await response.arrayBuffer();
        
        // 使用xlsx库解析文件
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        
        // 获取所有sheet名称
        const names = workbook.SheetNames;
        setSheetNames(names);
        
        if (names.length > 0) {
          setCurrentSheet(names[0]);
        }
        
        // 处理每个sheet
        const sheetsData: Record<string, SheetData> = {};
        
        for (const name of names) {
          const sheet = workbook.Sheets[name];
          const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
          
          if (jsonData.length > 0) {
            // 提取表头（第一行）
            const headers = jsonData[0] as string[];
            // 提取数据行
            const rows = jsonData.slice(1) as any[][];
            
            sheetsData[name] = {
              headers,
              rows,
              name
            };
          } else {
            sheetsData[name] = {
              headers: [],
              rows: [],
              name
            };
          }
        }
        
        setSheets(sheetsData);
        setError(null);
        
        // 初始化列宽
        if (names.length > 0 && sheetsData[names[0]]) {
          const initialWidths: Record<string, number> = {};
          sheetsData[names[0]].headers.forEach((_, index) => {
            initialWidths[`col${index}`] = 150; // 默认宽度
          });
          setColumnWidths(initialWidths);
        }
      } catch (err) {
        console.error('Excel文件加载失败:', err);
        setError('无法加载Excel文件，格式可能不支持');
      } finally {
        setLoading(false);
      }
    };
    
    if (fileUrl) {
      loadExcelFile();
    }
  }, [fileUrl]);
  
  // 处理sheet切换
  const handleSheetChange = (value: string) => {
    setCurrentSheet(value);
    setCurrentPage(1); // 切换表格时重置为第一页
    setSearchText(''); // 清空搜索
    setHighlightedCells({});
    
    // 初始化新sheet的列宽
    if (sheets[value]) {
      const newWidths: Record<string, number> = {};
      sheets[value].headers.forEach((_, index) => {
        newWidths[`col${index}`] = 150; // 默认宽度
      });
      setColumnWidths(newWidths);
    }
  };
  
  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  // 处理每页显示数量变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // 改变每页条数时重置为第一页
  };
  
  // 切换全屏模式
  const toggleFullscreen = () => {
    if (excelViewerRef.current) {
      if (!isFullscreen) {
        if (excelViewerRef.current.requestFullscreen) {
          excelViewerRef.current.requestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      }
    }
  };
  
  // 监听全屏变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);
  
  // 处理搜索功能
  const handleSearch = () => {
    if (!searchText.trim() || !currentSheet || !sheets[currentSheet]) {
      setHighlightedCells({});
      return;
    }
    
    const { rows } = sheets[currentSheet];
    const highlighted: Record<string, boolean> = {};
    const searchRegex = new RegExp(searchText, 'i');
    
    rows.forEach((row, rowIndex) => {
      row.forEach((cell: any, colIndex: number) => {
        const cellContent = String(cell);
        if (searchRegex.test(cellContent)) {
          highlighted[`${rowIndex + 1}-${colIndex}`] = true;
        }
      });
    });
    
    setHighlightedCells(highlighted);
  };
  
  // 调整列宽
  const handleColumnWidthChange = (colKey: string, width: number) => {
    setColumnWidths(prev => ({
      ...prev,
      [colKey]: Math.max(50, width) // 最小宽度50px
    }));
  };
  
  // 处理单元格点击，查看详细内容
  const handleCellClick = (text: any, colTitle: string) => {
    setDetailView({
      visible: true,
      title: `${colTitle} - 详细内容`,
      content: text
    });
  };
  
  // 关闭详细视图
  const closeDetailView = () => {
    setDetailView({
      ...detailView,
      visible: false
    });
  };
  
  
  // 处理列拖拽开始
  const handleResizeStart = (e: React.MouseEvent, colKey: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    setDraggingColumn(colKey);
    setStartX(e.clientX);
    setStartWidth(columnWidths[colKey] || 150);
    
    // 添加全局事件监听
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);
  };
  
  // 处理列拖拽移动
  const handleResizeMove = useCallback((e: MouseEvent) => {
    if (!draggingColumn) return;
    
    const delta = e.clientX - startX;
    const newWidth = Math.max(50, startWidth + delta); // 最小宽度50px
    
    setColumnWidths(prev => ({
      ...prev,
      [draggingColumn]: newWidth
    }));
  }, [draggingColumn, startX, startWidth]);
  
  // 处理列拖拽结束
  const handleResizeEnd = useCallback(() => {
    setDraggingColumn(null);
    
    // 移除全局事件监听
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);
  }, [handleResizeMove]);
  
  // 在组件卸载时清理事件监听
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [handleResizeMove, handleResizeEnd]);
  
  // 准备表格数据
  const getTableData = () => {
    if (!currentSheet || !sheets[currentSheet]) {
      return {
        columns: [],
        dataSource: []
      };
    }
    
    const { headers, rows } = sheets[currentSheet];
    
    // 根据当前分页计算要显示的数据
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedRows = rows.slice(startIndex, endIndex);
    
    // 设置列配置
    const columns = headers.map((header, index) => ({
      title: (
        <div className={styles.columnHeader}>
          <span>{header || `列${index + 1}`}</span>
          <Button 
            className={styles.columnWidthButton} 
            icon={<ColumnWidthOutlined />}
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              const newWidth = prompt(`请输入"${header || `列${index + 1}`}"的宽度 (像素)`, `${columnWidths[`col${index}`] || 150}`);
              if (newWidth && !isNaN(Number(newWidth))) {
                handleColumnWidthChange(`col${index}`, Number(newWidth));
              }
            }}
          />
          {/* 添加可拖拽的列宽调整把手 */}
          <div 
            className={styles.columnResizeHandle}
            onMouseDown={(e) => handleResizeStart(e, `col${index}`)}
          />
        </div>
      ),
      dataIndex: `col${index}`,
      key: `col${index}`,
      width: columnWidths[`col${index}`] || 150,
      ellipsis: true,
      render: (text: any, _: any, rowIndex: number) => {
        const isHighlighted = highlightedCells[`${rowIndex + startIndex + 1}-${index}`];
        return (
          <div 
            className={`${styles.cellContent} ${isHighlighted ? styles.highlightedCell : ''}`}
            onClick={() => handleCellClick(text, header || `列${index + 1}`)}
          >
            <Tooltip title="点击查看详细内容">
              <div className={styles.cellInner}>
                <span className={styles.cellText}>{text}</span>
                {String(text).length > 20 && (
                  <EyeOutlined className={styles.viewDetailsIcon} />
                )}
              </div>
            </Tooltip>
          </div>
        );
      }
    }));
    
    // 设置数据源
    const dataSource = paginatedRows.map((row, rowIndex) => {
      const item: Record<string, any> = { key: `row-${rowIndex + startIndex}` };
      
      // 填充数据
      headers.forEach((_, colIndex) => {
        item[`col${colIndex}`] = row[colIndex] !== undefined ? row[colIndex] : '';
      });
      
      return item;
    });
    
    return { columns, dataSource };
  };
  
  // 获取总行数
  const getTotalRows = () => {
    if (!currentSheet || !sheets[currentSheet]) {
      return 0;
    }
    return sheets[currentSheet].rows.length;
  };
  
  // 渲染表格
  const renderTable = () => {
    if (loading) {
      return (
        <div className={styles.loadingContainer}>
          <Spin size="large" tip="加载Excel数据中..." />
        </div>
      );
    }
    
    if (error) {
      return (
        <div className={styles.errorContainer}>
          <Empty 
            description={error} 
            image={Empty.PRESENTED_IMAGE_SIMPLE} 
          />
        </div>
      );
    }
    
    if (!currentSheet || !sheets[currentSheet]) {
      return (
        <div className={styles.emptyContainer}>
          <Empty description="没有可用的数据" />
        </div>
      );
    }
    
    const { columns, dataSource } = getTableData();
    const totalRows = getTotalRows();
    
    return (
      <>
        <div className={styles.searchAndControls}>
          <div className={styles.searchBar}>
            <Input 
              placeholder="搜索数据..." 
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              suffix={<SearchOutlined onClick={handleSearch} />}
              allowClear
            />
          </div>
          <div className={styles.tableControls}>
            <Button 
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              onClick={toggleFullscreen}
              title={isFullscreen ? "退出全屏" : "全屏查看"}
            />
          </div>
        </div>
        
        <div className={styles.tableContainer}>
          <Table 
            columns={columns} 
            dataSource={dataSource} 
            pagination={false}
            size="small"
            scroll={{ x: 'max-content', y: isFullscreen ? window.innerHeight - 200 : 500 }}
            bordered
            className={styles.excelTable}
          />
        </div>
        
        {totalRows > 0 && (
          <div className={styles.paginationContainer}>
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={totalRows}
              onChange={handlePageChange}
              onShowSizeChange={handlePageSizeChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total) => `共 ${total} 条数据`}
            />
          </div>
        )}
      </>
    );
  };
  
  return (
    <div 
      className={`${styles.excelViewer} ${isFullscreen ? styles.fullscreen : ''} ${draggingColumn ? styles.resizing : ''}`}
      ref={excelViewerRef}
    >
      <div className={styles.header}>
        <div className={styles.title}>
          <span className={styles.fileName}>{fileName}</span>
        </div>
        
        {sheetNames.length > 0 && (
          <div className={styles.sheetSelector}>
            <span className={styles.sheetLabel}>工作表:</span>
            <Select 
              value={currentSheet} 
              onChange={handleSheetChange}
              className={styles.sheetSelect}
            >
              {sheetNames.map(name => (
                <Option key={name} value={name}>{name}</Option>
              ))}
            </Select>
          </div>
        )}
      </div>
      
      <div className={styles.content}>
        {renderTable()}
      </div>
      
      {/* 详细内容查看模态框 */}
      <DetailView
        visible={detailView.visible}
        title={detailView.title}
        content={detailView.content}
        onClose={closeDetailView}
      />
    </div>
  );
};

export default ExcelViewer;