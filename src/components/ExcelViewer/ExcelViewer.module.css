.excelViewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-left: 1px solid #f0f0f0;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  flex-shrink: 0;
}

.title {
  font-weight: 500;
  font-size: 16px;
  color: #333;
  display: flex;
  align-items: center;
}

.fileName {
  margin-left: 8px;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sheetSelector {
  display: flex;
  align-items: center;
}

.sheetLabel {
  margin-right: 8px;
  color: #666;
}

.sheetSelect {
  min-width: 120px;
}

.content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tableContainer {
  flex: 1;
  overflow: auto;
  padding: 0 16px;
}

.paginationContainer {
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.loadingContainer,
.emptyContainer,
.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px;
}

/* 自定义表格样式 */
.tableContainer :global(.ant-table-thead > tr > th) {
  background-color: #f0f7ff;
  font-weight: 500;
  color: #333;
}

.tableContainer :global(.ant-table) {
  font-size: 13px;
}

.tableContainer :global(.ant-table-cell) {
  padding: 8px 12px !important;
}

/* 表格悬停效果 */
.tableContainer :global(.ant-table-tbody > tr:hover > td) {
  background-color: #f0f7ff;
}

/* 确保工作表选择器下拉菜单在表格上方 */
.sheetSelect :global(.ant-select-dropdown) {
  z-index: 1050;
}

/* 表格行的斑马纹样式 */
.tableContainer :global(.ant-table-tbody > tr:nth-child(odd) > td) {
  background-color: #fafafa;
}

/* 固定表头 */
.tableContainer :global(.ant-table-header) {
  position: sticky;
  top: 0;
  z-index: 2;
}

/* 高亮单元格样式 */
.highlightedCell {
  background-color: #fffbe6 !important;
  border: 1px solid #ffe58f !important;
}

/* 单元格内容样式 */
.cellContent {
  cursor: pointer;
  height: 100%;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
}

.cellInner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cellText {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.viewDetailsIcon {
  color: #1890ff;
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.cellContent:hover .viewDetailsIcon {
  opacity: 1;
}

/* 搜索和控制区域 */
.searchAndControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  margin: 16px 0;
}

.searchBar {
  width: 300px;
}

.tableControls {
  display: flex;
  gap: 8px;
}

/* 列标题 */
.columnHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.columnWidthButton {
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.columnHeader:hover .columnWidthButton {
  opacity: 1;
}

/* 列宽拖拽把手 */
.columnResizeHandle {
  position: absolute;
  top: 0;
  right: -5px;
  width: 10px;
  height: 100%;
  cursor: col-resize;
  z-index: 10;
}

/* 拖拽状态样式 */
.resizing {
  cursor: col-resize !important;
  user-select: none;
}

.resizing .tableContainer :global(.ant-table-thead th) {
  transition: none !important;
}

.resizing .tableContainer :global(.ant-table-container) {
  cursor: col-resize !important;
}

/* 详情模态框 */
.detailModal {
  top: 50px;
}

.detailContent {
  font-family: monospace;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 8px;
}

/* 全屏模式 */
.fullscreen {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: white;
  width: 100vw;
  height: 100vh;
}

.fullscreen .header {
  padding: 12px 16px;
}

.fullscreen .tableContainer {
  height: calc(100vh - 160px);
}

.excelTable {
  margin-bottom: 0;
}

/* 列宽缩放样式 */
.zoomInfo {
  margin-left: 8px;
  padding: 0 8px;
  background-color: #f0f7ff;
  border-radius: 4px;
  font-size: 12px;
  color: #1890ff;
  line-height: 22px;
  font-weight: 500;
  white-space: nowrap;
}

/* 适配移动设备 */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .searchAndControls {
    flex-direction: column;
    gap: 8px;
  }
  
  .searchBar {
    width: 100%;
  }
} 