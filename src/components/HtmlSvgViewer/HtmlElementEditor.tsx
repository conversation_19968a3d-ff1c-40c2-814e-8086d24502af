import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Select, Button, Collapse, ColorPicker, Slider } from 'antd';
import { DeleteOutlined, BoldOutlined, ItalicOutlined, UnderlineOutlined, AlignLeftOutlined, AlignCenterOutlined, AlignRightOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Option } = Select;
const { TextArea } = Input;


interface HtmlElementEditorProps {
  elementData: {
    tagName: string;
    id: string;
    classes: string;
    styles: string;
    innerText: string;
    innerHTML: string;
    outerHTML: string;
  } | null;
  onStyleChange: (property: string, value: string) => void;
  onTextChange: (newText: string) => void;
  onAttributeChange: (attribute: string, value: string) => void;
  onDeleteElement: () => void;
}

// CSS属性选择列表
const fontFamilyOptions = [
  { label: 'Arial', value: 'Arial, sans-serif' },
  { label: 'Times New Roman', value: 'Times New Roman, serif' },
  { label: 'Helvetica', value: 'Helvetica, sans-serif' },
  { label: 'Courier New', value: 'Courier New, monospace' },
  { label: '微软雅黑', value: 'Microsoft YaHei, sans-serif' },
  { label: '宋体', value: 'SimSun, serif' },
  { label: '黑体', value: 'SimHei, sans-serif' },
];

const fontWeightOptions = [
  { label: '正常', value: 'normal' },
  { label: '粗体', value: 'bold' },
  { label: '细体', value: '300' },
  { label: '中等', value: '500' },
  { label: '特粗', value: '800' },
];

const textAlignOptions = [
  { label: <AlignLeftOutlined />, value: 'left' },
  { label: <AlignCenterOutlined />, value: 'center' },
  { label: <AlignRightOutlined />, value: 'right' },
];

const displayOptions = [
  { label: '块级', value: 'block' },
  { label: '行内', value: 'inline' },
  { label: '行内块', value: 'inline-block' },
  { label: '弹性布局', value: 'flex' },
  { label: '网格布局', value: 'grid' },
  { label: '隐藏', value: 'none' },
];

/**
 * 从CSS字符串中提取样式对象
 */
const parseStyles = (stylesStr: string): Record<string, string> => {
  const styleObj: Record<string, string> = {};
  if (!stylesStr) return styleObj;
  
  const stylePairs = stylesStr.split(';').filter(Boolean);
  stylePairs.forEach(pair => {
    const [property, value] = pair.split(':').map(s => s.trim());
    if (property && value) {
      styleObj[property] = value;
    }
  });
  
  return styleObj;
};

/**
 * 解析颜色值为RGB或RGBA格式
 */
// const parseColor = (color: string): string => {
//   if (!color) return '';
//   return color;
// };

const HtmlElementEditor: React.FC<HtmlElementEditorProps> = ({
  elementData,
  onStyleChange,
  onTextChange,
  onAttributeChange,
  onDeleteElement
}) => {
  const [styles, setStyles] = useState<Record<string, string>>({});
  const [editingText, setEditingText] = useState<string>('');
  
  // 当元素数据更新时解析样式
  useEffect(() => {
    if (elementData) {
      const parsedStyles = parseStyles(elementData.styles);
      setStyles(parsedStyles);
      setEditingText(elementData.innerText);
    } else {
      setStyles({});
      setEditingText('');
    }
  }, [elementData]);
  
  // 应用样式变更
  const applyStyleChange = (property: string, value: string) => {
    const newStyles = { ...styles, [property]: value };
    setStyles(newStyles);
    onStyleChange(property, value);
  };
  
  // 应用文本变更
  const applyTextChange = () => {
    onTextChange(editingText);
  };
  
  if (!elementData) {
    return (
      <div className={styles.emptyEditor}>
        <p>请在左侧选择一个HTML元素进行编辑</p>
      </div>
    );
  }
  
  return (
    <div className={styles.htmlElementEditor}>
      <div className={styles.elementHeader}>
        <h3>编辑 {elementData.tagName.toLowerCase()} 元素</h3>
        <Button 
          type="primary" 
          danger
          icon={<DeleteOutlined />} 
          onClick={onDeleteElement}
          size="small"
        >
          删除元素
        </Button>
      </div>
      
      <Collapse defaultActiveKey={['1', '2']}>
        {/* 文本编辑面板 */}
        <Panel header="文本内容" key="1">
          {elementData.innerText !== undefined && (
            <div className={styles.propertyItem}>
              <TextArea
                rows={4}
                value={editingText}
                onChange={(e) => setEditingText(e.target.value)}
                onBlur={applyTextChange}
              />
              <div style={{ marginTop: 8 }}>
                <Button size="small" onClick={() => {
                  setEditingText(editingText => editingText.bold());
                  applyTextChange();
                }}><BoldOutlined /></Button>
                <Button size="small" onClick={() => {
                  setEditingText(editingText => `<i>${editingText}</i>`);
                  applyTextChange();
                }}><ItalicOutlined /></Button>
                <Button size="small" onClick={() => {
                  setEditingText(editingText => `<u>${editingText}</u>`);
                  applyTextChange();
                }}><UnderlineOutlined /></Button>
              </div>
            </div>
          )}
        </Panel>
        
        {/* 样式编辑面板 */}
        <Panel header="样式属性" key="2">
          <Form layout="vertical" size="small">
            {/* 字体样式 */}
            <div className={styles.propertyItem}>
              <Form.Item label="字体">
                <Select
                  value={styles['font-family'] || undefined}
                  onChange={(value) => applyStyleChange('font-family', value)}
                  style={{ width: '100%' }}
                >
                  {fontFamilyOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      <span style={{ fontFamily: option.value }}>{option.label}</span>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
            
            {/* 字体大小 */}
            <div className={styles.propertyItem}>
              <Form.Item label="字体大小">
                <div className={styles.fontSizeControl}>
                  <InputNumber
                    value={parseInt(styles['font-size']) || 16}
                    min={8}
                    max={72}
                    onChange={(value) => applyStyleChange('font-size', `${value}px`)}
                    style={{ width: '60px' }}
                  />
                  <Slider
                    value={parseInt(styles['font-size']) || 16}
                    min={8}
                    max={72}
                    onChange={(value) => applyStyleChange('font-size', `${value}px`)}
                    style={{ flex: 1, marginLeft: 16 }}
                  />
                </div>
              </Form.Item>
            </div>
            
            {/* 字体颜色 */}
            <div className={styles.propertyItem}>
              <Form.Item label="字体颜色">
                <ColorPicker
                  format="hex"
                  showText
                  onChange={(_, hex) => applyStyleChange('color', hex)}
                />
              </Form.Item>
            </div>
            
            {/* 背景颜色 */}
            <div className={styles.propertyItem}>
              <Form.Item label="背景颜色">
                <ColorPicker
                  format="hex"
                  showText
                  onChange={(_, hex) => applyStyleChange('background-color', hex)}
                />
              </Form.Item>
            </div>
            
            {/* 字体粗细 */}
            <div className={styles.propertyItem}>
              <Form.Item label="字体粗细">
                <Select
                  value={styles['font-weight'] || undefined}
                  onChange={(value) => applyStyleChange('font-weight', value)}
                  style={{ width: '100%' }}
                >
                  {fontWeightOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
            
            {/* 文本对齐 */}
            <div className={styles.propertyItem}>
              <Form.Item label="文本对齐">
                <Select
                  value={styles['text-align'] || undefined}
                  onChange={(value) => applyStyleChange('text-align', value)}
                  style={{ width: '100%' }}
                >
                  {textAlignOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
            
            {/* 边距设置 */}
            <div className={styles.propertyItem}>
              <Form.Item label="边距">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="上"
                    value={(styles['margin-top'] || '').replace('px', '')}
                    onChange={(e) => applyStyleChange('margin-top', `${e.target.value}px`)}
                    style={{ width: '25%' }}
                  />
                  <Input
                    addonBefore="右"
                    value={(styles['margin-right'] || '').replace('px', '')}
                    onChange={(e) => applyStyleChange('margin-right', `${e.target.value}px`)}
                    style={{ width: '25%' }}
                  />
                  <Input
                    addonBefore="下"
                    value={(styles['margin-bottom'] || '').replace('px', '')}
                    onChange={(e) => applyStyleChange('margin-bottom', `${e.target.value}px`)}
                    style={{ width: '25%' }}
                  />
                  <Input
                    addonBefore="左"
                    value={(styles['margin-left'] || '').replace('px', '')}
                    onChange={(e) => applyStyleChange('margin-left', `${e.target.value}px`)}
                    style={{ width: '25%' }}
                  />
                </div>
              </Form.Item>
            </div>
            
            {/* 内边距设置 */}
            <div className={styles.propertyItem}>
              <Form.Item label="内边距">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="上"
                    value={(styles['padding-top'] || '').replace('px', '')}
                    onChange={(e) => applyStyleChange('padding-top', `${e.target.value}px`)}
                    style={{ width: '25%' }}
                  />
                  <Input
                    addonBefore="右"
                    value={(styles['padding-right'] || '').replace('px', '')}
                    onChange={(e) => applyStyleChange('padding-right', `${e.target.value}px`)}
                    style={{ width: '25%' }}
                  />
                  <Input
                    addonBefore="下"
                    value={(styles['padding-bottom'] || '').replace('px', '')}
                    onChange={(e) => applyStyleChange('padding-bottom', `${e.target.value}px`)}
                    style={{ width: '25%' }}
                  />
                  <Input
                    addonBefore="左"
                    value={(styles['padding-left'] || '').replace('px', '')}
                    onChange={(e) => applyStyleChange('padding-left', `${e.target.value}px`)}
                    style={{ width: '25%' }}
                  />
                </div>
              </Form.Item>
            </div>
            
            {/* 显示方式 */}
            <div className={styles.propertyItem}>
              <Form.Item label="显示方式">
                <Select
                  value={styles['display'] || undefined}
                  onChange={(value) => applyStyleChange('display', value)}
                  style={{ width: '100%' }}
                >
                  {displayOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </Form>
        </Panel>
        
        {/* 属性编辑面板 */}
        <Panel header="元素属性" key="3">
          <Form layout="vertical" size="small">
            <div className={styles.propertyItem}>
              <Form.Item label="ID">
                <Input
                  value={elementData.id}
                  onChange={(e) => onAttributeChange('id', e.target.value)}
                />
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="类名">
                <Input
                  value={elementData.classes}
                  onChange={(e) => onAttributeChange('class', e.target.value)}
                />
              </Form.Item>
            </div>
            
            {/* 自定义样式 */}
            <div className={styles.propertyItem}>
              <Form.Item label="自定义样式">
                <TextArea
                  rows={4}
                  value={elementData.styles}
                  onChange={(e) => onAttributeChange('style', e.target.value)}
                  placeholder="例如: color: red; font-size: 16px;"
                />
              </Form.Item>
            </div>
          </Form>
        </Panel>
      </Collapse>
    </div>
  );
};

export default HtmlElementEditor; 