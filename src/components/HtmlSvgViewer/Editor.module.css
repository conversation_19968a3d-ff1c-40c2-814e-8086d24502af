.editorContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.toolbar {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editorContent {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.previewPanel {
  flex: 1;
  border-right: 1px solid #f0f0f0;
  overflow: hidden;
  position: relative;
  background-color: #fff;
}

.propertiesPanel {
  width: 300px;
  overflow-y: auto;
  padding: 0 8px;
}

.editorFrame {
  width: 100%;
  height: 100%;
  border: none;
  background-color: #fff;
}

.noContentMessage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #999;
  padding: 20px;
  background-color: rgba(250, 250, 250, 0.8);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.propertySection {
  padding: 8px 0;
}

.previewContainer {
  height: 70vh;
  overflow: hidden;
}

.previewFrame {
  width: 100%;
  height: 100%;
  border: none;
}
