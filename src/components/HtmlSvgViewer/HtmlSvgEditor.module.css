.editorContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8f9fa;
}

.toolbar {
  padding: 10px;
  border-bottom: 1px solid #eaeaea;
  background-color: white;
  display: flex;
  justify-content: space-between;
}

.editorContent {
  display: flex;
  flex: 1;
  height: calc(100% - 56px); /* 减去工具栏的高度 */
  overflow: hidden;
}

.previewPanel {
  flex: 1;
  min-width: 0;
  background-color: white;
  padding: 10px;
  border-right: 1px solid #eaeaea;
  overflow: auto;
  height: 100%;
}

.propertiesPanel {
  width: 320px;
  background-color: white;
  height: 100%;
  overflow: auto;
  flex-shrink: 0;
  border-left: 1px solid #eaeaea;
}

.propertySection {
  padding: 12px;
}

.editorFrame, .previewFrame {
  width: 100%;
  height: 100%;
  border: none;
  overflow: auto;
}

.previewContainer {
  height: 70vh;
  overflow: auto;
  border: 1px solid #eaeaea;
  padding: 0;
}

.propertyItem {
  margin-bottom: 12px;
}

.propertyLabel {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #333;
}

.colorPicker {
  margin-top: 5px;
  width: 100%;
}

.fontSizeControl {
  display: flex;
  align-items: center;
}

.svgToolPanel {
  padding: 10px;
  border-top: 1px solid #eaeaea;
  background-color: #f8f8f8;
}

/* SVG编辑相关样式 */
.svgShapeButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.shapeButton {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
}

.shapeButton:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.shapeButtonActive {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.elementListItem {
  padding: 8px 10px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.elementListItem:hover {
  background-color: #f5f5f5;
}

.elementListItemActive {
  background-color: #e6f7ff;
}

.elementListTag {
  font-weight: bold;
  margin-right: 6px;
}

.elementListId {
  color: #1890ff;
  margin-right: 6px;
}

.elementListClass {
  color: #52c41a;
  margin-right: 6px;
} 