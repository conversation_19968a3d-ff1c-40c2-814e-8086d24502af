import React, { useState, useEffect, useRef } from 'react';
import { Button, Tabs, Space, Modal, message, Input, Form, Slider, ColorPicker, Divider } from 'antd';
import { SaveOutlined, EyeOutlined, UndoOutlined, RedoOutlined, CodeOutlined, EditOutlined } from '@ant-design/icons';
import styles from './Editor.module.css';

const { TextArea } = Input;
const { TabPane } = Tabs;

interface SvgEditorProps {
  content: string;
  onSave: (newContent: string) => void;
  onCancel: () => void;
  visible: boolean;
}

/**
 * SVG编辑器组件
 * 提供SVG内容的编辑功能，可以修改内容、样式和布局
 */
const SvgEditor: React.FC<SvgEditorProps> = ({
  content,
  onSave,
  onCancel,
  visible
}) => {
  // 编辑模式：可视化编辑或源代码编辑
  const [editMode, setEditMode] = useState<'visual' | 'source'>('visual');

  // SVG内容
  const [svgContent, setSvgContent] = useState<string>(content || '');

  // 预览模式
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);

  // 编辑历史
  const [history, setHistory] = useState<string[]>([content || '']);
  const [historyIndex, setHistoryIndex] = useState<number>(0);

  // iframe引用
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 当前选中的元素
  const [selectedElement, setSelectedElement] = useState<{
    element: SVGElement | null;
    path: string;
    attributes: Record<string, string>;
    tagName: string;
  } | null>(null);

  // 样式编辑状态
  const [fillColor, setFillColor] = useState<string>('#000000');
  const [strokeColor, setStrokeColor] = useState<string>('#000000');
  const [strokeWidth, setStrokeWidth] = useState<number>(1);
  const [opacity, setOpacity] = useState<number>(100);
  const [elementAttributes, setElementAttributes] = useState<Record<string, string>>({});
  const [attributeKey, setAttributeKey] = useState<string>('');
  const [attributeValue, setAttributeValue] = useState<string>('');

  // 初始化编辑内容
  useEffect(() => {
    if (visible) {
      // 确保编辑模式为可视化编辑
      setEditMode('visual');

      // 如果有内容，使用提供的内容
      if (content && content.trim() !== '') {
        setSvgContent(content);
        setHistory([content]);
        setHistoryIndex(0);
      } else {
        // 如果没有内容，提供默认的SVG模板
        const defaultContent = `
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景矩形 -->
  <rect width="400" height="300" fill="#f0f0f0" />

  <!-- 圆形 -->
  <circle cx="200" cy="150" r="80" fill="#0066cc" />

  <!-- 文本 -->
  <text x="200" y="160" font-family="Arial" font-size="24" fill="white" text-anchor="middle">SVG 示例</text>

  <!-- 说明文本 -->
  <text x="200" y="250" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">点击任意元素进行编辑</text>
</svg>
        `;
        setSvgContent(defaultContent);
        setHistory([defaultContent]);
        setHistoryIndex(0);
      }

      // 确保iframe在下一个渲染周期后更新
      setTimeout(() => {
        console.log('Initializing visual editor content');
        if (iframeRef.current) {
          updateVisualEditor();
        }
      }, 500);
    }
  }, [content, visible]); // updateVisualEditor 是组件内部函数，不需要添加到依赖数组

  // 当SVG内容变化时更新预览
  useEffect(() => {
    if (editMode === 'visual') {
      updateVisualEditor();
    }
  }, [svgContent, editMode]); // updateVisualEditor 是组件内部函数，不需要添加到依赖数组

  // 组件挂载时立即更新预览
  useEffect(() => {
    if (visible && editMode === 'visual') {
      // 使用setTimeout确保DOM已经渲染，iframe已经挂载
      setTimeout(() => {
        updateVisualEditor();
        console.log('Visual editor updated on modal open');
      }, 300);
    }
  }, [visible, editMode]); // updateVisualEditor 是组件内部函数，不需要添加到依赖数组

  // 更新可视化编辑器
  const updateVisualEditor = () => {
    if (!iframeRef.current) return;

    const iframe = iframeRef.current;

    try {
      // 创建完整的HTML文档
      const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      margin: 0;
      padding: 10px;
      font-family: Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
      background-color: #f8f9fa;
    }

    svg {
      max-width: 100%;
      height: auto;
      border: 1px solid #ddd;
    }

    .element-highlight {
      stroke: #1890ff !important;
      stroke-width: 2px !important;
      stroke-dasharray: 5,5 !important;
    }

    .element-path {
      position: fixed;
      bottom: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 1000;
    }

    svg * {
      cursor: pointer;
    }

    svg *:hover {
      stroke: rgba(24, 144, 255, 0.5);
      stroke-width: 1px;
    }
  </style>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 元素路径显示
      let pathDisplay = document.createElement('div');
      pathDisplay.className = 'element-path';
      pathDisplay.style.display = 'none';
      document.body.appendChild(pathDisplay);

      // 获取元素路径
      function getElementPath(element) {
        let path = [];
        let currentElement = element;

        while (currentElement && currentElement.tagName !== 'svg') {
          let tag = currentElement.tagName.toLowerCase();
          let id = currentElement.id ? '#' + currentElement.id : '';

          path.unshift(tag + id);
          currentElement = currentElement.parentElement;
        }

        path.unshift('svg');
        return path.join(' > ');
      }

      // 为所有SVG元素添加点击事件
      function makeElementsEditable() {
        const svg = document.querySelector('svg');
        if (!svg) return;

        const svgElements = svg.querySelectorAll('*');

        svgElements.forEach(el => {
          // 鼠标悬停显示路径
          el.addEventListener('mouseover', (e) => {
            e.stopPropagation();
            pathDisplay.textContent = getElementPath(el);
            pathDisplay.style.display = 'block';
          });

          el.addEventListener('mouseout', () => {
            pathDisplay.style.display = 'none';
          });

          // 点击选择元素
          el.addEventListener('click', (e) => {
            e.stopPropagation();
            e.preventDefault();

            // 移除其他元素的高亮
            document.querySelectorAll('.element-highlight').forEach(highlighted => {
              highlighted.classList.remove('element-highlight');
            });

            // 添加高亮
            el.classList.add('element-highlight');

            // 获取元素属性
            const attributes = {};
            Array.from(el.attributes).forEach(attr => {
              attributes[attr.name] = attr.value;
            });

            // 通知父窗口
            window.parent.postMessage({
              type: 'svgElementSelected',
              path: getElementPath(el),
              tagName: el.tagName,
              attributes: attributes,
              outerHTML: el.outerHTML
            }, '*');
          });
        });

        // 点击SVG时选择整个SVG
        svg.addEventListener('click', (e) => {
          if (e.target === svg) {
            document.querySelectorAll('.element-highlight').forEach(highlighted => {
              highlighted.classList.remove('element-highlight');
            });

            // 获取SVG属性
            const attributes = {};
            Array.from(svg.attributes).forEach(attr => {
              attributes[attr.name] = attr.value;
            });

            // 通知父窗口
            window.parent.postMessage({
              type: 'svgElementSelected',
              path: 'svg',
              tagName: 'svg',
              attributes: attributes,
              outerHTML: svg.outerHTML
            }, '*');
          }
        });
      }

      // 初始化编辑功能
      makeElementsEditable();

      // 添加提示信息
      const infoBox = document.createElement('div');
      infoBox.style.cssText = 'position: fixed; top: 10px; right: 10px; background: rgba(24, 144, 255, 0.1); border: 1px solid #1890ff; padding: 10px; border-radius: 4px; font-size: 12px; color: #1890ff; z-index: 1000;';
      infoBox.innerHTML = '点击任意SVG元素进行编辑';
      document.body.appendChild(infoBox);

      // 5秒后隐藏提示
      setTimeout(() => {
        infoBox.style.opacity = '0';
        infoBox.style.transition = 'opacity 0.5s';
        setTimeout(() => infoBox.remove(), 500);
      }, 5000);
    });
  </script>
</head>
<body>
  ${svgContent}
</body>
</html>
      `;

      // 使用srcdoc属性设置iframe内容
      iframe.srcdoc = fullHtml;

    } catch (error) {
      console.error('更新可视化编辑器失败:', error);
      message.error('更新可视化编辑器失败');
    }
  };

  // 监听iframe消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'svgElementSelected') {
        console.log('选中SVG元素:', event.data);

        // 更新选中元素状态
        setSelectedElement({
          element: null, // 无法直接传递DOM元素
          path: event.data.path,
          attributes: event.data.attributes || {},
          tagName: event.data.tagName
        });

        // 更新属性编辑状态
        const attrs = event.data.attributes || {};
        setElementAttributes(attrs);

        // 更新常用属性
        setFillColor(attrs.fill || '#000000');
        setStrokeColor(attrs.stroke || '#000000');
        setStrokeWidth(parseInt(attrs['stroke-width']) || 1);
        setOpacity(parseFloat(attrs.opacity || '1') * 100);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // 更新元素属性
  const updateElementAttribute = (name: string, value: string) => {
    if (!selectedElement || !iframeRef.current) return;

    try {
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) return;

      // 查找选中的元素
      const selectedDomElement = iframeDoc.querySelector('.element-highlight');
      if (selectedDomElement) {
        // 更新属性
        selectedDomElement.setAttribute(name, value);

        // 更新本地属性状态
        setElementAttributes({
          ...elementAttributes,
          [name]: value
        });

        // 更新SVG内容
        const svgElement = iframeDoc.querySelector('svg');
        if (svgElement) {
          setSvgContent(svgElement.outerHTML);

          // 添加到历史记录
          addToHistory(svgElement.outerHTML);
        }

        message.success(`属性 ${name} 已更新`);
      }
    } catch (error) {
      console.error('更新元素属性失败:', error);
      message.error('更新元素属性失败');
    }
  };

  // 添加新属性
  const addNewAttribute = () => {
    if (!attributeKey.trim() || !selectedElement) return;

    updateElementAttribute(attributeKey.trim(), attributeValue);

    // 清空输入
    setAttributeKey('');
    setAttributeValue('');
  };

  // 添加到历史记录
  const addToHistory = (newContent: string) => {
    // 如果当前不是最新的历史记录，则删除之后的记录
    const newHistory = [...history.slice(0, historyIndex + 1), newContent];
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // 撤销操作
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setSvgContent(history[historyIndex - 1]);
    }
  };

  // 重做操作
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setSvgContent(history[historyIndex + 1]);
    }
  };

  // 切换编辑模式
  const toggleEditMode = () => {
    if (editMode === 'visual') {
      // 从可视化模式切换到源代码模式
      setEditMode('source');
    } else {
      // 从源代码模式切换到可视化模式
      setEditMode('visual');

      // 验证SVG内容
      try {
        const parser = new DOMParser();
        const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml');
        const parserError = svgDoc.querySelector('parsererror');

        if (parserError) {
          message.error('SVG内容无效，请修复错误后再切换');
          return;
        }

        // 更新历史记录
        addToHistory(svgContent);
      } catch (error) {
        message.error('SVG内容无效，请修复错误后再切换');
        return;
      }
    }
  };

  // 显示预览
  const showPreview = () => {
    setPreviewVisible(true);
  };

  // 关闭预览
  const closePreview = () => {
    setPreviewVisible(false);
  };

  // 保存编辑
  const handleSave = () => {
    try {
      // 确保内容不为空
      if (!svgContent || svgContent.trim() === '') {
        message.warning('内容不能为空');
        return;
      }

      // 验证SVG内容
      if (editMode === 'source') {
        try {
          const parser = new DOMParser();
          const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml');
          const parserError = svgDoc.querySelector('parsererror');

          if (parserError) {
            message.error('SVG内容无效，请修复错误后再保存');
            return;
          }
        } catch (error) {
          message.error('SVG内容无效，请修复错误后再保存');
          return;
        }
      }

      // 调用保存回调
      onSave(svgContent);
      message.success('保存成功');
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 渲染工具栏
  const renderToolbar = () => {
    return (
      <div className={styles.toolbar}>
        <Space>
          <Button
            icon={<UndoOutlined />}
            onClick={handleUndo}
            disabled={historyIndex <= 0}
            title="撤销"
          >
            撤销
          </Button>
          <Button
            icon={<RedoOutlined />}
            onClick={handleRedo}
            disabled={historyIndex >= history.length - 1}
            title="重做"
          >
            重做
          </Button>
          <Button
            icon={<EyeOutlined />}
            onClick={showPreview}
            title="预览"
          >
            预览
          </Button>
          <Button
            icon={editMode === 'visual' ? <CodeOutlined /> : <EditOutlined />}
            onClick={toggleEditMode}
            type={editMode === 'source' ? 'primary' : 'default'}
            title={editMode === 'visual' ? '源代码编辑' : '可视化编辑'}
          >
            {editMode === 'visual' ? '源代码编辑' : '可视化编辑'}
          </Button>
          <Button
            icon={<SaveOutlined />}
            type="primary"
            onClick={handleSave}
            title="保存"
          >
            保存
          </Button>
        </Space>

        <div style={{ marginLeft: 16, color: '#666' }}>
          {editMode === 'visual'
            ? '提示: 点击左侧预览区域中的任意SVG元素进行编辑，然后在右侧面板中修改属性和样式'
            : '提示: 直接编辑SVG源代码，完成后点击"保存"按钮'}
        </div>
      </div>
    );
  };

  // 渲染源代码编辑器
  const renderSourceEditor = () => {
    return (
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', padding: '16px' }}>
        <TextArea
          value={svgContent}
          onChange={(e) => setSvgContent(e.target.value)}
          style={{
            flex: 1,
            minHeight: '500px',
            fontFamily: 'monospace',
            fontSize: '14px',
            lineHeight: '1.5',
            padding: '12px'
          }}
          placeholder="在此输入SVG代码..."
        />
      </div>
    );
  };

  // 渲染可视化编辑器
  const renderVisualEditor = () => {
    return (
      <>
        {/* 左侧预览区域 */}
        <div className={styles.previewPanel}>
          <iframe
            ref={iframeRef}
            className={styles.editorFrame}
            title="SVG编辑预览"
            sandbox="allow-same-origin allow-scripts"
          />
        </div>

        {/* 右侧属性面板 */}
        <div className={styles.propertiesPanel}>
          <Tabs defaultActiveKey="attributes">
            <TabPane tab="属性" key="attributes">
              <div className={styles.propertySection}>
                {selectedElement ? (
                  <Form layout="vertical">
                    {/* 常用属性编辑 */}
                    {selectedElement.tagName !== 'svg' && (
                      <>
                        <Form.Item label="填充颜色 (fill)">
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <ColorPicker
                              value={fillColor}
                              onChange={(color) => {
                                const hexColor = color.toHexString();
                                setFillColor(hexColor);
                                updateElementAttribute('fill', hexColor);
                              }}
                            />
                            <span style={{ marginLeft: 8 }}>{fillColor}</span>
                          </div>
                        </Form.Item>

                        <Form.Item label="描边颜色 (stroke)">
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <ColorPicker
                              value={strokeColor}
                              onChange={(color) => {
                                const hexColor = color.toHexString();
                                setStrokeColor(hexColor);
                                updateElementAttribute('stroke', hexColor);
                              }}
                            />
                            <span style={{ marginLeft: 8 }}>{strokeColor}</span>
                          </div>
                        </Form.Item>

                        <Form.Item label="描边宽度 (stroke-width)">
                          <Slider
                            min={0}
                            max={20}
                            value={strokeWidth}
                            onChange={(value) => {
                              setStrokeWidth(value);
                              updateElementAttribute('stroke-width', value.toString());
                            }}
                          />
                          <div style={{ marginTop: 8 }}>{strokeWidth}px</div>
                        </Form.Item>

                        <Form.Item label="不透明度 (opacity)">
                          <Slider
                            min={0}
                            max={100}
                            value={opacity}
                            onChange={(value) => {
                              setOpacity(value);
                              updateElementAttribute('opacity', (value / 100).toString());
                            }}
                          />
                          <div style={{ marginTop: 8 }}>{opacity}%</div>
                        </Form.Item>
                      </>
                    )}

                    {/* 添加新属性 */}
                    <Divider orientation="left">添加/修改属性</Divider>
                    <Form.Item label="属性名">
                      <Input
                        value={attributeKey}
                        onChange={(e) => setAttributeKey(e.target.value)}
                        placeholder="输入属性名..."
                      />
                    </Form.Item>
                    <Form.Item label="属性值">
                      <Input
                        value={attributeValue}
                        onChange={(e) => setAttributeValue(e.target.value)}
                        placeholder="输入属性值..."
                      />
                    </Form.Item>
                    <Form.Item>
                      <Button type="primary" onClick={addNewAttribute}>
                        添加/更新属性
                      </Button>
                    </Form.Item>

                    {/* 当前属性列表 */}
                    <Divider orientation="left">当前属性</Divider>
                    <div style={{ maxHeight: '200px', overflow: 'auto' }}>
                      {Object.entries(elementAttributes).map(([key, value]) => (
                        <div key={key} style={{ marginBottom: '8px', display: 'flex', justifyContent: 'space-between' }}>
                          <span style={{ fontWeight: 'bold' }}>{key}:</span>
                          <span>{value}</span>
                        </div>
                      ))}
                    </div>

                    {selectedElement.path && (
                      <div style={{ marginTop: 16, fontSize: 12, color: '#666' }}>
                        <div>当前选中: {selectedElement.path}</div>
                      </div>
                    )}
                  </Form>
                ) : (
                  <div>
                    <p>请在左侧选择一个SVG元素进行编辑</p>
                    <p style={{ marginTop: '16px', color: '#1890ff' }}>
                      提示: 如果无法选择元素，请点击上方的"源代码编辑"按钮，直接编辑SVG代码。
                    </p>
                  </div>
                )}
              </div>
            </TabPane>
          </Tabs>
        </div>
      </>
    );
  };

  return (
    <Modal
      title="SVG 编辑器"
      open={visible}
      onCancel={onCancel}
      width="90%"
      style={{ top: 20 }}
      footer={null}
      styles={{ body: { height: 'calc(90vh - 100px)', padding: 0, overflow: 'hidden' } }}
      destroyOnHidden
    >
      <div className={styles.editorContainer}>
        {/* 工具栏 */}
        {renderToolbar()}

        {/* 编辑区域 */}
        <div className={styles.editorContent}>
          {editMode === 'source' ? renderSourceEditor() : renderVisualEditor()}
        </div>
      </div>

      {/* 预览模态框 */}
      <Modal
        title="SVG 预览"
        open={previewVisible}
        onCancel={closePreview}
        width="90%"
        styles={{ body: { padding: '16px' } }}
        footer={<Button onClick={closePreview}>关闭预览</Button>}
      >
        <div className={styles.previewContainer}>
          <iframe
            srcDoc={svgContent}
            className={styles.previewFrame}
            title="SVG内容预览"
            style={{ width: '100%', height: '70vh', border: 'none' }}
          />
        </div>
      </Modal>
    </Modal>
  );
};

export default SvgEditor;
