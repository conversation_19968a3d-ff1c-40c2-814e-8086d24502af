// import React, { useState } from 'react';
// import { <PERSON><PERSON>, Card, Tabs, message } from 'antd';
// import { EditOutlined } from '@ant-design/icons';
// import ContentEditor from './ContentEditor';

// const { TabPane } = Tabs;

// // 示例HTML内容
// const sampleHtmlContent = `
// <div style="padding: 20px; font-family: Arial, sans-serif;">
//   <h1 style="color: #333;">HTML 内容示例</h1>
//   <p style="font-size: 16px; line-height: 1.5;">
//     这是一个<strong>HTML</strong>内容示例，您可以编辑这段文本，
//     修改样式，或者移动元素的位置。
//   </p>
//   <div style="background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-top: 20px;">
//     <h3 style="color: #0066cc;">功能说明</h3>
//     <ul>
//       <li>点击元素可以选中它</li>
//       <li>在右侧面板修改文本内容</li>
//       <li>调整文字颜色和大小</li>
//       <li>移动元素位置</li>
//     </ul>
//   </div>
// </div>
// `;

// // 示例SVG内容
// const sampleSvgContent = `
// <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
//   <rect x="10" y="10" width="180" height="180" fill="#f0f0f0" stroke="#333" stroke-width="2" />
//   <circle cx="100" cy="100" r="50" fill="#0066cc" />
//   <text x="100" y="100" font-family="Arial" font-size="16" fill="white" text-anchor="middle" dominant-baseline="middle">SVG 示例</text>
// </svg>
// `;

// /**
//  * 编辑器示例组件
//  * 展示如何使用ContentEditor组件
//  */
// const EditorExample: React.FC = () => {
//   // 当前选中的内容类型
//   const [activeTab, setActiveTab] = useState<string>('html');
  
//   // 编辑器可见状态
//   const [editorVisible, setEditorVisible] = useState<boolean>(false);
  
//   // 内容状态
//   const [htmlContent, setHtmlContent] = useState<string>(sampleHtmlContent);
//   const [svgContent, setSvgContent] = useState<string>(sampleSvgContent);
  
//   // 获取当前内容
//   const getCurrentContent = (): string => {
//     return activeTab === 'html' ? htmlContent : svgContent;
//   };
  
//   // 打开编辑器
//   const handleEdit = () => {
//     setEditorVisible(true);
//   };
  
//   // 取消编辑
//   const handleCancel = () => {
//     setEditorVisible(false);
//   };
  
//   // 保存编辑内容
//   const handleSave = (newContent: string) => {
//     if (activeTab === 'html') {
//       setHtmlContent(newContent);
//     } else {
//       setSvgContent(newContent);
//     }
//     setEditorVisible(false);
//     message.success('内容已保存');
//   };
  
//   return (
//     <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
//       <h1>内容编辑器示例</h1>
//       <p>点击"编辑"按钮打开编辑器，系统会自动判断内容类型并打开相应的编辑器。</p>
      
//       <Tabs activeKey={activeTab} onChange={setActiveTab}>
//         <TabPane tab="HTML 内容" key="html">
//           <Card 
//             title="HTML 内容预览" 
//             extra={
//               <Button 
//                 type="primary" 
//                 icon={<EditOutlined />} 
//                 onClick={handleEdit}
//               >
//                 编辑
//               </Button>
//             }
//           >
//             <div 
//               dangerouslySetInnerHTML={{ __html: htmlContent }} 
//               style={{ minHeight: 200 }}
//             />
//           </Card>
//         </TabPane>
        
//         <TabPane tab="SVG 内容" key="svg">
//           <Card 
//             title="SVG 内容预览" 
//             extra={
//               <Button 
//                 type="primary" 
//                 icon={<EditOutlined />} 
//                 onClick={handleEdit}
//               >
//                 编辑
//               </Button>
//             }
//           >
//             <div 
//               dangerouslySetInnerHTML={{ __html: svgContent }} 
//               style={{ minHeight: 200, display: 'flex', justifyContent: 'center' }}
//             />
//           </Card>
//         </TabPane>
//       </Tabs>
      
//       {/* 内容编辑器 */}
//       <ContentEditor
//         content={getCurrentContent()}
//         onSave={handleSave}
//         onCancel={handleCancel}
//         visible={editorVisible}
//       />
//     </div>
//   );
// };

// export default EditorExample;
