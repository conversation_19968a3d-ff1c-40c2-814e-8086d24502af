import React, { useState } from 'react';
import {  Form, Input, InputNumber, ColorPicker, Button, Select, Slider, Tabs, Space } from 'antd';
import { DeleteOutlined, CopyOutlined, PlusOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import styles from './HtmlSvgEditor.module.css';

const { Option } = Select;
const { TabPane } = Tabs;

interface SvgElementEditorProps {
  elementData: {
    tagName: string;
    id: string;
    attributes: Record<string, string>;
    outerHTML: string;
  } | null;
  onAttributeChange: (attribute: string, value: string) => void;
  onDeleteElement: () => void;
  onAddElement: (tagName: string, attributes: Record<string, string>) => void;
  onMoveElement: (direction: 'up' | 'down') => void;
  onCopyElement: () => void;
}

// SVG形状类型选项
const svgShapeOptions = [
  { label: '矩形', value: 'rect' },
  { label: '圆形', value: 'circle' },
  { label: '椭圆', value: 'ellipse' },
  { label: '线段', value: 'line' },
  { label: '多边形', value: 'polygon' },
  { label: '路径', value: 'path' },
  { label: '文本', value: 'text' },
  { label: '组', value: 'g' },
];


// SVG线条类型选项
const strokeOptions = [
  { label: '实线', value: '' },
  { label: '虚线', value: '5,5' },
  { label: '点划线', value: '10,5,2,5' },
];

/**
 * 根据SVG元素类型获取基本属性
 */
const getDefaultAttributes = (tagName: string): Record<string, string> => {
  switch (tagName) {
    case 'rect':
      return {
        x: '10',
        y: '10',
        width: '100',
        height: '60',
        fill: '#4a90e2',
        rx: '0',
        ry: '0'
      };
    case 'circle':
      return {
        cx: '50',
        cy: '50',
        r: '40',
        fill: '#50e3c2'
      };
    case 'ellipse':
      return {
        cx: '50',
        cy: '50',
        rx: '40',
        ry: '25',
        fill: '#ff6b6b'
      };
    case 'line':
      return {
        x1: '10',
        y1: '10',
        x2: '90',
        y2: '90',
        stroke: '#4a4a4a',
        'stroke-width': '2'
      };
    case 'polygon':
      return {
        points: '50,10 90,90 10,90',
        fill: '#f5a623'
      };
    case 'path':
      return {
        d: 'M10,10 L90,10 L90,90 Z',
        fill: '#9013fe'
      };
    case 'text':
      return {
        x: '50',
        y: '50',
        'font-size': '16',
        'text-anchor': 'middle',
        fill: '#4a4a4a'
      };
    case 'g':
      return {
        transform: 'translate(0,0)'
      };
    default:
      return {};
  }
};

/**
 * 解析颜色值以适应颜色选择器
 */
const parseColor = (color: string): string => {
  if (!color) return '';
  if (color === 'none') return 'transparent';
  return color;
};

const SvgElementEditor: React.FC<SvgElementEditorProps> = ({
  elementData,
  onAttributeChange,
  onDeleteElement,
  onAddElement,
  onMoveElement,
  onCopyElement
}) => {
  const [newShapeType, setNewShapeType] = useState<string>('rect');
  
  if (!elementData) {
    return (
      <div className={styles.emptyEditor}>
        <p>请在左侧选择一个SVG元素进行编辑</p>
        <div className={styles.addElementSection}>
          <h3>添加新图形</h3>
          <div className={styles.addElementControls}>
            <Select
              value={newShapeType}
              onChange={(value) => setNewShapeType(value)}
              style={{ width: 120 }}
            >
              {svgShapeOptions.map(option => (
                <Option key={option.value} value={option.value}>{option.label}</Option>
              ))}
            </Select>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={() => onAddElement(newShapeType, getDefaultAttributes(newShapeType))}
            >
              添加
            </Button>
          </div>
        </div>
      </div>
    );
  }
  
  // 判断是哪种类型的SVG元素
  const isSvgRoot = elementData.tagName.toLowerCase() === 'svg';
  const isShape = ['rect', 'circle', 'ellipse', 'line', 'polygon', 'path'].includes(elementData.tagName.toLowerCase());
  const isText = elementData.tagName.toLowerCase() === 'text';
  const isGroup = elementData.tagName.toLowerCase() === 'g';
  
  // 获取元素特定属性
  const attributes = elementData.attributes || {};
  
  // 渲染SVG元素的通用属性编辑界面
  const renderCommonAttributes = () => (
    <div>
      <Form layout="vertical" size="small">
        <div className={styles.propertyItem}>
          <Form.Item label="ID">
            <Input
              value={attributes.id || ''}
              onChange={(e) => onAttributeChange('id', e.target.value)}
            />
          </Form.Item>
        </div>
        
        {!isSvgRoot && (
          <>
            <div className={styles.propertyItem}>
              <Form.Item label="填充颜色">
                <ColorPicker
                  format="hex"
                  showText
                  value={parseColor(attributes.fill || '')}
                  onChange={(color) => onAttributeChange('fill', color.toHexString())}
                />
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="边框颜色">
                <ColorPicker
                  format="hex"
                  showText
                  value={parseColor(attributes.stroke || '')}
                  onChange={(color) => onAttributeChange('stroke', color.toHexString())}
                />
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="边框宽度">
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <InputNumber
                    value={Number(attributes['stroke-width']) || 0}
                    min={0}
                    max={20}
                    step={0.5}
                    onChange={(value) => onAttributeChange('stroke-width', String(value))}
                    style={{ width: 80 }}
                  />
                  <Slider
                    value={Number(attributes['stroke-width']) || 0}
                    min={0}
                    max={20}
                    step={0.5}
                    onChange={(value) => onAttributeChange('stroke-width', String(value))}
                    style={{ flex: 1, marginLeft: 16 }}
                  />
                </div>
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="边框类型">
                <Select
                  value={attributes['stroke-dasharray'] || ''}
                  onChange={(value) => onAttributeChange('stroke-dasharray', value)}
                  style={{ width: '100%' }}
                >
                  {strokeOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="透明度">
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <InputNumber
                    value={Number(attributes.opacity) || 1}
                    min={0}
                    max={1}
                    step={0.1}
                    onChange={(value) => onAttributeChange('opacity', String(value))}
                    style={{ width: 80 }}
                  />
                  <Slider
                    value={Number(attributes.opacity) || 1}
                    min={0}
                    max={1}
                    step={0.1}
                    onChange={(value) => onAttributeChange('opacity', String(value))}
                    style={{ flex: 1, marginLeft: 16 }}
                  />
                </div>
              </Form.Item>
            </div>
          </>
        )}
      </Form>
    </div>
  );
  
  // 渲染特定形状的属性编辑界面
  const renderShapeAttributes = () => {
    if (!isShape) return null;
    
    const tagName = elementData.tagName.toLowerCase();
    
    switch (tagName) {
      case 'rect':
        return (
          <Form layout="vertical" size="small">
            <div className={styles.propertyItem}>
              <Form.Item label="位置">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="X"
                    value={attributes.x || '0'}
                    onChange={(e) => onAttributeChange('x', e.target.value)}
                    style={{ width: '50%' }}
                  />
                  <Input
                    addonBefore="Y"
                    value={attributes.y || '0'}
                    onChange={(e) => onAttributeChange('y', e.target.value)}
                    style={{ width: '50%' }}
                  />
                </div>
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="尺寸">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="宽"
                    value={attributes.width || '0'}
                    onChange={(e) => onAttributeChange('width', e.target.value)}
                    style={{ width: '50%' }}
                  />
                  <Input
                    addonBefore="高"
                    value={attributes.height || '0'}
                    onChange={(e) => onAttributeChange('height', e.target.value)}
                    style={{ width: '50%' }}
                  />
                </div>
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="圆角">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="RX"
                    value={attributes.rx || '0'}
                    onChange={(e) => onAttributeChange('rx', e.target.value)}
                    style={{ width: '50%' }}
                  />
                  <Input
                    addonBefore="RY"
                    value={attributes.ry || '0'}
                    onChange={(e) => onAttributeChange('ry', e.target.value)}
                    style={{ width: '50%' }}
                  />
                </div>
              </Form.Item>
            </div>
          </Form>
        );
      
      case 'circle':
        return (
          <Form layout="vertical" size="small">
            <div className={styles.propertyItem}>
              <Form.Item label="圆心位置">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="CX"
                    value={attributes.cx || '0'}
                    onChange={(e) => onAttributeChange('cx', e.target.value)}
                    style={{ width: '50%' }}
                  />
                  <Input
                    addonBefore="CY"
                    value={attributes.cy || '0'}
                    onChange={(e) => onAttributeChange('cy', e.target.value)}
                    style={{ width: '50%' }}
                  />
                </div>
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="半径">
                <Input
                  addonBefore="R"
                  value={attributes.r || '0'}
                  onChange={(e) => onAttributeChange('r', e.target.value)}
                />
              </Form.Item>
            </div>
          </Form>
        );
      
      case 'ellipse':
        return (
          <Form layout="vertical" size="small">
            <div className={styles.propertyItem}>
              <Form.Item label="圆心位置">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="CX"
                    value={attributes.cx || '0'}
                    onChange={(e) => onAttributeChange('cx', e.target.value)}
                    style={{ width: '50%' }}
                  />
                  <Input
                    addonBefore="CY"
                    value={attributes.cy || '0'}
                    onChange={(e) => onAttributeChange('cy', e.target.value)}
                    style={{ width: '50%' }}
                  />
                </div>
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="半径">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="RX"
                    value={attributes.rx || '0'}
                    onChange={(e) => onAttributeChange('rx', e.target.value)}
                    style={{ width: '50%' }}
                  />
                  <Input
                    addonBefore="RY"
                    value={attributes.ry || '0'}
                    onChange={(e) => onAttributeChange('ry', e.target.value)}
                    style={{ width: '50%' }}
                  />
                </div>
              </Form.Item>
            </div>
          </Form>
        );
      
      case 'line':
        return (
          <Form layout="vertical" size="small">
            <div className={styles.propertyItem}>
              <Form.Item label="起点">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="X1"
                    value={attributes.x1 || '0'}
                    onChange={(e) => onAttributeChange('x1', e.target.value)}
                    style={{ width: '50%' }}
                  />
                  <Input
                    addonBefore="Y1"
                    value={attributes.y1 || '0'}
                    onChange={(e) => onAttributeChange('y1', e.target.value)}
                    style={{ width: '50%' }}
                  />
                </div>
              </Form.Item>
            </div>
            
            <div className={styles.propertyItem}>
              <Form.Item label="终点">
                <div style={{ display: 'flex', gap: 8 }}>
                  <Input
                    addonBefore="X2"
                    value={attributes.x2 || '0'}
                    onChange={(e) => onAttributeChange('x2', e.target.value)}
                    style={{ width: '50%' }}
                  />
                  <Input
                    addonBefore="Y2"
                    value={attributes.y2 || '0'}
                    onChange={(e) => onAttributeChange('y2', e.target.value)}
                    style={{ width: '50%' }}
                  />
                </div>
              </Form.Item>
            </div>
          </Form>
        );
      
      case 'polygon':
        return (
          <Form layout="vertical" size="small">
            <div className={styles.propertyItem}>
              <Form.Item label="点坐标集合 (x,y x,y ...)">
                <Input
                  value={attributes.points || ''}
                  onChange={(e) => onAttributeChange('points', e.target.value)}
                />
              </Form.Item>
            </div>
          </Form>
        );
      
      case 'path':
        return (
          <Form layout="vertical" size="small">
            <div className={styles.propertyItem}>
              <Form.Item label="路径数据">
                <Input.TextArea
                  rows={4}
                  value={attributes.d || ''}
                  onChange={(e) => onAttributeChange('d', e.target.value)}
                />
              </Form.Item>
            </div>
          </Form>
        );
      
      default:
        return null;
    }
  };
  
  // 渲染文本元素的属性编辑界面
  const renderTextAttributes = () => {
    if (!isText) return null;
    
    return (
      <Form layout="vertical" size="small">
        <div className={styles.propertyItem}>
          <Form.Item label="位置">
            <div style={{ display: 'flex', gap: 8 }}>
              <Input
                addonBefore="X"
                value={attributes.x || '0'}
                onChange={(e) => onAttributeChange('x', e.target.value)}
                style={{ width: '50%' }}
              />
              <Input
                addonBefore="Y"
                value={attributes.y || '0'}
                onChange={(e) => onAttributeChange('y', e.target.value)}
                style={{ width: '50%' }}
              />
            </div>
          </Form.Item>
        </div>
        
        <div className={styles.propertyItem}>
          <Form.Item label="文本内容">
            <Input.TextArea
              rows={3}
              value={elementData.outerHTML.replace(/<[^>]*>/g, '').trim()}
              onChange={(e) => {
                // 直接修改文本内容，这需要特殊处理
                onAttributeChange('textContent', e.target.value);
              }}
            />
          </Form.Item>
        </div>
        
        <div className={styles.propertyItem}>
          <Form.Item label="字体大小">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <InputNumber
                value={Number(attributes['font-size']) || 16}
                min={8}
                max={72}
                onChange={(value) => onAttributeChange('font-size', String(value))}
                style={{ width: 80 }}
              />
              <Slider
                value={Number(attributes['font-size']) || 16}
                min={8}
                max={72}
                onChange={(value) => onAttributeChange('font-size', String(value))}
                style={{ flex: 1, marginLeft: 16 }}
              />
            </div>
          </Form.Item>
        </div>
        
        <div className={styles.propertyItem}>
          <Form.Item label="文本对齐">
            <Select
              value={attributes['text-anchor'] || 'start'}
              onChange={(value) => onAttributeChange('text-anchor', value)}
              style={{ width: '100%' }}
            >
              <Option value="start">左对齐</Option>
              <Option value="middle">居中</Option>
              <Option value="end">右对齐</Option>
            </Select>
          </Form.Item>
        </div>
        
        <div className={styles.propertyItem}>
          <Form.Item label="字体风格">
            <Select
              value={attributes['font-style'] || 'normal'}
              onChange={(value) => onAttributeChange('font-style', value)}
              style={{ width: '100%' }}
            >
              <Option value="normal">正常</Option>
              <Option value="italic">斜体</Option>
              <Option value="oblique">倾斜</Option>
            </Select>
          </Form.Item>
        </div>
        
        <div className={styles.propertyItem}>
          <Form.Item label="字体粗细">
            <Select
              value={attributes['font-weight'] || 'normal'}
              onChange={(value) => onAttributeChange('font-weight', value)}
              style={{ width: '100%' }}
            >
              <Option value="normal">正常</Option>
              <Option value="bold">粗体</Option>
              <Option value="lighter">细体</Option>
              <Option value="bolder">特粗</Option>
            </Select>
          </Form.Item>
        </div>
      </Form>
    );
  };
  
  // 渲染SVG组的属性编辑界面
  const renderGroupAttributes = () => {
    if (!isGroup) return null;
    
    return (
      <Form layout="vertical" size="small">
        <div className={styles.propertyItem}>
          <Form.Item label="变换 (transform)">
            <Input
              value={attributes.transform || ''}
              onChange={(e) => onAttributeChange('transform', e.target.value)}
              placeholder="例如: translate(10,20) rotate(45)"
            />
          </Form.Item>
        </div>
      </Form>
    );
  };
  
  // 渲染SVG根元素的属性编辑界面
  const renderRootAttributes = () => {
    if (!isSvgRoot) return null;
    
    return (
      <Form layout="vertical" size="small">
        <div className={styles.propertyItem}>
          <Form.Item label="视口 (viewBox)">
            <Input
              value={attributes.viewBox || ''}
              onChange={(e) => onAttributeChange('viewBox', e.target.value)}
              placeholder="例如: 0 0 800 400"
            />
          </Form.Item>
        </div>
        
        <div className={styles.propertyItem}>
          <Form.Item label="尺寸">
            <div style={{ display: 'flex', gap: 8 }}>
              <Input
                addonBefore="宽"
                value={attributes.width || ''}
                onChange={(e) => onAttributeChange('width', e.target.value)}
                style={{ width: '50%' }}
              />
              <Input
                addonBefore="高"
                value={attributes.height || ''}
                onChange={(e) => onAttributeChange('height', e.target.value)}
                style={{ width: '50%' }}
              />
            </div>
          </Form.Item>
        </div>
        
        <div className={styles.propertyItem}>
          <Form.Item label="保留比例">
            <Select
              value={attributes.preserveAspectRatio || 'xMidYMid meet'}
              onChange={(value) => onAttributeChange('preserveAspectRatio', value)}
              style={{ width: '100%' }}
            >
              <Option value="xMidYMid meet">保持比例 (默认)</Option>
              <Option value="none">忽略比例</Option>
              <Option value="xMinYMin meet">左上对齐</Option>
              <Option value="xMaxYMax meet">右下对齐</Option>
            </Select>
          </Form.Item>
        </div>
        
        <div className={styles.propertyItem}>
          <Form.Item label="背景颜色">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <ColorPicker
                value={parseColor(attributes.fill || '#ffffff')}
                onChange={(_, hex) => onAttributeChange('fill', hex)}
                style={{ marginRight: 8 }}
              />
              <Input
                value={attributes.fill || '#ffffff'}
                onChange={(e) => onAttributeChange('fill', e.target.value)}
                style={{ flex: 1 }}
              />
            </div>
          </Form.Item>
        </div>
      </Form>
    );
  };
  
  // 添加新元素的界面
  const renderAddElement = () => (
    <div className={styles.addElementSection}>
      <h3>添加新图形</h3>
      <div className={styles.addElementControls}>
        <Select
          value={newShapeType}
          onChange={(value) => setNewShapeType(value)}
          style={{ width: 120 }}
        >
          {svgShapeOptions.map(option => (
            <Option key={option.value} value={option.value}>{option.label}</Option>
          ))}
        </Select>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={() => onAddElement(newShapeType, getDefaultAttributes(newShapeType))}
        >
          添加
        </Button>
      </div>
    </div>
  );
  
  return (
    <div className={styles.svgElementEditor}>
      <div className={styles.elementHeader}>
        <h3>编辑 {elementData.tagName.toLowerCase()} 元素</h3>
        <Space>
          <Button 
            icon={<CopyOutlined />} 
            onClick={onCopyElement}
            size="small"
            title="复制元素"
          />
          <Button 
            icon={<ArrowUpOutlined />} 
            onClick={() => onMoveElement('up')}
            size="small"
            title="上移一层"
          />
          <Button 
            icon={<ArrowDownOutlined />} 
            onClick={() => onMoveElement('down')}
            size="small"
            title="下移一层"
          />
          <Button 
            type="primary" 
            danger
            icon={<DeleteOutlined />} 
            onClick={onDeleteElement}
            size="small"
            title="删除元素"
          />
        </Space>
      </div>
      
      <Tabs defaultActiveKey="1">
        <TabPane tab="通用" key="1">
          {renderCommonAttributes()}
        </TabPane>
        
        {isShape && (
          <TabPane tab="形状" key="2">
            {renderShapeAttributes()}
          </TabPane>
        )}
        
        {isText && (
          <TabPane tab="文本" key="2">
            {renderTextAttributes()}
          </TabPane>
        )}
        
        {isGroup && (
          <TabPane tab="组" key="2">
            {renderGroupAttributes()}
          </TabPane>
        )}
        
        {isSvgRoot && (
          <TabPane tab="SVG" key="2">
            {renderRootAttributes()}
          </TabPane>
        )}
      </Tabs>
      
      {isSvgRoot && renderAddElement()}
    </div>
  );
};

export default SvgElementEditor; 