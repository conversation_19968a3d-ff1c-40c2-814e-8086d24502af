import React, { useState, useEffect, useRef } from 'react';
import { Button, Tabs, Space, Modal, message } from 'antd';
import {  SaveOutlined, EyeOutlined, UndoOutlined, RedoOutlined } from '@ant-design/icons';
import styles from './HtmlSvgEditor.module.css';

interface HtmlSvgEditorProps {
  content: string; // HTML或SVG内容
  type: 'html' | 'svg';
  onSave: (newContent: string) => void;
  onCancel: () => void;
  visible: boolean;
}

/**
 * HTML/SVG编辑器组件
 * 提供类似PPT的编辑功能，可以修改内容、样式和布局
 */
const HtmlSvgEditor: React.FC<HtmlSvgEditorProps> = ({ 
  content, 
  type, 
  onSave, 
  onCancel, 
  visible 
}) => {
  const [editedContent, setEditedContent] = useState<string>(content);
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [history, setHistory] = useState<string[]>([content]);
  const [historyIndex, setHistoryIndex] = useState<number>(0);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  // 初始化编辑内容
  useEffect(() => {
    if (content) {
      setEditedContent(content);
      setHistory([content]);
      setHistoryIndex(0);
    }
  }, [content, visible]);
  
  // 渲染编辑预览
  useEffect(() => {
    renderEditPreview();
  }, [editedContent]);
  
  // 初始化iframe内容
  const renderEditPreview = () => {
    if (!iframeRef.current) return;
    
    const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
    if (!iframeDoc) return;
    
    try {
      iframeDoc.open();
      
      if (type === 'html') {
        // 为HTML内容添加编辑模式所需的脚本和样式
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <style>
                body { margin: 0; padding: 10px; }
                .editable-highlight { outline: 2px dashed #1890ff; position: relative; }
                .element-toolbar {
                  position: absolute;
                  top: -30px;
                  left: 0;
                  background: #fff;
                  border: 1px solid #d9d9d9;
                  border-radius: 2px;
                  padding: 2px;
                  z-index: 1000;
                  display: flex;
                }
                .editor-button {
                  margin: 0 2px;
                  cursor: pointer;
                  padding: 2px 4px;
                  font-size: 12px;
                }
              </style>
              <script>
                document.addEventListener('DOMContentLoaded', function() {
                  // 为所有元素添加点击事件
                  const makeElementsEditable = () => {
                    const editableElements = document.querySelectorAll('.slide *:not(script):not(style)');
                    editableElements.forEach(el => {
                      el.addEventListener('click', (e) => {
                        e.stopPropagation();
                        
                        // 移除其他元素的高亮
                        document.querySelectorAll('.editable-highlight').forEach(hl => {
                          hl.classList.remove('editable-highlight');
                        });
                        
                        // 添加高亮
                        el.classList.add('editable-highlight');
                        
                        // 通知父窗口选中了元素
                        window.parent.postMessage({
                          type: 'elementSelected',
                          tagName: el.tagName,
                          id: el.id,
                          classes: el.className,
                          styles: el.getAttribute('style') || '',
                          innerText: el.innerText || '',
                          innerHTML: el.innerHTML || '',
                          outerHTML: el.outerHTML || ''
                        }, '*');
                      });
                    });
                  };
                  
                  // 初始化编辑功能
                  makeElementsEditable();
                });
              </script>
            </head>
            <body>${editedContent}</body>
          </html>
        `);
      } else if (type === 'svg') {
        // 为SVG内容添加编辑模式所需的脚本和样式
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <style>
                body { 
                  margin: 0; 
                  padding: 10px; 
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  min-height: 400px;
                }
                svg { max-width: 100%; }
                svg * {
                  cursor: pointer;
                }
                .svg-element-selected {
                  stroke: #1890ff !important;
                  stroke-width: 2px !important;
                  stroke-dasharray: 5,5 !important;
                }
              </style>
              <script>
                document.addEventListener('DOMContentLoaded', function() {
                  const svg = document.querySelector('svg');
                  if (!svg) return;
                  
                  // 为所有SVG元素添加点击事件
                  const svgElements = svg.querySelectorAll('*');
                  svgElements.forEach(el => {
                    if (el.tagName === 'svg') return;
                    
                    el.addEventListener('click', (e) => {
                      e.stopPropagation();
                      
                      // 移除其他元素的高亮
                      document.querySelectorAll('.svg-element-selected').forEach(selected => {
                        selected.classList.remove('svg-element-selected');
                      });
                      
                      // 添加高亮
                      el.classList.add('svg-element-selected');
                      
                      // 获取元素属性
                      const attributes = {};
                      Array.from(el.attributes).forEach(attr => {
                        attributes[attr.name] = attr.value;
                      });
                      
                      // 通知父窗口选中了SVG元素
                      window.parent.postMessage({
                        type: 'svgElementSelected',
                        tagName: el.tagName,
                        id: el.id,
                        attributes: attributes,
                        outerHTML: el.outerHTML
                      }, '*');
                    });
                  });
                  
                  // 点击SVG空白区域选中整个SVG
                  svg.addEventListener('click', (e) => {
                    if (e.target === svg) {
                      document.querySelectorAll('.svg-element-selected').forEach(selected => {
                        selected.classList.remove('svg-element-selected');
                      });
                      
                      // 获取SVG属性
                      const attributes = {};
                      Array.from(svg.attributes).forEach(attr => {
                        attributes[attr.name] = attr.value;
                      });
                      
                      // 通知父窗口选中了整个SVG
                      window.parent.postMessage({
                        type: 'svgElementSelected',
                        tagName: 'svg',
                        id: svg.id,
                        attributes: attributes,
                        outerHTML: svg.outerHTML
                      }, '*');
                    }
                  });
                });
              </script>
            </head>
            <body>${editedContent}</body>
          </html>
        `);
      }
      
      iframeDoc.close();
    } catch (error) {
      console.error('渲染编辑预览失败:', error);
      message.error('渲染编辑预览失败');
    }
  };
  
  // 监听iframe中的消息事件
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'elementSelected') {
        console.log('选中HTML元素:', event.data);
        // 处理HTML元素选中
      } else if (event.data.type === 'svgElementSelected') {
        console.log('选中SVG元素:', event.data);
        // 处理SVG元素选中
      }
    };
    
    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);
  
  // 保存修改
  const handleSave = () => {
    try {
      onSave(editedContent);
      message.success('保存成功');
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };
  
  // 更新历史记录
  // const updateHistory = (newContent: string) => {
  //   const newHistory = [...history.slice(0, historyIndex + 1), newContent];
  //   setHistory(newHistory);
  //   setHistoryIndex(newHistory.length - 1);
  // };
  
  // 撤销操作
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setEditedContent(history[historyIndex - 1]);
    }
  };
  
  // 重做操作
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setEditedContent(history[historyIndex + 1]);
    }
  };
  
  // 显示预览模态框
  const showPreview = () => {
    setPreviewVisible(true);
  };
  
  // 切换到编辑模式
  const switchToEditMode = () => {
    setPreviewVisible(false);
  };
  
  return (
    <Modal
      title={`${type === 'html' ? 'HTML' : 'SVG'} 编辑器`}
      open={visible}
      onCancel={onCancel}
      width="90%"
      style={{ top: 20 }}
      footer={null}
      styles={{ body: { height: 'calc(90vh - 100px)', padding: 0, overflow: 'hidden' } }}
      destroyOnHidden
    >
      <div className={styles.editorContainer}>
        {/* 工具栏 */}
        <div className={styles.toolbar}>
          <Space>
            <Button icon={<UndoOutlined />} onClick={handleUndo} disabled={historyIndex <= 0}>
              撤销
            </Button>
            <Button icon={<RedoOutlined />} onClick={handleRedo} disabled={historyIndex >= history.length - 1}>
              重做
            </Button>
            <Button icon={<EyeOutlined />} onClick={showPreview}>
              预览
            </Button>
            <Button icon={<SaveOutlined />} type="primary" onClick={handleSave}>
              保存
            </Button>
          </Space>
        </div>
        
        {/* 编辑区域 */}
        <div className={styles.editorContent}>
          {/* 左侧编辑预览 */}
          <div className={styles.previewPanel}>
            <iframe
              ref={iframeRef}
              className={styles.editorFrame}
              title="编辑预览"
            />
          </div>
          
          {/* 右侧属性面板 */}
          <div className={styles.propertiesPanel}>
            <Tabs defaultActiveKey="1">
              <Tabs.TabPane tab="样式" key="1">
                {/* 这里将添加样式编辑组件 */}
                <div className={styles.propertySection}>
                  <p>请在左侧选择一个元素进行编辑</p>
                </div>
              </Tabs.TabPane>
              <Tabs.TabPane tab="内容" key="2">
                {/* 这里将添加内容编辑组件 */}
                <div className={styles.propertySection}>
                  <p>请在左侧选择一个元素进行编辑</p>
                </div>
              </Tabs.TabPane>
            </Tabs>
          </div>
        </div>
      </div>
      
      {/* 预览模态框 */}
      <Modal
        title="预览"
        open={previewVisible}
        onCancel={switchToEditMode}
        width="90%"
        footer={<Button onClick={switchToEditMode}>返回编辑</Button>}
      >
        <div className={styles.previewContainer}>
          <iframe
            srcDoc={editedContent}
            className={styles.previewFrame}
            title="内容预览"
          />
        </div>
      </Modal>
    </Modal>
  );
};

export default HtmlSvgEditor; 