import React, { useState, useEffect } from 'react';
import { notification, Button, Modal, Spin, Select, Typography, Alert, Switch, Space, Radio } from 'antd';
import { FilePdfOutlined, SettingOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { htmlToPdfLegacy } from '../../components/products/AICourse/AICourseUtils';
import SafeTooltip from '../common/SafeTooltip';
import { WorkflowCodeBlock } from '../../utils/WorkflowCodeBlockProcessor';

const { Option } = Select;
const { Text } = Typography;
const { Group: RadioGroup, Button: RadioButton } = Radio;

interface ApiHtmlToPdfExporterProps {
  iframeRef: React.RefObject<HTMLIFrameElement>; // 引用包含HTML内容的iframe
  filename?: string; // PDF文件名
  disabled?: boolean; // 是否禁用导出
  sessionId?: string; // 会话ID，用于生成文件名
  codeBlockId?: string; // 代码块ID，用于生成文件名
  codeBlocks?: WorkflowCodeBlock[]; // 所有代码块，用于批量导出
  allHtmlMode?: boolean; // 是否为批量导出模式
  showButton?: boolean; // 是否显示按钮，默认为true
  onRef?: (ref: { openModal: () => void }) => void; // 通过ref暴露方法
}

// 页面方向

const ApiHtmlToPdfExporter: React.FC<ApiHtmlToPdfExporterProps> = ({
  iframeRef,
  filename = 'document.pdf',
  disabled = false,
  sessionId = '',
  codeBlockId = '',
  codeBlocks = [],
  allHtmlMode = false,
  showButton = true, // 默认显示按钮
  onRef,
}) => {
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [quality, setQuality] = useState<number>(2);
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);
  const [exportAllHtml, setExportAllHtml] = useState<boolean>(true); // 默认导出所有HTML
  const [pdfFormat, setPdfFormat] = useState<'A4' | 'PPT'>('PPT'); // 使用API格式

  // 暴露打开模态框的方法
  useEffect(() => {
    if (onRef) {
      onRef({
        openModal: handleExportClick
      });
    }
  }, [onRef]);

  // 提示用户打开导出设置
  const handleExportClick = () => {
    setIsModalVisible(true);
  };

  // 关闭设置模态框
  const handleCancel = () => {
    setIsModalVisible(false);
  };

  // 导出为PDF（单个HTML或所有HTML）
  const handleExport = async () => {
    if (!iframeRef.current && !exportAllHtml) {
      notification.error({ message: '导出失败', description: '无法获取内容' });
      return;
    }

    setIsExporting(true);

    try {
      // 创建自定义文件名
      let customFilename = filename;

      if (exportAllHtml) {
        // 批量导出所有HTML内容为多页PDF
        customFilename = sessionId
          ? `html-slides-${sessionId.substring(0, 8)}.pdf`
          : 'html-slides.pdf';

        // 过滤出HTML类型的代码块
        const htmlBlocks = codeBlocks.filter(block => 
          block.language.toLowerCase() === 'html' || 
          block.code.trim().toLowerCase().includes('<html') ||
          block.code.trim().toLowerCase().includes('<!doctype html>')
        );

        if (htmlBlocks.length === 0) {
          throw new Error('没有可导出的HTML内容');
        }

        console.log(`开始批量导出${htmlBlocks.length}个HTML内容到PDF (${pdfFormat}格式)`);
        
        // 收集所有HTML内容
        // const htmlContents = htmlBlocks.map(block => block.code);
        // 使用多HTML导出功能
        await exportHtmlToPdf(htmlBlocks, customFilename);

      } else {
        // 单个HTML内容导出
        customFilename = sessionId && codeBlockId
          ? `html-${sessionId.substring(0, 8)}-${codeBlockId}.pdf`
          : filename;

        // 确保 iframeRef.current 存在
        if (!iframeRef.current) {
          throw new Error('无法获取iframe引用');
        }

        const iframe = iframeRef.current;

        // 直接截取当前iframe内容
        if (!iframe.contentDocument) {
          throw new Error('无法访问iframe内容');
        }

        // 获取IFRAME中的完整HTML内容
        const docType = iframe.contentDocument.doctype;
        const docTypeString = docType ? 
          new XMLSerializer().serializeToString(docType) : 
          '<!DOCTYPE html>';
        
        const htmlContent = docTypeString + iframe.contentDocument.documentElement.outerHTML;
        
        // 导出单个HTML内容
        await exportHtmlToPdf(htmlContent, customFilename);
      }

      notification.success({
        message: '导出成功',
        description: `已成功导出为 ${customFilename} (${pdfFormat}格式)`
      });

    } catch (error) {
      console.error('PDF导出错误:', error);
      notification.error({
        message: '导出失败',
        description: error instanceof Error ? error.message : '生成PDF时发生错误'
      });
    } finally {
      setIsExporting(false);
      setIsModalVisible(false);
    }
  };

  // 使用API导出HTML为PDF
  const exportHtmlToPdf = async (html: string |WorkflowCodeBlock[], customFilename?: string): Promise<void> => {
    console.log(html,'html');
    try {
      // 处理单个HTML内容的特殊情况 - 从iframe获取实际渲染内容
      if (!Array.isArray(html) && iframeRef.current) {
        try {
          // 抓取iframe中实际渲染的内容
          const iframe = iframeRef.current;
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
          
          if (iframeDoc) {
            console.log('从iframe获取实际渲染内容');
            
            // 获取完整HTML结构，包括文档类型声明
            const docType = iframeDoc.doctype ? 
              new XMLSerializer().serializeToString(iframeDoc.doctype) : 
              '<!DOCTYPE html>';
              
            // 获取完整HTML，保留所有CSS和脚本
            const renderedHtml = docType + iframeDoc.documentElement.outerHTML;
            
            // 使用渲染后的HTML替代原始HTML
            html = renderedHtml;
            
            console.log('已捕获iframe中的实际渲染内容，长度:', renderedHtml.length);
          }
        } catch (e) {
          console.error('从iframe获取渲染内容失败，将使用原始HTML:', e);
        }
      }
      
      // 准备HTML内容数组
      const htmlContents = Array.isArray(html) ? html : [html];
      console.log(`准备导出${htmlContents.length}个HTML内容为PDF (${pdfFormat}格式)`);

      // 使用AICourseUtils中的htmlToPdf函数
      // 为了兼容现有逻辑，需要使用一个临时的sessionId
      const tempSessionId = sessionId || new Date().getTime().toString();
      
      try {
        // 传递用户选择的PDF格式
        console.log(htmlContents,'htmlContents---');
        const { url } = await htmlToPdfLegacy(htmlContents, tempSessionId, pdfFormat as 'PPT' | 'A4',"Chat");
        console.log('PDF生成成功:', url);
        
        if (!url) {
          throw new Error('生成的PDF URL为空');
        }

        // 创建一个文件名
        const fileName = customFilename || 
          (sessionId ? `pdf-${sessionId.substring(0, 8)}.pdf` : `pdf-${new Date().getTime()}.pdf`);
        
        // 创建下载链接并自动触发下载
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = fileName; // 指定下载的文件名
        downloadLink.target = '_blank'; // 保持原有的行为，但确保使用download属性
        document.body.appendChild(downloadLink);
        downloadLink.click(); // 触发下载
        document.body.removeChild(downloadLink);
        
        notification.success({
          message: 'PDF生成成功',
          description: `PDF已生成并开始下载 (${pdfFormat}格式)`
        });
      } catch (err) {
        console.error('调用htmlToPdf失败:', err);
        throw new Error('PDF生成失败: ' + (err instanceof Error ? err.message : String(err)));
      }
    } catch (error) {
      console.error('PDF导出错误:', error);
      throw error;
    }
  };

  return (
    <>
      {showButton && (
        <Button
          type="text"
          icon={<FilePdfOutlined />}
          onClick={handleExportClick}
          disabled={disabled}
          title="导出为PDF文档"
        />
      )}

      <Modal
        title="导出为PDF文档"
        open={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={isExporting}
            onClick={handleExport}
            icon={<FilePdfOutlined />}
          >
            开始导出
          </Button>
        ]}
        width={500}
        styles={{ body: { padding: '16px' } }}
      >
        <Spin spinning={isExporting} tip="正在生成PDF，请稍候...这可能需要几秒钟时间，特别是对于包含图表的内容">
          <Alert
            message="导出HTML为PDF文档"
            description="此功能将HTML内容导出为PDF，可选择不同的页面格式。每个HTML将作为单独的一页，多个内容将自动合并为一个PDF文件。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          {!allHtmlMode && codeBlocks.length > 1 && (
            <div style={{ marginBottom: 16 }}>
              <Space align="center">
                <Text strong>导出模式:</Text>
                <Switch
                  checked={exportAllHtml}
                  onChange={setExportAllHtml}
                  checkedChildren="所有HTML"
                  unCheckedChildren="当前HTML"
                />
                <SafeTooltip title="开启后将所有HTML内容作为多页PDF导出，每个HTML作为单独的一页">
                  <InfoCircleOutlined style={{ cursor: 'pointer' }} />
                </SafeTooltip>
              </Space>
            </div>
          )}

          <div style={{ marginBottom: 16 }}>
            <Text strong>输出格式：</Text>
            <RadioGroup
              value={pdfFormat}
              onChange={(e) => setPdfFormat(e.target.value)}
              style={{ marginLeft: 8 }}
            >
              <RadioButton value="PPT">PPT (16:9幻灯片)</RadioButton>
              <RadioButton value="A4">A4纸张</RadioButton>
            </RadioGroup>
            <div style={{ marginTop: 4, fontSize: '12px', color: '#888' }}>
              PPT格式适合演示文稿，A4格式适合打印文档
            </div>
          </div>

          <div style={{ marginBottom: 16 }}>
            <Button
              type="link"
              icon={<SettingOutlined />}
              onClick={() => setShowAdvanced(!showAdvanced)}
              style={{ padding: 0 }}
            >
              {showAdvanced ? '隐藏高级选项' : '显示高级选项'}
            </Button>
          </div>

          {showAdvanced && (
            <div style={{ marginBottom: 16, backgroundColor: '#f9f9f9', padding: 12, borderRadius: 6 }}>
              <div>
                <Text strong>
                  渲染质量：
                  <SafeTooltip title="更高的质量将产生更清晰的PDF，但需要更多的处理时间和内存">
                    <InfoCircleOutlined style={{ marginLeft: 8, cursor: 'pointer' }} />
                  </SafeTooltip>
                </Text>
                <Select
                  value={quality}
                  onChange={(value: number) => setQuality(value)}
                  style={{ width: 120, marginLeft: 8 }}
                >
                  <Option value={1}>标准</Option>
                  <Option value={2}>高清 (推荐)</Option>
                  <Option value={3}>超高清</Option>
                </Select>
              </div>
            </div>
          )}
        </Spin>
      </Modal>
    </>
  );
};

export default ApiHtmlToPdfExporter;
