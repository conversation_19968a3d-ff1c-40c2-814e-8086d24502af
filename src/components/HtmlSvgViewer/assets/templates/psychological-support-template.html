<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>乳腺癌治疗期间的心理支持与康复管理</title>
  <style>
    /* 基础样式 */
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      width: 100%;
    }
    
    /* 主容器 */
    .slide-container {
      width: 1280px;
      min-height: 720px;
      margin: 0 auto;
      padding: 40px;
      box-sizing: border-box;
      background-color: white;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      position: relative;
    }
    
    /* 左侧区域 - 标题和图表 */
    .left-section {
      width: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      padding-right: 40px;
    }
    
    /* 标题样式 */
    .title {
      font-size: 28px;
      font-weight: bold;
      color: #2c3e50;
      margin: 0 0 30px 0;
      line-height: 1.4;
      text-align: left;
    }
    
    /* 图表容器 */
    .chart-container {
      width: 100%;
      height: 450px;
      margin: 20px 0;
      position: relative;
    }
    
    /* 右侧区域 - 卡片 */
    .right-section {
      width: 50%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-around;
      align-items: flex-start;
      gap: 20px;
    }
    
    /* 卡片样式 */
    .card {
      width: calc(33.333% - 20px);
      min-width: 150px;
      padding: 20px 15px;
      box-shadow: 0 3px 6px rgba(0,0,0,0.1);
      border-radius: 8px;
      background-color: #f9f9f9;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      flex: 1;
    }
    
    /* 卡片图标 */
    .card-icon {
      width: 60px;
      height: 60px;
      margin: 0 auto 15px auto;
      object-fit: contain;
    }
    
    /* 卡片标题 */
    .card-title {
      font-size: 18px;
      font-weight: bold;
      margin: 10px 0 5px 0;
      color: #2c3e50;
      text-align: center;
    }
    
    /* 卡片内容 */
    .card-content {
      font-size: 14px;
      color: #666;
      margin: 5px 0 0 0;
      text-align: center;
      line-height: 1.4;
    }
    
    /* 数据来源 */
    .data-source {
      position: absolute;
      bottom: 20px;
      left: 20px;
      font-size: 12px;
      color: #999;
    }
  </style>
</head>
<body>
  <div class="slide-container">
    <!-- 左侧区域 - 标题和图表 -->
    <div class="left-section">
      <h2 class="title">乳腺癌治疗期间的心理支持与康复管理</h2>
      <div class="chart-container" id="supportChart">
        <!-- 图表将通过JavaScript动态渲染 -->
      </div>
    </div>
    
    <!-- 右侧区域 - 卡片 -->
    <div class="right-section">
      <!-- 情绪疏导卡片 -->
      <div class="card">
        <img src="data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" class="card-icon" alt="情绪疏导图标">
        <h3 class="card-title">情绪疏导</h3>
        <p class="card-content">专业心理咨询和恐惧焦虑管理</p>
      </div>
      
      <!-- 社会支持卡片 -->
      <div class="card">
        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzM0OThkYiI+PHBhdGggZD0iTTE2IDExYzEuNjYgMCAyLjk5LTEuMzQgMi45OS0zUzE3LjY2IDUgMTYgNWMtMS42NiAwLTMgMS4zNC0zIDNzMS4zNCAzIDMgM3ptLTggMGMxLjY2IDAgMi45OS0xLjM0IDIuOTktM1M5LjY2IDUgOCA1QzYuMzQgNSA1IDYuMzQgNSA4czEuMzQgMyAzIDN6bTAgMmMtMi4zMyAwLTcgMS4xNy03IDMuNVYxOWgxNHYtMi41YzAtMi4zMy00LjY3LTMuNS03LTMuNXptOCAwYy0uMjkgMC0uNjIuMDItLjk3LjA1IDEuMTYuODQgMS45NyAxLjk3IDEuOTcgMy40NVYxOWg2di0yLjVjMC0yLjMzLTQuNjctMy41LTctMy41eiIvPjwvc3ZnPg==" class="card-icon" alt="社会支持图标">
        <h3 class="card-title">社会支持</h3>
        <p class="card-content">家人全程陪伴和病友互助小组</p>
      </div>
      
      <!-- 环境优化卡片 -->
      <div class="card">
        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzM0OThkYiI+PHBhdGggZD0iTTIwIDRINGMtMS4xMSAwLTEuOTkuODktMS45OSAyTDIgMThjMCAxLjExLjg5IDIgMiAyaDE2YzEuMTEgMCAyLS44OSAyLTJWNmMwLTEuMTEtLjg5LTItMi0yem0wIDE0SDRWOGgxNnYxMHpNOCAxM2g4di0ySDh2MnptMCA0aDYgdi0ySDh2MnoiLz48L3N2Zz4=" class="card-icon" alt="环境优化图标">
        <h3 class="card-title">环境优化</h3>
        <p class="card-content">舒适居住空间和就医环境优化</p>
      </div>
    </div>
    
    <!-- 数据来源 -->
    <div class="data-source">
      数据来源：《为什么要重视乳腺癌患者的心理需求》
    </div>
  </div>

  <!-- 图表脚本 -->
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化图表
      var chartDom = document.getElementById('supportChart');
      var myChart = echarts.init(chartDom);
      
      // 图表配置
      var option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center',
          textStyle: {
            color: '#666'
          }
        },
        series: [
          {
            name: '心理压力源',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c}%'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: [
              { value: 35, name: '乳房外观改变', itemStyle: { color: '#4e97f8' } },
              { value: 40, name: '乳腺癌患者主要心理压力源', itemStyle: { color: '#73bbff' } },
              { value: 25, name: '恶性肿瘤诊断', itemStyle: { color: '#b3d7ff' } }
            ]
          }
        ]
      };
      
      // 设置图表配置
      myChart.setOption(option);
      
      // 响应窗口大小变化
      window.addEventListener('resize', function() {
        myChart.resize();
      });
    });
  </script>
</body>
</html>
