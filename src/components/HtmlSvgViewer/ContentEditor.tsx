// import React, { useState, useEffect } from 'react';
// import { HtmlEditor } from './index';
// import { SvgEditor } from './index';
// import { message } from 'antd';

// interface ContentEditorProps {
//   content: string;
//   onSave: (newContent: string) => void;
//   onCancel: () => void;
//   visible: boolean;
// }

// /**
//  * 内容编辑器组件
//  * 根据内容类型（HTML或SVG）选择合适的编辑器组件
//  */
// const ContentEditor: React.FC<ContentEditorProps> = ({
//   content,
//   onSave,
//   onCancel,
//   visible
// }) => {
//   // 内容类型状态
//   const [contentType, setContentType] = useState<'html' | 'svg'>('html');

//   // 检测内容类型
//   useEffect(() => {
//     // 判断内容是否为SVG
//     const isSvg = detectSvgContent(content);
//     setContentType(isSvg ? 'svg' : 'html');

//     // 记录内容类型到控制台，方便调试
//     console.log('Content type detected:', isSvg ? 'SVG' : 'HTML');
//     console.log('Content preview:', content.substring(0, 100) + (content.length > 100 ? '...' : ''));
//   }, [content, visible]);

//   // 检测内容是否为SVG
//   const detectSvgContent = (content: string): boolean => {
//     // 简单检测是否包含SVG标签
//     const svgPattern = /<svg[^>]*>|<\/svg>/i;

//     // 如果内容包含SVG标签，且不是嵌入在HTML中的SVG（即不是完整的HTML文档）
//     const hasSvgTags = svgPattern.test(content);
//     const hasHtmlDoctype = /<!DOCTYPE html>|<html[^>]*>|<\/html>/i.test(content);

//     // 如果有SVG标签但没有HTML文档标记，或者内容以<svg开头，则认为是SVG内容
//     return (hasSvgTags && !hasHtmlDoctype) || content.trim().toLowerCase().startsWith('<svg');
//   };

//   // 处理保存操作
//   const handleSave = (newContent: string) => {
//     try {
//       // 记录保存的内容
//       console.log('Saving content:', newContent.substring(0, 100) + (newContent.length > 100 ? '...' : ''));
//       onSave(newContent);
//       message.success(`${contentType === 'html' ? 'HTML' : 'SVG'} 内容已保存`);
//     } catch (error) {
//       console.error('保存内容失败:', error);
//       message.error('保存内容失败');
//     }
//   };

//   // 根据内容类型渲染相应的编辑器
//   return contentType === 'html' ? (
//     <HtmlEditor
//       content={content || ''}
//       onSave={handleSave}
//       onCancel={onCancel}
//       visible={visible}
//     />
//   ) : (
//     <SvgEditor
//       content={content || ''}
//       onSave={handleSave}
//       onCancel={onCancel}
//       visible={visible}
//     />
//   );
// };

// export default ContentEditor;
