import React, { useState, useEffect, useRef } from 'react';
import { Button, Tabs, Space, Modal, message, Input, Form, Select, Slider, ColorPicker, Radio, Divider } from 'antd';
import { SaveOutlined, EyeOutlined, UndoOutlined, RedoOutlined, CodeOutlined, EditOutlined } from '@ant-design/icons';
import styles from './Editor.module.css';
import { WorkflowGraphData } from '../../utils/WorkflowCodeBlockProcessor';
import { updateData } from '../api/api';
import { WORKFLOW_GRAPH_ENDPOINT } from '../../Constant/RouterConstant';
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface HtmlEditorProps {
  workflowGraphList: WorkflowGraphData[];
  onSave: (updatedWorkflowGraphList: WorkflowGraphData[]) => void;
  onCancel: () => void;
  visible: boolean;
  onEditSvg?: (svgContent: string, elementPath: string) => void; // 可选的SVG编辑回调
  selectedWorkflowIndex?: number; // 当前选择的WorkflowGraph索引
  selectedCodeBlockIndex?: number; // 当前选择的代码块索引
}

/**
 * HTML编辑器组件
 * 提供HTML内容的编辑功能，可以修改内容、样式和布局
 */
const HtmlEditor: React.FC<HtmlEditorProps> = ({
  workflowGraphList,
  onSave,
  onCancel,
  visible,
  onEditSvg,
  selectedWorkflowIndex = 0,
  selectedCodeBlockIndex = 0
}) => {
  // 编辑模式：可视化编辑或源代码编辑
  const [editMode, setEditMode] = useState<'visual' | 'source'>('visual');

  // 当前选择的WorkflowGraph和CodeBlock
  const [currentWorkflowIndex, setCurrentWorkflowIndex] = useState<number>(selectedWorkflowIndex);
  const [currentCodeBlockIndex, setCurrentCodeBlockIndex] = useState<number>(selectedCodeBlockIndex);

  // 获取当前编辑的代码块内容
  const getCurrentCodeBlockContent = () => {
    if (workflowGraphList.length === 0 || 
        currentWorkflowIndex >= workflowGraphList.length ||
        !workflowGraphList[currentWorkflowIndex].code_blocks ||
        currentCodeBlockIndex >= workflowGraphList[currentWorkflowIndex].code_blocks.length) {
      return '';
    }
    return workflowGraphList[currentWorkflowIndex].code_blocks[currentCodeBlockIndex]?.code || '';
  };

  // HTML内容
  const [htmlContent, setHtmlContent] = useState<string>(getCurrentCodeBlockContent());

  // 预览模式
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);

  // 编辑历史
  const [history, setHistory] = useState<string[]>([getCurrentCodeBlockContent()]);
  const [historyIndex, setHistoryIndex] = useState<number>(0);

  // iframe引用
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 当前选中的元素
  const [selectedElement, setSelectedElement] = useState<{
    element: HTMLElement | null;
    path: string;
    styles: Record<string, string>;
    content: string;
    isSvg?: boolean; // 标记是否为SVG元素
    tagName?: string; // 元素标签名
  } | null>(null);

  // 样式编辑状态
  const [textColor, setTextColor] = useState<string>('#000000');
  const [backgroundColor, setBackgroundColor] = useState<string>('transparent');
  const [fontSize, setFontSize] = useState<number>(16);
  const [fontWeight, setFontWeight] = useState<string>('normal');
  const [textAlign, setTextAlign] = useState<string>('left');
  const [padding, setPadding] = useState<number>(0);
  const [margin, setMargin] = useState<number>(0);
  const [borderRadius, setBorderRadius] = useState<number>(0);
  const [elementContent, setElementContent] = useState<string>('');

  // 初始化编辑内容
  useEffect(() => {
    if (visible) {
      // 确保编辑模式为可视化编辑
      setEditMode('visual');

      // 获取当前编辑的内容
      const currentContent = getCurrentCodeBlockContent();
      
      // 如果有内容，使用提供的内容
      if (currentContent && currentContent.trim() !== '') {
        setHtmlContent(currentContent);
        setHistory([currentContent]);
        setHistoryIndex(0);
      } else {
        // 如果没有内容，提供默认的HTML模板
        const defaultContent = `
<div style="padding: 20px; font-family: Arial, sans-serif;">
  <h1 style="color: #333;">HTML 内容</h1>
  <p style="font-size: 16px; line-height: 1.5;">
    这是一个示例内容，您可以开始编辑这段文本。
  </p>
  <div style="background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-top: 20px;">
    <h3 style="color: #0066cc;">编辑说明</h3>
    <ul>
      <li>点击任意文本或元素进行选择</li>
      <li>在右侧面板修改内容和样式</li>
      <li>使用源代码模式直接编辑HTML</li>
    </ul>
  </div>
</div>
        `;
        setHtmlContent(defaultContent);
        setHistory([defaultContent]);
        setHistoryIndex(0);
      }

      // 确保iframe在下一个渲染周期后更新
      setTimeout(() => {
        console.log('Initializing visual editor content');
        if (iframeRef.current) {
          updateVisualEditor();
        }
      }, 500);
    }
  }, [visible, currentWorkflowIndex, currentCodeBlockIndex]); // updateVisualEditor 是组件内部函数，不需要添加到依赖数组

  // 当HTML内容变化时更新预览
  useEffect(() => {
    if (editMode === 'visual') {
      updateVisualEditor();
    }
  }, [htmlContent, editMode]); // updateVisualEditor 是组件内部函数，不需要添加到依赖数组

  // 组件挂载时立即更新预览
  useEffect(() => {
    if (visible && editMode === 'visual') {
      // 使用setTimeout确保DOM已经渲染，iframe已经挂载
      setTimeout(() => {
        updateVisualEditor();
        console.log('Visual editor updated on modal open');
      }, 300);
    }
  }, [visible, editMode]); // updateVisualEditor 是组件内部函数，不需要添加到依赖数组

  // 更新可视化编辑器
  const updateVisualEditor = () => {
    if (!iframeRef.current) return;

    const iframe = iframeRef.current;

    try {
      // 创建完整的HTML文档
      const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      margin: 0;
      padding: 10px;
      font-family: Arial, sans-serif;
    }

    .element-highlight {
      outline: 2px dashed #1890ff;
      position: relative;
    }

    /* 编辑控件容器 */
    .edit-controls {
      position: absolute;
      top: -30px;
      left: 0;
      display: flex;
      background: #1890ff;
      border-radius: 4px 4px 0 0;
      z-index: 1001;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    /* 拖动手柄样式 */
    .drag-handle {
      padding: 4px 8px;
      color: white;
      font-size: 12px;
      cursor: move;
      user-select: none;
      display: flex;
      align-items: center;
    }

    .drag-handle::before {
      content: "⋮⋮";
      margin-right: 4px;
      font-size: 14px;
    }

    /* 调整大小手柄 */
    .resize-handle {
      padding: 4px 8px;
      color: white;
      font-size: 12px;
      cursor: se-resize;
      user-select: none;
      display: flex;
      align-items: center;
      border-left: 1px solid rgba(255,255,255,0.3);
    }

    .resize-handle::before {
      content: "⤡";
      margin-right: 4px;
      font-size: 14px;
    }

    /* 可拖动元素样式 */
    .draggable {
      cursor: move;
    }

    /* 拖动时的样式 */
    .dragging {
      opacity: 0.8;
      outline: 2px solid #1890ff !important;
      box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
      position: relative;
      z-index: 1000;
    }

    /* 调整大小时的样式 */
    .resizing {
      outline: 2px solid #1890ff !important;
      box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
      position: relative;
      z-index: 1000;
    }

    /* 调整大小指示器 */
    .resize-indicator {
      position: absolute;
      width: 10px;
      height: 10px;
      background-color: #1890ff;
      border: 1px solid white;
      border-radius: 50%;
      bottom: -5px;
      right: -5px;
      cursor: se-resize;
      z-index: 1001;
    }

    .element-path {
      position: fixed;
      bottom: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 1000;
    }

    * {
      cursor: pointer;
    }

    *:hover {
      outline: 1px dashed rgba(24, 144, 255, 0.5);
    }
  </style>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 元素路径显示
      let pathDisplay = document.createElement('div');
      pathDisplay.className = 'element-path';
      pathDisplay.style.display = 'none';
      document.body.appendChild(pathDisplay);

      // 获取元素路径
      function getElementPath(element) {
        let path = [];
        let currentElement = element;

        while (currentElement && currentElement !== document.body) {
          let tag = currentElement.tagName.toLowerCase();
          let id = currentElement.id ? '#' + currentElement.id : '';
          let classes = Array.from(currentElement.classList)
            .filter(cls => cls !== 'element-highlight')
            .map(cls => '.' + cls)
            .join('');

          path.unshift(tag + id + classes);
          currentElement = currentElement.parentElement;
        }

        return path.join(' > ');
      }

      // 为所有元素添加点击事件
      function makeElementsEditable() {
        const allElements = document.querySelectorAll('body *');

        allElements.forEach(el => {
          // 鼠标悬停显示路径
          el.addEventListener('mouseover', (e) => {
            e.stopPropagation();
            pathDisplay.textContent = getElementPath(el);
            pathDisplay.style.display = 'block';
          });

          el.addEventListener('mouseout', () => {
            pathDisplay.style.display = 'none';
          });

          // 点击选择元素
          el.addEventListener('click', (e) => {
            e.stopPropagation();
            e.preventDefault();

            // 移除其他元素的高亮
            document.querySelectorAll('.element-highlight').forEach(highlighted => {
              highlighted.classList.remove('element-highlight');
            });

            // 添加高亮
            el.classList.add('element-highlight');

            // 为选中的元素添加交互功能（拖拽和调整大小）
            addInteractionsToSelectedElement(el);

            // 获取计算样式
            const computedStyle = window.getComputedStyle(el);
            const styles = {
              color: computedStyle.color,
              backgroundColor: computedStyle.backgroundColor,
              fontSize: computedStyle.fontSize,
              fontWeight: computedStyle.fontWeight,
              textAlign: computedStyle.textAlign,
              padding: computedStyle.padding,
              margin: computedStyle.margin,
              borderRadius: computedStyle.borderRadius,
              display: computedStyle.display,
              position: computedStyle.position,
              width: computedStyle.width,
              height: computedStyle.height
            };

            // 检查是否为SVG元素或SVG内部元素
            const isSvgElement = el.tagName.toLowerCase() === 'svg' ||
                               el.closest('svg') !== null ||
                               el.namespaceURI === 'http://www.w3.org/2000/svg';

            // 获取SVG特有属性
            const svgAttributes = {};
            if (isSvgElement) {
              ['fill', 'stroke', 'stroke-width', 'opacity', 'cx', 'cy', 'r', 'd', 'points', 'viewBox'].forEach(attr => {
                if (el.hasAttribute(attr)) {
                  svgAttributes[attr] = el.getAttribute(attr);
                }
              });
            }

            // 通知父窗口
            window.parent.postMessage({
              type: 'elementSelected',
              path: getElementPath(el),
              tagName: el.tagName,
              content: el.innerHTML,
              text: el.innerText || el.textContent,
              styles: styles,
              isSvgElement: isSvgElement,
              svgAttributes: svgAttributes,
              namespaceURI: el.namespaceURI,
              attributes: Array.from(el.attributes).reduce((obj, attr) => {
                obj[attr.name] = attr.value;
                return obj;
              }, {})
            }, '*');
          });
        });

        // 点击body时清除选择
        document.body.addEventListener('click', (e) => {
          if (e.target === document.body) {
            document.querySelectorAll('.element-highlight').forEach(highlighted => {
              highlighted.classList.remove('element-highlight');
            });

            window.parent.postMessage({
              type: 'elementSelected',
              path: 'body',
              tagName: 'BODY',
              content: document.body.innerHTML,
              text: document.body.innerText,
              styles: {},
              isSvgElement: false,
              svgAttributes: {},
              attributes: {}
            }, '*');
          }
        });
      }

      // 拖拽和调整大小相关变量
      let activeElement = null;
      let isDragging = false;
      let isResizing = false;
      let startX = 0;
      let startY = 0;
      let startLeft = 0;
      let startTop = 0;
      let startWidth = 0;
      let startHeight = 0;

      // 添加编辑控件到元素
      function addEditControlsToElement(element) {
        // 检查元素是否可以交互（只有div, section, article等容器元素可交互）
        const interactiveTags = ['DIV', 'SECTION', 'ARTICLE', 'ASIDE', 'HEADER', 'FOOTER', 'MAIN', 'NAV', 'FIGURE'];
        if (!interactiveTags.includes(element.tagName)) {
          console.log('元素不可交互:', element.tagName);
          return;
        }

        // 移除所有现有的编辑控件
        document.querySelectorAll('.edit-controls, .resize-indicator').forEach(control => {
          control.remove();
        });

        // 创建编辑控件容器
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'edit-controls';

        // 创建拖动手柄
        const dragHandle = document.createElement('div');
        dragHandle.className = 'drag-handle';
        dragHandle.textContent = '拖动';
        controlsContainer.appendChild(dragHandle);

        // 创建调整大小手柄
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        resizeHandle.textContent = '调整大小';
        controlsContainer.appendChild(resizeHandle);

        // 添加控件到元素
        element.appendChild(controlsContainer);

        // 创建右下角调整大小指示器
        const resizeIndicator = document.createElement('div');
        resizeIndicator.className = 'resize-indicator';
        element.appendChild(resizeIndicator);

        // 确保元素可以定位
        const computedStyle = window.getComputedStyle(element);
        if (computedStyle.position === 'static') {
          element.style.position = 'relative';
        }

        // 添加拖动功能
        dragHandle.onmousedown = function(e) {
          e.preventDefault();
          e.stopPropagation();

          // 设置活动元素
          activeElement = element;
          isDragging = true;

          // 记录起始位置
          startX = e.clientX;
          startY = e.clientY;

          // 获取元素当前位置
          const rect = element.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(element);

          // 解析当前位置
          startLeft = parseInt(computedStyle.left) || 0;
          startTop = parseInt(computedStyle.top) || 0;

          // 添加拖动样式
          element.classList.add('dragging');

          // 通知父窗口拖动开始
          window.parent.postMessage({
            type: 'dragStart',
            path: getElementPath(element)
          }, '*');

          // 添加全局鼠标事件
          document.addEventListener('mousemove', handleDragMove);
          document.addEventListener('mouseup', handleDragEnd);

          console.log('拖动开始:', { startX, startY, startLeft, startTop });
        };

        // 添加调整大小功能
        function setupResizeHandler(handle) {
          handle.onmousedown = function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 设置活动元素
            activeElement = element;
            isResizing = true;

            // 记录起始位置和尺寸
            startX = e.clientX;
            startY = e.clientY;
            startWidth = element.offsetWidth;
            startHeight = element.offsetHeight;

            // 添加调整大小样式
            element.classList.add('resizing');

            // 通知父窗口调整大小开始
            window.parent.postMessage({
              type: 'resizeStart',
              path: getElementPath(element)
            }, '*');

            // 添加全局鼠标事件
            document.addEventListener('mousemove', handleResizeMove);
            document.addEventListener('mouseup', handleResizeEnd);

            console.log('调整大小开始:', { startX, startY, startWidth, startHeight });
          };
        }

        // 为两个调整大小控件设置事件处理
        setupResizeHandler(resizeHandle);
        setupResizeHandler(resizeIndicator);
      }

      // 处理拖动移动
      function handleDragMove(e) {
        if (!isDragging || !activeElement) return;

        // 计算移动距离
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;

        // 更新元素位置
        activeElement.style.left = (startLeft + deltaX) + 'px';
        activeElement.style.top = (startTop + deltaY) + 'px';

        // 防止事件冒泡
        e.preventDefault();
        e.stopPropagation();
      }

      // 处理拖动结束
      function handleDragEnd(e) {
        if (!isDragging || !activeElement) return;

        // 移除拖动样式
        activeElement.classList.remove('dragging');

        // 获取最终位置
        const finalPosition = {
          left: activeElement.style.left,
          top: activeElement.style.top
        };

        // 通知父窗口拖动结束
        window.parent.postMessage({
          type: 'dragEnd',
          path: getElementPath(activeElement),
          position: finalPosition
        }, '*');

        console.log('拖动结束:', finalPosition);

        // 清理
        isDragging = false;

        // 移除全局事件监听
        document.removeEventListener('mousemove', handleDragMove);
        document.removeEventListener('mouseup', handleDragEnd);
      }

      // 处理调整大小移动
      function handleResizeMove(e) {
        if (!isResizing || !activeElement) return;

        // 计算新尺寸
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;
        const newWidth = Math.max(50, startWidth + deltaX);
        const newHeight = Math.max(50, startHeight + deltaY);

        // 更新元素尺寸
        activeElement.style.width = newWidth + 'px';
        activeElement.style.height = newHeight + 'px';

        // 防止事件冒泡
        e.preventDefault();
        e.stopPropagation();
      }

      // 处理调整大小结束
      function handleResizeEnd(e) {
        if (!isResizing || !activeElement) return;

        // 移除调整大小样式
        activeElement.classList.remove('resizing');

        // 获取最终尺寸
        const finalSize = {
          width: activeElement.style.width,
          height: activeElement.style.height
        };

        // 通知父窗口调整大小结束
        window.parent.postMessage({
          type: 'resizeEnd',
          path: getElementPath(activeElement),
          size: finalSize
        }, '*');

        console.log('调整大小结束:', finalSize);

        // 清理
        isResizing = false;

        // 移除全局事件监听
        document.removeEventListener('mousemove', handleResizeMove);
        document.removeEventListener('mouseup', handleResizeEnd);
      }

      // 为选中的元素添加编辑控件
      function addInteractionsToSelectedElement(element) {
        if (!element) return;

        console.log('为元素添加交互功能:', element.tagName, getElementPath(element));

        // 添加编辑控件
        addEditControlsToElement(element);
      }

      // 初始化编辑功能
      makeElementsEditable();

      // 5秒后隐藏提示
      setTimeout(() => {
        infoBox.style.opacity = '0';
        infoBox.style.transition = 'opacity 0.5s';
        setTimeout(() => infoBox.remove(), 500);
      }, 5000);
    });
  </script>
</head>
<body>
  ${htmlContent}
</body>
</html>
      `;

      // 使用srcdoc属性设置iframe内容
      iframe.srcdoc = fullHtml;

    } catch (error) {
      console.error('更新可视化编辑器失败:', error);
      message.error('更新可视化编辑器失败');
    }
  };

  // 监听iframe消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 处理元素选中消息
      if (event.data && event.data.type === 'elementSelected') {
        console.log('选中元素:', event.data);

        // 检查是否为SVG元素
        const tagName = event.data.tagName || '';
        const isSvg = tagName.toLowerCase() === 'svg' ||
                     (event.data.path && event.data.path.includes(' > svg')) ||
                     tagName.toLowerCase().startsWith('svg:');

        console.log('元素类型:', { tagName, isSvg });

        // 更新选中元素状态
        setSelectedElement({
          element: null, // 无法直接传递DOM元素
          path: event.data.path,
          styles: event.data.styles || {},
          content: event.data.content || '',
          isSvg: isSvg,
          tagName: tagName
        });

        // 更新样式编辑状态
        const styles = event.data.styles || {};
        setTextColor(styles.color || '#000000');
        setBackgroundColor(styles.backgroundColor || 'transparent');
        setFontSize(parseInt(styles.fontSize) || 16);
        setFontWeight(styles.fontWeight || 'normal');
        setTextAlign(styles.textAlign || 'left');
        setPadding(parseInt(styles.padding) || 0);
        setMargin(parseInt(styles.margin) || 0);
        setBorderRadius(parseInt(styles.borderRadius) || 0);

        // 更新内容编辑状态
        setElementContent(event.data.content || '');
      }
      // 处理拖动开始消息
      else if (event.data && event.data.type === 'dragStart') {
        console.log('拖动开始:', event.data);
        // 可以在这里添加拖动开始的处理逻辑
      }
      // 处理拖动结束消息
      else if (event.data && event.data.type === 'dragEnd') {
        console.log('拖动结束:', event.data);

        // 更新HTML内容
        updateIframeContent('元素位置已更新');
      }
      // 处理调整大小开始消息
      else if (event.data && event.data.type === 'resizeStart') {
        console.log('调整大小开始:', event.data);
        // 可以在这里添加调整大小开始的处理逻辑
      }
      // 处理调整大小结束消息
      else if (event.data && event.data.type === 'resizeEnd') {
        console.log('调整大小结束:', event.data);

        // 更新HTML内容
        updateIframeContent('元素大小已更新');
      }
    };

    // 辅助函数：更新iframe内容
    const updateIframeContent = (successMessage: string) => {
      if (iframeRef.current) {
        const iframe = iframeRef.current;
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

        if (iframeDoc) {
          // 更新HTML内容
          setHtmlContent(iframeDoc.body.innerHTML);

          // 添加到历史记录
          addToHistory(iframeDoc.body.innerHTML);

          // 显示成功消息
          message.success(successMessage);
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // 更新元素内容
  const updateElementContent = () => {
    if (!selectedElement || !iframeRef.current) return;

    try {
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) return;

      // 查找选中的元素
      const selectedDomElement = iframeDoc.querySelector('.element-highlight');
      if (selectedDomElement) {
        // 更新内容
        selectedDomElement.innerHTML = elementContent;

        // 更新HTML内容
        setHtmlContent(iframeDoc.body.innerHTML);

        // 添加到历史记录
        addToHistory(iframeDoc.body.innerHTML);

        message.success('内容已更新');
      }
    } catch (error) {
      console.error('更新元素内容失败:', error);
      message.error('更新元素内容失败');
    }
  };

  // 更新元素样式
  const updateElementStyle = (property: string, value: string | number) => {
    if (!selectedElement || !iframeRef.current) return;

    try {
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) return;

      // 查找选中的元素
      const selectedDomElement = iframeDoc.querySelector('.element-highlight') as HTMLElement;
      if (selectedDomElement) {
        // 检查是否为SVG元素
        const isSvgElement = selectedElement.isSvg;

        if (isSvgElement) {
          // 对于SVG元素，使用setAttribute方法设置属性
          // SVG属性通常使用连字符格式，如stroke-width，而不是驼峰格式
          selectedDomElement.setAttribute(property, value.toString());
          console.log(`更新SVG属性: ${property} = ${value}`);
        } else {
          // 对于普通HTML元素，使用style属性
          selectedDomElement.style[property as any] = value.toString();
        }

        // 更新HTML内容
        setHtmlContent(iframeDoc.body.innerHTML);

        // 添加到历史记录
        addToHistory(iframeDoc.body.innerHTML);
      }
    } catch (error) {
      console.error('更新元素样式失败:', error);
      message.error('更新元素样式失败');
    }
  };

  // 添加到历史记录
  const addToHistory = (newContent: string) => {
    // 如果当前不是最新的历史记录，则删除之后的记录
    const newHistory = [...history.slice(0, historyIndex + 1), newContent];
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // 撤销操作
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setHtmlContent(history[historyIndex - 1]);
    }
  };

  // 重做操作
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setHtmlContent(history[historyIndex + 1]);
    }
  };

  // 切换编辑模式
  const toggleEditMode = () => {
    if (editMode === 'visual') {
      // 从可视化模式切换到源代码模式
      setEditMode('source');
    } else {
      // 从源代码模式切换到可视化模式
      setEditMode('visual');
      // 更新历史记录
      addToHistory(htmlContent);
    }
  };

  // 显示预览
  const showPreview = () => {
    setPreviewVisible(true);
  };

  // 关闭预览
  const closePreview = () => {
    setPreviewVisible(false);
  };

  // 保存编辑到后端API
  const handleSave = async () => {
    try {
      // 确保内容不为空
      if (!htmlContent || htmlContent.trim() === '') {
        message.warning('内容不能为空');
        return;
      }

      // 确保有有效的WorkflowGraph和CodeBlock
      if (workflowGraphList.length === 0 || 
          currentWorkflowIndex >= workflowGraphList.length ||
          !workflowGraphList[currentWorkflowIndex].code_blocks ||
          currentCodeBlockIndex >= workflowGraphList[currentWorkflowIndex].code_blocks.length) {
        message.error('未找到有效的代码块');
        return;
      }

      // 创建更新后的WorkflowGraphList副本
      const updatedWorkflowGraphList = [...workflowGraphList];
      const currentWorkflow = { ...updatedWorkflowGraphList[currentWorkflowIndex] };
      const updatedCodeBlocks = [...currentWorkflow.code_blocks];
      
      // 更新当前代码块的内容
      console.log(htmlContent,'htmlContent');
      updatedCodeBlocks[currentCodeBlockIndex] = {
        ...updatedCodeBlocks[currentCodeBlockIndex],
        code: htmlContent
      };
      
      currentWorkflow.code_blocks = updatedCodeBlocks;
      updatedWorkflowGraphList[currentWorkflowIndex] = currentWorkflow;

      // 调用API更新数据
      const updateEndpoint = `${WORKFLOW_GRAPH_ENDPOINT}/${currentWorkflow.id}`;
      const updatePayload = {
        code_blocks: updatedCodeBlocks
      };

      console.log('保存到API:', updateEndpoint, updatePayload);
      
      // 调用API更新
      const response = await updateData(updateEndpoint, updatePayload);
      
      if (response) {
        // 调用保存回调，传递更新后的列表
        onSave(updatedWorkflowGraphList);
        message.success('保存成功');
        console.log('WorkflowGraph更新成功:', response);
      } else {
        throw new Error('API响应为空');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 渲染WorkflowGraph和CodeBlock选择器
  const renderSelectors = () => {
    const currentWorkflow = workflowGraphList[currentWorkflowIndex];
    const codeBlocks = currentWorkflow?.code_blocks || [];

    return (
      <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0', backgroundColor: '#fafafa' }}>
        <Space size="large">
          <div>
            <label style={{ marginRight: 8, fontWeight: 'bold' }}>WorkflowGraph:</label>
            <Select
              value={currentWorkflowIndex}
              onChange={(value) => {
                setCurrentWorkflowIndex(value);
                setCurrentCodeBlockIndex(0); // 重置代码块索引
                const newContent = workflowGraphList[value]?.code_blocks?.[0]?.code || '';
                setHtmlContent(newContent);
                setHistory([newContent]);
                setHistoryIndex(0);
              }}
              style={{ minWidth: 200 }}
            >
              {workflowGraphList.map((workflow, index) => (
                <Option key={workflow.id} value={index}>
                  {workflow.name || `WorkflowGraph ${workflow.id}`} ({workflow.code_blocks?.length || 0} 代码块)
                </Option>
              ))}
            </Select>
          </div>

          {codeBlocks.length > 0 && (
            <div>
              <label style={{ marginRight: 8, fontWeight: 'bold' }}>代码块:</label>
              <Select
                value={currentCodeBlockIndex}
                onChange={(value) => {
                  setCurrentCodeBlockIndex(value);
                  const newContent = codeBlocks[value]?.code || '';
                  setHtmlContent(newContent);
                  setHistory([newContent]);
                  setHistoryIndex(0);
                }}
                style={{ minWidth: 200 }}
              >
                {codeBlocks.map((block, index) => (
                  <Option key={`${block.content_id}_${index}`} value={index}>
                    {block.node_name || `代码块 ${index + 1}`} ({block.language})
                  </Option>
                ))}
              </Select>
            </div>
          )}

          <div style={{ color: '#666', fontSize: '12px' }}>
            当前编辑: {currentWorkflow?.name || 'N/A'} → {codeBlocks[currentCodeBlockIndex]?.node_name || 'N/A'}
          </div>
        </Space>
      </div>
    );
  };

  // 渲染工具栏
  const renderToolbar = () => {
    return (
      <div className={styles.toolbar}>
        <Space>
          <Button
            icon={<UndoOutlined />}
            onClick={handleUndo}
            disabled={historyIndex <= 0}
            title="撤销"
          >
            撤销
          </Button>
          <Button
            icon={<RedoOutlined />}
            onClick={handleRedo}
            disabled={historyIndex >= history.length - 1}
            title="重做"
          >
            重做
          </Button>
          <Button
            icon={<EyeOutlined />}
            onClick={showPreview}
            title="预览"
          >
            预览
          </Button>
          <Button
            icon={editMode === 'visual' ? <CodeOutlined /> : <EditOutlined />}
            onClick={toggleEditMode}
            type={editMode === 'source' ? 'primary' : 'default'}
            title={editMode === 'visual' ? '源代码编辑' : '可视化编辑'}
          >
            {editMode === 'visual' ? '源代码编辑' : '可视化编辑'}
          </Button>
          <Button
            icon={<SaveOutlined />}
            type="primary"
            onClick={handleSave}
            title="保存"
          >
            保存
          </Button>
        </Space>

        <div style={{ marginLeft: 16, color: '#666' }}>
          {editMode === 'visual'
            ? '提示: 点击左侧预览区域中的任意元素进行编辑，然后在右侧面板中修改内容和样式'
            : '提示: 直接编辑HTML源代码，完成后点击"保存"按钮'}
        </div>
      </div>
    );
  };

  // 渲染源代码编辑器
  const renderSourceEditor = () => {
    return (
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', padding: '16px' }}>
        <TextArea
          value={htmlContent}
          onChange={(e) => setHtmlContent(e.target.value)}
          style={{
            flex: 1,
            minHeight: '500px',
            fontFamily: 'monospace',
            fontSize: '14px',
            lineHeight: '1.5',
            padding: '12px'
          }}
          placeholder="在此输入HTML代码..."
        />
      </div>
    );
  };

  // 渲染可视化编辑器
  const renderVisualEditor = () => {
    return (
      <>
        {/* 左侧预览区域 */}
        <div className={styles.previewPanel}>
          <iframe
            ref={iframeRef}
            className={styles.editorFrame}
            title="HTML编辑预览"
            sandbox="allow-same-origin allow-scripts"
          />
        </div>

        {/* 右侧属性面板 */}
        <div className={styles.propertiesPanel}>
          <Tabs defaultActiveKey="content">
            <TabPane tab="内容" key="content">
              <div className={styles.propertySection}>
                {selectedElement ? (
                  <Form layout="vertical">
                    <Form.Item label="HTML内容">
                      <TextArea
                        value={elementContent}
                        onChange={(e) => setElementContent(e.target.value)}
                        rows={6}
                        placeholder="输入HTML内容..."
                      />
                    </Form.Item>
                    <Form.Item>
                      <Button type="primary" onClick={updateElementContent}>
                        更新内容
                      </Button>
                    </Form.Item>
                    {selectedElement.path && (
                      <div style={{ marginTop: 16, fontSize: 12, color: '#666' }}>
                        <div>当前选中: {selectedElement.path}</div>
                        {selectedElement.isSvg && (
                          <div style={{ color: '#1890ff', marginTop: 4 }}>
                            这是一个SVG元素，您可以在"SVG属性"标签页中编辑SVG特有属性
                          </div>
                        )}
                      </div>
                    )}
                  </Form>
                ) : (
                  <div>
                    <p>请在左侧选择一个元素进行编辑</p>
                    <p style={{ marginTop: '16px', color: '#1890ff' }}>
                      提示: 如果无法选择元素，请点击上方的"源代码编辑"按钮，直接编辑HTML代码。
                    </p>
                  </div>
                )}
              </div>
            </TabPane>

            <TabPane tab="样式" key="style">
              <div className={styles.propertySection}>
                {selectedElement ? (
                  <Form layout="vertical">
                    <Form.Item label="文字颜色">
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ColorPicker
                          value={textColor}
                          onChange={(color) => {
                            const hexColor = color.toHexString();
                            setTextColor(hexColor);
                            updateElementStyle('color', hexColor);
                          }}
                        />
                        <span style={{ marginLeft: 8 }}>{textColor}</span>
                      </div>
                    </Form.Item>

                    <Form.Item label="背景颜色">
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ColorPicker
                          value={backgroundColor}
                          onChange={(color) => {
                            const hexColor = color.toHexString();
                            setBackgroundColor(hexColor);
                            updateElementStyle('backgroundColor', hexColor);
                          }}
                        />
                        <span style={{ marginLeft: 8 }}>{backgroundColor}</span>
                      </div>
                    </Form.Item>

                    <Form.Item label="字体大小">
                      <Slider
                        min={8}
                        max={72}
                        value={fontSize}
                        onChange={(value) => {
                          setFontSize(value);
                          updateElementStyle('fontSize', `${value}px`);
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{fontSize}px</div>
                    </Form.Item>

                    <Form.Item label="字体粗细">
                      <Select
                        value={fontWeight}
                        onChange={(value) => {
                          setFontWeight(value);
                          updateElementStyle('fontWeight', value);
                        }}
                        style={{ width: '100%' }}
                      >
                        <Option value="normal">正常</Option>
                        <Option value="bold">粗体</Option>
                        <Option value="lighter">细体</Option>
                        <Option value="bolder">更粗</Option>
                        <Option value="100">100</Option>
                        <Option value="200">200</Option>
                        <Option value="300">300</Option>
                        <Option value="400">400</Option>
                        <Option value="500">500</Option>
                        <Option value="600">600</Option>
                        <Option value="700">700</Option>
                        <Option value="800">800</Option>
                        <Option value="900">900</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item label="文本对齐">
                      <Radio.Group
                        value={textAlign}
                        onChange={(e) => {
                          setTextAlign(e.target.value);
                          updateElementStyle('textAlign', e.target.value);
                        }}
                      >
                        <Radio.Button value="left">左对齐</Radio.Button>
                        <Radio.Button value="center">居中</Radio.Button>
                        <Radio.Button value="right">右对齐</Radio.Button>
                        <Radio.Button value="justify">两端对齐</Radio.Button>
                      </Radio.Group>
                    </Form.Item>
                  </Form>
                ) : (
                  <div>
                    <p>请在左侧选择一个元素进行编辑</p>
                    <p style={{ marginTop: '16px', color: '#1890ff' }}>
                      提示: 如果无法选择元素，请点击上方的"源代码编辑"按钮，直接编辑HTML代码。
                    </p>
                  </div>
                )}
              </div>
            </TabPane>

            <TabPane tab="布局" key="layout">
              <div className={styles.propertySection}>
                {selectedElement ? (
                  <Form layout="vertical">
                    <Form.Item label="内边距 (padding)">
                      <Slider
                        min={0}
                        max={100}
                        value={padding}
                        onChange={(value) => {
                          setPadding(value);
                          updateElementStyle('padding', `${value}px`);
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{padding}px</div>
                    </Form.Item>

                    <Form.Item label="外边距 (margin)">
                      <Slider
                        min={0}
                        max={100}
                        value={margin}
                        onChange={(value) => {
                          setMargin(value);
                          updateElementStyle('margin', `${value}px`);
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{margin}px</div>
                    </Form.Item>

                    <Form.Item label="圆角 (border-radius)">
                      <Slider
                        min={0}
                        max={50}
                        value={borderRadius}
                        onChange={(value) => {
                          setBorderRadius(value);
                          updateElementStyle('borderRadius', `${value}px`);
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{borderRadius}px</div>
                    </Form.Item>

                    <Divider orientation="left">位置和大小控制</Divider>

                    <div style={{ marginBottom: '16px', padding: '8px', backgroundColor: '#f0f8ff', borderRadius: '4px' }}>
                      <p style={{ color: '#1890ff', margin: 0 }}>
                        <strong>提示：</strong> 您可以直接在预览区域拖动元素或调整其大小。
                      </p>
                      <ul style={{ color: '#666', marginTop: '8px', paddingLeft: '20px' }}>
                        <li>选中元素后，顶部会出现蓝色控制栏</li>
                        <li>点击并拖动"拖动"按钮可以移动元素</li>
                        <li>点击并拖动"调整大小"按钮或右下角蓝点可以改变元素大小</li>
                        <li>也可以通过下方的数值输入精确控制位置和大小</li>
                      </ul>
                    </div>

                    {/* 位置类型选择 */}
                    <Form.Item label="定位方式">
                      <Radio.Group
                        value={selectedElement.styles.position || 'static'}
                        onChange={(e) => {
                          updateElementStyle('position', e.target.value);
                        }}
                      >
                        <Radio.Button value="static">默认</Radio.Button>
                        <Radio.Button value="relative">相对定位</Radio.Button>
                        <Radio.Button value="absolute">绝对定位</Radio.Button>
                      </Radio.Group>
                    </Form.Item>

                    {/* 位置坐标控制 - 只在相对或绝对定位时显示 */}
                    {(selectedElement.styles.position === 'relative' ||
                      selectedElement.styles.position === 'absolute') && (
                      <>
                        <div style={{ display: 'flex', gap: '16px' }}>
                          <Form.Item label="左边距 (left)" style={{ flex: 1 }}>
                            <Input
                              type="number"
                              value={parseInt(selectedElement.styles.left) || 0}
                              onChange={(e) => {
                                updateElementStyle('left', `${e.target.value}px`);
                              }}
                              addonAfter="px"
                            />
                          </Form.Item>

                          <Form.Item label="上边距 (top)" style={{ flex: 1 }}>
                            <Input
                              type="number"
                              value={parseInt(selectedElement.styles.top) || 0}
                              onChange={(e) => {
                                updateElementStyle('top', `${e.target.value}px`);
                              }}
                              addonAfter="px"
                            />
                          </Form.Item>
                        </div>

                        <p style={{ color: '#1890ff', fontSize: '12px', marginBottom: '16px' }}>
                          提示: 您也可以直接拖动元素来调整位置
                        </p>
                      </>
                    )}

                    {/* 变换控制 */}
                    <Form.Item label="变换 (transform)">
                      <Input
                        value={selectedElement.styles.transform || ''}
                        onChange={(e) => {
                          updateElementStyle('transform', e.target.value);
                        }}
                        placeholder="例如: translate(10px, 20px)"
                      />
                      <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
                        常用格式: translate(x, y), rotate(deg), scale(n)
                      </div>
                    </Form.Item>
                  </Form>
                ) : (
                  <div>
                    <p>请在左侧选择一个元素进行编辑</p>
                    <p style={{ marginTop: '16px', color: '#1890ff' }}>
                      提示: 如果无法选择元素，请点击上方的"源代码编辑"按钮，直接编辑HTML代码。
                    </p>
                  </div>
                )}
              </div>
            </TabPane>

            {/* SVG属性标签页 - 仅当选中的元素是SVG元素时显示 */}
            {selectedElement && selectedElement.isSvg && (
              <TabPane tab="SVG属性" key="svg">
                <div className={styles.propertySection}>
                  <Form layout="vertical">
                    <Form.Item label="填充颜色 (fill)">
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ColorPicker
                          value={selectedElement.styles.fill || '#000000'}
                          onChange={(color) => {
                            const hexColor = color.toHexString();
                            updateElementStyle('fill', hexColor);
                          }}
                        />
                        <span style={{ marginLeft: 8 }}>{selectedElement.styles.fill || '#000000'}</span>
                      </div>
                    </Form.Item>

                    <Form.Item label="描边颜色 (stroke)">
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ColorPicker
                          value={selectedElement.styles.stroke || '#000000'}
                          onChange={(color) => {
                            const hexColor = color.toHexString();
                            updateElementStyle('stroke', hexColor);
                          }}
                        />
                        <span style={{ marginLeft: 8 }}>{selectedElement.styles.stroke || '#000000'}</span>
                      </div>
                    </Form.Item>

                    <Form.Item label="描边宽度 (stroke-width)">
                      <Slider
                        min={0}
                        max={20}
                        value={parseInt(selectedElement.styles['stroke-width']) || 1}
                        onChange={(value) => {
                          updateElementStyle('stroke-width', value.toString());
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{parseInt(selectedElement.styles['stroke-width']) || 1}px</div>
                    </Form.Item>

                    <Form.Item label="不透明度 (opacity)">
                      <Slider
                        min={0}
                        max={100}
                        value={Math.round((parseFloat(selectedElement.styles.opacity) || 1) * 100)}
                        onChange={(value) => {
                          updateElementStyle('opacity', (value / 100).toString());
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{Math.round((parseFloat(selectedElement.styles.opacity) || 1) * 100)}%</div>
                    </Form.Item>

                    <Divider orientation="left">SVG元素属性</Divider>
                    <p style={{ marginBottom: 16, color: '#1890ff' }}>
                      提示: 如需更复杂的SVG编辑，请点击"编辑SVG"按钮使用专用SVG编辑器
                    </p>

                    <Button
                      type="primary"
                      onClick={() => {
                        // 提取SVG内容
                        if (selectedElement && iframeRef.current) {
                          try {
                            const iframe = iframeRef.current;
                            const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

                            if (!iframeDoc) return;

                            // 查找选中的元素
                            const selectedDomElement = iframeDoc.querySelector('.element-highlight');
                            if (selectedDomElement) {
                              // 如果选中的是SVG元素本身
                              if (selectedDomElement.tagName.toLowerCase() === 'svg') {
                                const svgContent = selectedDomElement.outerHTML;
                                // 调用父组件提供的回调函数来打开SvgEditor
                                if (onEditSvg) {
                                  onEditSvg(svgContent, selectedElement.path);
                                } else {
                                  message.info('SVG编辑功能未配置');
                                  console.log('SVG内容:', svgContent);
                                }
                              }
                              // 如果选中的是SVG内部元素
                              else if (selectedElement.isSvg) {
                                // 找到父SVG元素
                                const svgElement = selectedDomElement.closest('svg');
                                if (svgElement) {
                                  const svgContent = svgElement.outerHTML;
                                  // 调用父组件提供的回调函数来打开SvgEditor
                                  if (onEditSvg) {
                                    onEditSvg(svgContent, selectedElement.path);
                                  } else {
                                    message.info('SVG编辑功能未配置');
                                    console.log('SVG内容:', svgContent);
                                  }
                                } else {
                                  message.error('无法找到父SVG元素');
                                }
                              }
                            }
                          } catch (error) {
                            console.error('提取SVG内容失败:', error);
                            message.error('提取SVG内容失败');
                          }
                        }
                      }}
                      style={{ marginBottom: 16 }}
                    >
                      编辑SVG
                    </Button>

                    <div style={{ maxHeight: '200px', overflow: 'auto', border: '1px solid #f0f0f0', padding: '8px', borderRadius: '4px' }}>
                      {Object.entries(selectedElement.styles).map(([key, value]) => (
                        <div key={key} style={{ marginBottom: '8px', display: 'flex', justifyContent: 'space-between' }}>
                          <span style={{ fontWeight: 'bold' }}>{key}:</span>
                          <span>{value}</span>
                        </div>
                      ))}
                    </div>
                  </Form>
                </div>
              </TabPane>
            )}
          </Tabs>
        </div>
      </>
    );
  };

  return (
    <Modal
      title="HTML 编辑器"
      open={visible}
      onCancel={onCancel}
      width="90%"
      style={{ top: 20 }}
      footer={null}
      styles={{ body: { height: 'calc(90vh - 100px)', padding: 0, overflow: 'hidden' } }}
      destroyOnHidden
    >
      <div className={styles.editorContainer}>
        {/* WorkflowGraph和CodeBlock选择器 */}
        {renderSelectors()}
        
        {/* 工具栏 */}
        {renderToolbar()}

        {/* 编辑区域 */}
        <div className={styles.editorContent}>
          {editMode === 'source' ? renderSourceEditor() : renderVisualEditor()}
        </div>
      </div>

      {/* 预览模态框 */}
      <Modal
        title="HTML 预览"
        open={previewVisible}
        onCancel={closePreview}
        width="90%"
        styles={{ body: { padding: '16px' } }}
        footer={<Button onClick={closePreview}>关闭预览</Button>}
      >
        <div className={styles.previewContainer}>
          <iframe
            srcDoc={htmlContent}
            className={styles.previewFrame}
            title="HTML内容预览"
            style={{ width: '100%', height: '70vh', border: 'none' }}
          />
        </div>
      </Modal>
    </Modal>
  );
};

export default HtmlEditor;
