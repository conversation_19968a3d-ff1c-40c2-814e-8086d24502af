.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
  transition: height 0.3s ease;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 4px;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin: 0;
  flex-shrink: 0;
}

.actionButtons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.zoomIndicator {
  font-size: 12px;
  color: #666;
  margin: 0 4px;
  min-width: 36px;
  text-align: center;
}

.content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.codeContainer {
  padding: 16px;
  overflow: auto;
  position: relative;
  background-color: #f5f5f5;
  transition: height 0.3s ease;
  min-height: 300px;
}

.codeBlock {
  margin: 0;
  word-wrap: break-word;
  white-space: pre-wrap;
  overflow-x: auto;
  width: 100%;
}

.previewContainer {
  width: 100%;
  overflow: hidden;
  position: relative;
  transition: height 0.3s ease;
  min-height: 300px;
}

.previewFrame {
  width: 100%;
  height: 100%;
  border: none;
}

.pagination {
  padding: 12px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e8e8e8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    width: 100%;
  }
}

/* 动画效果 */
.codeContainer, .previewContainer {
  transition: all 0.3s ease;
}

/* 全屏预览相关样式 */
.fullscreenContainer {
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  overflow: auto;
}

.fullscreenFrame {
  width: 100%;
  height: 100%;
  border: none;
  background-color: #fff;
  overflow: auto !important;
}

.fullscreenHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.fullscreenControls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 修复一些按钮样式问题 */
.buttons {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
} 