import React, { useState, useRef, useEffect } from 'react';
import {Button, notification, Empty, Pagination, Modal } from 'antd';
import { CodeOutlined, EyeOutlined, CopyOutlined, DownloadOutlined, ZoomInOutlined, ZoomOutOutlined, FullscreenOutlined, SaveOutlined, EditOutlined, FilePdfOutlined } from '@ant-design/icons';
import styles from './HtmlSvgViewer.module.css';
import { HtmlEditor, SvgEditor, ApiHtmlToPdfExporter } from './index';
import { WorkflowCodeBlock, WorkflowGraphData } from '../../utils/WorkflowCodeBlockProcessor';


interface HtmlSvgViewerProps {
  workflowGraphList: WorkflowGraphData[];
  codeBlocks: WorkflowCodeBlock[];
  sessionId: string;
  clickedCodeBlockId?: string | null;
  isTabActive?: boolean;
}

const HtmlSvgViewer: React.FC<HtmlSvgViewerProps> = ({ workflowGraphList, codeBlocks, sessionId, clickedCodeBlockId, isTabActive = false }) => {
  const [activeCodeBlock, setActiveCodeBlock] = useState<WorkflowCodeBlock | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [zoomLevel, setZoomLevel] = useState(1); // 添加缩放级别状态
  const [isFullscreenModalVisible, setIsFullscreenModalVisible] = useState(false); // 添加全屏模态框状态
  const [fullscreenZoomLevel, setFullscreenZoomLevel] = useState(1.5); // 全屏模式下的缩放级别
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const fullscreenIframeRef = useRef<HTMLIFrameElement>(null);
  const pdfExporterRef = useRef<{ openModal: () => void } | null>(null); // 添加PDF导出器引用
  // 客户端PDF导出器引用已移除
  const contentRef = useRef<HTMLDivElement>(null);
  const pageSize = 1; // 每页显示一个代码块
  const prevIsPreviewModeRef = useRef(isPreviewMode); // Track previous preview mode state
  const [isEditorVisible, setIsEditorVisible] = useState<boolean>(false);
  const [contentHeight, setContentHeight] = useState<number>(400); // 默认高度
  console.log('codeBlocks', codeBlocks,clickedCodeBlockId);
  // 处理用户点击代码块的逻辑
  useEffect(() => {
    if (clickedCodeBlockId && codeBlocks.length > 0) {
      // 找到对应的代码块
      const clickedBlock = codeBlocks.find(block => block.content_id === clickedCodeBlockId);

      if (clickedBlock) {
        // 设置活动代码块
        setActiveCodeBlock(clickedBlock);

        // 设置为预览模式
        setIsPreviewMode(true);

        // 更新渲染状态，强制触发一次预览
        prevIsPreviewModeRef.current = false; // 欺骗useEffect，让它认为我们是从代码模式切换到预览模式

        // 找到对应的页码
        const pageIndex = codeBlocks.findIndex(block => block.content_id === clickedCodeBlockId);
        if (pageIndex !== -1) {
          setCurrentPage(pageIndex + 1);
        }

        console.log('检测到用户点击代码块，准备预览:', {
          clickedId: clickedCodeBlockId,
          blockIndex: pageIndex,
          totalBlocks: codeBlocks.length,
          page: pageIndex + 1
        });

        // 使用setTimeout确保DOM已更新，然后立即手动触发预览，并直接传递代码块
        setTimeout(() => {
          if (iframeRef.current) {
            console.log('立即执行预览渲染');
            updatePreview(clickedBlock);
          }
        }, 100);
      }
    }
  }, [clickedCodeBlockId, codeBlocks]); // 依赖于clickedCodeBlockId和codeBlocks

  // 添加代码块数量控制和去重逻辑
  // 当代码块列表首次加载或更新时的初始化逻辑
  useEffect(() => {
    // 去除内容完全相同的代码块
    const uniqueBlocks: WorkflowCodeBlock[] = [];
    const contentSet = new Set<string>();

    codeBlocks.forEach(block => {
      // 创建内容标识符：类型+内容的组合
      const contentId = `${block.language}:${block.code}`;

      if (!contentSet.has(contentId)) {
        contentSet.add(contentId);
        uniqueBlocks.push(block);
      }
    });

    // 如果发现重复，记录日志
    if (uniqueBlocks.length < codeBlocks.length) {
      console.log(`检测到重复代码块：原始数量${codeBlocks.length}，去重后${uniqueBlocks.length}`);
    }

    console.log(`HtmlSvgViewer接收到${codeBlocks.length}个代码块，去重后剩余${uniqueBlocks.length}个`);

    // 使用去重后的代码块数组处理后续逻辑
    const effectiveBlocks = uniqueBlocks;

    // 如果代码块数量超过50个，只保留最新的50个
    if (effectiveBlocks.length > 50) {
      console.warn(`代码块数量(${effectiveBlocks.length})超过50个，将截取最新的50个`);
      // 此处不直接修改props，而是在内部状态维护一个截取后的版本
      const truncatedBlocks = effectiveBlocks.slice(-50);
      if (activeCodeBlock && !truncatedBlocks.some(block => block.content_id === activeCodeBlock.content_id)) {
        // 如果当前活动的代码块被截取掉了，重置为第一个
        setActiveCodeBlock(truncatedBlocks[0]);
        setCurrentPage(1);
      }
    } else if (effectiveBlocks.length > 0) {
      // 如果activeCodeBlock为空或不在当前effectiveBlocks中，则设置为第一个
      if (!activeCodeBlock || !effectiveBlocks.some(block => block.content_id === activeCodeBlock.content_id)) {
        console.log('初始化选择第一个代码块');
        setActiveCodeBlock(effectiveBlocks[0]);
        setCurrentPage(1);
      }
    } else {
      // 没有代码块时清空状态
      setActiveCodeBlock(null);
    }
  }, [codeBlocks]); // 仅依赖codeBlocks的变化

  // 添加新的useEffect来处理预览模式切换和代码块变化
  useEffect(() => {
    if (isPreviewMode && activeCodeBlock && iframeRef.current) {
      console.log('isPreviewMode or activeCodeBlock changed, updating preview...');
      const timeoutId = setTimeout(() => updatePreview(activeCodeBlock), 150); // 增加延迟确保状态更新
      return () => clearTimeout(timeoutId);
    } else if (!isPreviewMode && iframeRef.current && iframeRef.current.contentDocument) {
      // Clear iframe content when switching to code view
      const iframeDoc = iframeRef.current.contentDocument;
      if (iframeDoc.body && iframeDoc.body.innerHTML !== '' && iframeDoc.body.innerHTML !== '<body></body>') {
        try {
          iframeDoc.open();
          iframeDoc.write('<!DOCTYPE html><html><head></head><body></body></html>');
          iframeDoc.close();
          console.log('Cleared iframe content on switching to code view.');
        } catch(e) {
          console.error("Could not clear iframe content", e);
        }
      }
    }
  }, [isPreviewMode, activeCodeBlock]); // 依赖于预览模式和当前代码块

  // 监听标签页激活状态变化
  useEffect(() => {
    console.log('标签页状态变化:', {isTabActive, codeBlocksCount: codeBlocks.length, hasActiveBlock: !!activeCodeBlock});

    // 当标签页激活且有代码块时，立即选择第一个代码块
    if (isTabActive && codeBlocks.length > 0) {
      const firstBlock = codeBlocks[0];

      // 无条件选择第一个代码块，确保标签切换时始终显示
      console.log('标签页激活，强制选择第一个代码块');
      setActiveCodeBlock(firstBlock);
      setCurrentPage(1);

      // 确保处于预览模式
      if (!isPreviewMode) {
        setIsPreviewMode(true);
        // 由于isPreviewMode变化会触发另一个useEffect，它会负责渲染
      } else {
        // 如果已经是预览模式，则直接更新预览
        setTimeout(() => {
          if (iframeRef.current) {
            updatePreview(firstBlock);
          }
        }, 100);
      }
    }
  }, [isTabActive, codeBlocks]); // 只依赖这两个，因为逻辑是强制选择第一个

  // 添加处理缩放的函数
  const handleZoomIn = () => {
    setZoomLevel(prevZoom => {
      const newZoom = Math.min(prevZoom + 0.25, 3); // 限制最大缩放比例为3倍
      applyZoom(newZoom);
      return newZoom;
    });
  };

  const handleZoomOut = () => {
    setZoomLevel(prevZoom => {
      const newZoom = Math.max(prevZoom - 0.25, 0.5); // 限制最小缩放比例为0.5倍
      applyZoom(newZoom);
      return newZoom;
    });
  };

  const applyZoom = (zoom: number) => {
    if (iframeRef.current && activeCodeBlock && activeCodeBlock.language === 'svg') {
      const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
      if (iframeDoc) {
        const svgElement = iframeDoc.querySelector('svg');
        if (svgElement) {
          svgElement.style.transform = `scale(${zoom})`;
          svgElement.style.transformOrigin = 'top center';
          console.log('SVG缩放已应用:', zoom);
        }
      }
    }
  };

  // 添加内容高度监听
  useEffect(() => {
    // 监听iframe加载完成事件
    const handleIframeLoad = () => {
      if (iframeRef.current && isPreviewMode) {
        try {
          // 获取iframe内容的实际高度
          const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
          if (iframeDoc) {
            const height = iframeDoc.body.scrollHeight || 400;
            console.log('内容高度:', height);
            // 设置最小高度400px，最大高度80vh
            const newHeight = Math.max(400, Math.min(height, window.innerHeight * 0.8));
            setContentHeight(newHeight);
          }
        } catch (error) {
          console.error('获取iframe高度失败:', error);
        }
      }
    };

    // 设置iframe加载事件监听
    if (iframeRef.current) {
      iframeRef.current.onload = handleIframeLoad;
    }

    // 定期检查内容高度
    const checkContentHeight = () => {
      if (iframeRef.current && isPreviewMode) {
        try {
          const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
          if (iframeDoc && iframeDoc.body) {
            const height = iframeDoc.body.scrollHeight || 400;
            // 如果高度有明显变化，则更新
            if (Math.abs(height - contentHeight) > 50) {
              const newHeight = Math.max(400, Math.min(height, window.innerHeight * 0.8));
              setContentHeight(newHeight);
            }
          }
        } catch (error) {
          console.error('检查iframe高度失败:', error);
        }
      }
    };

    // 设置定时器定期检查内容高度
    const timer = setInterval(checkContentHeight, 1000);

    // 窗口尺寸变化时重新计算高度
    const handleResize = () => {
      if (iframeRef.current && isPreviewMode) {
        checkContentHeight();
      }
    };
    window.addEventListener('resize', handleResize);

    return () => {
      clearInterval(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, [isPreviewMode, activeCodeBlock, contentHeight]);

  // 更新预览内容
  const updatePreview = (blockToRender?: WorkflowCodeBlock | null) => {
    const block = blockToRender || activeCodeBlock;

    if (!block || !iframeRef.current) {
      console.log('无法渲染，代码块或iframe未就绪', {
        blockId: block?.content_id,
        hasIframe: !!iframeRef.current
      });
      return;
    }

    console.log('开始渲染预览内容...', {
      codeType: block.language,
      codeId: block.content_id,
      contentLength: block.code.length
    });

    const iframe = iframeRef.current;
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

    if (!iframeDoc) {
      console.error('无法获取iframe文档对象');
      return;
    }

    try {
      let htmlContent = '';
      let safeContent = block.code;

      // 尝试修复或净化可能导致问题的SVG内容
      if (block.language === 'svg') {
        try {
          // 尝试解析SVG以验证其有效性
          const parser = new DOMParser();
          const svgDoc = parser.parseFromString(safeContent, 'image/svg+xml');

          // 检查解析错误
          const parserError = svgDoc.querySelector('parsererror');
          if (parserError) {
            console.warn('SVG解析错误，将尝试添加错误提示', parserError.textContent);
            // 将原始SVG包装在错误提示中
            safeContent = `
              <div style="border: 2px solid red; padding: 10px; margin: 10px; background-color: #fff0f0;">
                <div style="color: red; margin-bottom: 10px; font-weight: bold;">SVG渲染错误:</div>
                <pre style="overflow: auto; max-height: 200px; background: #f8f8f8; padding: 8px; border: 1px solid #ddd;">${
                  block.code.replace(/</g, '&lt;').replace(/>/g, '&gt;')
                }</pre>
              </div>
            `;
          }
        } catch (parseError) {
          console.error('SVG解析失败:', parseError);
        }
      }

      if (block.language === 'svg') {
        // SVG代码模板 - 使用可能已修改的safeContent
        htmlContent = `
          <!DOCTYPE html>
          <html style="height: 100%; overflow: auto;">
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>
                html, body {
                  margin: 0;
                  padding: 16px;
                  height: 100%;
                  overflow: auto !important;
                  background-color: #f8f9fa;
                }
                body {
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  align-items: center;
                  min-height: 100%;
                  padding-bottom: 40px;
                }
                .error-container {
                  color: red;
                  background-color: #fff0f0;
                  border: 1px solid red;
                  padding: 10px;
                  margin: 10px 0;
                  border-radius: 4px;
                  max-width: 100%;
                  overflow: auto;
                }
                .svg-container {
                  margin-top: 20px;
                  text-align: center;
                  transform: scale(${zoomLevel});
                  transform-origin: top center;
                  transition: transform 0.3s ease;
                  cursor: zoom-in;
                }
                svg {
                  max-width: 100%;
                  width: auto;
                  height: auto;
                  display: block; /* 确保SVG完整显示 */
                  overflow: visible;
                }
                * {
                  box-sizing: border-box;
                }
              </style>
              <script>
                // 添加错误捕获
                window.onerror = function(message, source, lineno, colno, error) {
                  console.error('SVG渲染错误:', message);
                  const errorDiv = document.createElement('div');
                  errorDiv.className = 'error-container';
                  errorDiv.innerHTML = '<strong>SVG渲染错误:</strong><br>' + message;
                  document.body.insertBefore(errorDiv, document.body.firstChild);
                  return true; // 防止错误向上冒泡
                };

                // 添加SVG尺寸自适应脚本
                window.onload = function() {
                  const svgElement = document.querySelector('svg');
                  const svgContainer = document.querySelector('.svg-container');

                  if (svgElement) {
                    // 确保SVG元素有正确的viewBox
                    if (!svgElement.getAttribute('viewBox') &&
                        svgElement.getAttribute('width') &&
                        svgElement.getAttribute('height')) {
                      const width = parseFloat(svgElement.getAttribute('width'));
                      const height = parseFloat(svgElement.getAttribute('height'));
                      svgElement.setAttribute('viewBox', '0 0 ' + width + ' ' + height);
                    }

                    // 移除可能限制尺寸的固定宽高
                    if (svgElement.style.width || svgElement.style.height) {
                      svgElement.style.width = '';
                      svgElement.style.height = '';
                    }

                    // 应用最大宽度保证完整显示
                    svgElement.style.maxWidth = '100%';
                    svgElement.style.width = 'auto';
                    svgElement.style.height = 'auto';

                    console.log('SVG尺寸调整已应用');
                  }

                  // 添加双击事件处理
                  if (svgContainer) {
                    svgContainer.addEventListener('dblclick', function() {
                      // 通知父窗口打开全屏模式
                      window.parent.postMessage('openFullscreen', '*');
                    });
                  }
                };
              </script>
            </head>
            <body>
              <div class="svg-container">
                ${safeContent}
              </div>
            </body>
          </html>
        `;
      } else {
        // HTML代码处理
        const parser = new DOMParser();
        const doc = parser.parseFromString(block.code, 'text/html');

        // 查找并隔离内联脚本
        const scripts = doc.querySelectorAll('script:not([src])');
        scripts.forEach(script => {
          if (script instanceof HTMLScriptElement) { // 类型守卫
            const originalContent = script.textContent || script.innerText; // innerText 可能在某些情况下需要
            if (originalContent && originalContent.trim()) {
              // 使用 IIFE 隔离作用域
              script.textContent = `(function() { try { ${originalContent} } catch(e) { console.error('Script execution error:', e); } })();`;
              // 添加标记，避免重复处理
              script.setAttribute('data-isolated', 'true');
            }
          } else {
            console.warn('Found a script element that is not an HTMLScriptElement:', script);
          }
        });

        // 获取处理后的HTML内容
        let processedContent = doc.documentElement.outerHTML;

        // 构建完整的HTML模板，并将处理后的内容注入
        htmlContent = `
          <!DOCTYPE html>
          <html style="height: 100%; overflow: auto;">
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>
                /* 保留必要的全局样式 */
                html, body {
                  overflow: auto !important;
                  height: 100%;
                  margin: 0;
                  min-height: 100%;
                  width: 100%;
                  /* 移除强制白色背景 */
                }
                body {
                  padding: 0;
                  margin: 0;
                  width: 100%;
                  min-width: 100%;
                  display: block;
                  min-height: 100vh;
                  /* 让body背景透明，以便显示HTML内容的背景 */
                  background-color: transparent;
                }
                * {
                  box-sizing: border-box;
                }
                .error-container {
                  color: red;
                  background-color: #fff0f0;
                  border: 1px solid red;
                  padding: 10px;
                  margin: 10px 0;
                  border-radius: 4px;
                  max-width: 100%;
                  overflow: auto;
                }
              </style>
              <script>
                // 仅保留错误捕获脚本
                window.onerror = function(message, source, lineno, colno, error) {
                  console.error('HTML渲染错误:', message);
                  const errorDiv = document.createElement('div');
                  errorDiv.className = 'error-container';
                  errorDiv.innerHTML = '<strong>渲染错误:</strong><br>' + message;
                  document.body.insertBefore(errorDiv, document.body.firstChild);
                  return true;
                };

                // 添加文档加载完成处理
                window.onload = function() {
                  // 尝试获取实际内容的背景色并应用
                  const bodyElement = document.body;
                  const htmlElement = document.documentElement;
                  let contentBgColor = 'white'; // 默认为白色

                  // 尝试从body的第一个子元素获取背景色（更可能包含内容的容器）
                  if (bodyElement.firstElementChild) {
                     const firstChildStyle = window.getComputedStyle(bodyElement.firstElementChild);
                     if (firstChildStyle.backgroundColor && firstChildStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' && firstChildStyle.backgroundColor !== 'transparent') {
                       contentBgColor = firstChildStyle.backgroundColor;
                     }
                  }
                  // 如果没找到，尝试从body获取
                  else {
                     const bodyStyle = window.getComputedStyle(bodyElement);
                     if (bodyStyle.backgroundColor && bodyStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' && bodyStyle.backgroundColor !== 'transparent') {
                        contentBgColor = bodyStyle.backgroundColor;
                     }
                  }

                  htmlElement.style.backgroundColor = contentBgColor;
                  bodyElement.style.backgroundColor = contentBgColor;

                  // 确保内容至少填满视口
                  const viewportHeight = window.innerHeight;
                  if (bodyElement.scrollHeight < viewportHeight) {
                     bodyElement.style.minHeight = viewportHeight + 'px';
                  }
                };
              </script>
            </head>
            ${processedContent}
          </html>
        `;
      }

      iframeDoc.open();
      iframeDoc.write(htmlContent);
      iframeDoc.close();

      // 内容加载后检查高度
      setTimeout(() => {
        if (iframeRef.current) {
          try {
            const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
            if (iframeDoc && iframeDoc.body) {
              const height = iframeDoc.body.scrollHeight || 400;
              const newHeight = Math.max(400, Math.min(height, window.innerHeight * 0.8));
              setContentHeight(newHeight);
            }
          } catch (error) {
            console.error('更新内容高度失败:', error);
          }
        }
      }, 300);

      console.log('预览内容已渲染完成');

    } catch (error) {
      console.error('预览渲染错误:', error);

      // 显示错误信息在iframe中
      try {
        iframeDoc.open();
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  padding: 20px;
                  color: #d32f2f;
                  background-color: #ffebee;
                }
                .error-container {
                  border: 1px solid #d32f2f;
                  border-radius: 4px;
                  padding: 16px;
                  margin-bottom: 20px;
                }
                pre {
                  background: #f8f8f8;
                  padding: 10px;
                  border-radius: 4px;
                  overflow: auto;
                  font-size: 14px;
                }
              </style>
            </head>
            <body>
              <div class="error-container">
                <h3>渲染错误</h3>
                <p>${error instanceof Error ? error.message : String(error)}</p>
              </div>
              <h4>原始代码：</h4>
              <pre>${block.code.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
            </body>
          </html>
        `);
        iframeDoc.close();
      } catch (e) {
        console.error('显示错误信息失败:', e);
      }

      notification.error({
        message: '预览失败',
        description: '无法渲染预览内容'
      });
    }
  };

  // 复制代码
  const copyCode = () => {
    if (!activeCodeBlock) return;

    navigator.clipboard.writeText(activeCodeBlock.code)
      .then(() => {
        notification.success({
          message: '复制成功',
          description: '代码已复制到剪贴板'
        });
      })
      .catch(() => {
        notification.error({
          message: '复制失败',
          description: '无法复制到剪贴板，请手动选择并复制'
        });
      });
  };

  // 下载代码
  const downloadCode = () => {
    if (!activeCodeBlock) return;

    const fileType = activeCodeBlock.language === 'html' ? 'html' : 'svg';
    const fileName = `code-${sessionId.substring(0, 8)}-${activeCodeBlock.content_id}.${fileType}`;

    const blob = new Blob([activeCodeBlock.code], { type: `text/${fileType}` });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();

    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, 100);
  };

  // 切换代码/预览模式
  const togglePreviewMode = () => {
    setIsPreviewMode(prevMode => !prevMode); // Use functional update
    // The useEffect hook will handle the rendering logic based on the new state
  };

  // 翻页处理
  const handlePageChange = (page: number) => {
    // 获取当前页的代码块
    const newActiveCodeBlock = codeBlocks[page - 1];

    // 同步更新当前页码和活动代码块
    setCurrentPage(page);
    setActiveCodeBlock(newActiveCodeBlock);

    // 如果当前是预览模式，立即更新预览内容
    if (isPreviewMode && iframeRef.current) {
      // 使用setTimeout确保状态已更新
      setTimeout(() => {
        updatePreview(newActiveCodeBlock);
      }, 50);
    }

    console.log('页码已切换:', {
      page,
      newActiveBlockId: newActiveCodeBlock?.content_id,
      newActiveBlockType: newActiveCodeBlock?.language
    });
  };

  // 渲染卡片内容
  const renderContent = () => {
    if (codeBlocks.length === 0) {
      // 没有代码块，显示空状态
      return <Empty description="暂无可预览的内容" />;
    }

    if (!activeCodeBlock) {
      return <Empty description="没有可用的HTML/SVG代码" />;
    }

    if (isPreviewMode) {
      return (
        <div className={styles.previewContainer} ref={contentRef} style={{ height: `${contentHeight}px` }}>
          <iframe
            ref={iframeRef}
            className={styles.previewFrame}
            sandbox="allow-scripts allow-same-origin allow-popups allow-modals"
            title="HTML/SVG预览"
            scrolling="yes"
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              overflow: 'auto'
            }}
          />
        </div>
      );
    }

    return (
      <div className={styles.codeContainer} style={{ maxHeight: `${contentHeight}px` }}>
        <pre className={styles.codeBlock}>
          <code>{activeCodeBlock.code}</code>
        </pre>
      </div>
    );
  };

  // 创建按钮组
  const renderButtons = () => {
    if (!activeCodeBlock) return null;

    return (
      <div className={styles.buttons}>
        {/* 代码/预览切换按钮 */}
        <Button
          type="text"
          icon={isPreviewMode ? <CodeOutlined /> : <EyeOutlined />}
          onClick={togglePreviewMode}
          title={isPreviewMode ? '查看代码' : '预览'}
        />

        {/* 仅在代码模式下提供复制按钮 */}
        {!isPreviewMode && (
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={copyCode}
            title="复制"
          />
        )}

        {/* 提供代码下载按钮 */}
        <Button
          type="text"
          icon={<DownloadOutlined />}
          onClick={downloadCode}
          title="下载"
        />

        {/* 仅在预览模式下提供额外功能按钮 */}
        {isPreviewMode && (
          <>
            <Button
              type="text"
              icon={<ZoomInOutlined />}
              onClick={handleZoomIn}
              title="放大"
            />
            <Button
              type="text"
              icon={<ZoomOutOutlined />}
              onClick={handleZoomOut}
              title="缩小"
            />
            <Button
              type="text"
              icon={<FullscreenOutlined />}
              onClick={handleFullscreenMode}
              title="全屏"
            />

            {/* 删除原生PDF导出，只保留SVG的PNG导出 */}
            {activeCodeBlock.language === 'svg' && (
              <Button
                type="text"
                icon={<SaveOutlined />}
                onClick={handleSave}
                title="保存为PNG图片"
              />
            )}

            {/* 添加HTML类型的PDF保存按钮 */}
            {activeCodeBlock.language === 'html' && (
              <Button
                type="text"
                icon={<FilePdfOutlined />}
                onClick={saveHtmlAsPdf}
                title="导出为PDF"
              />
            )}

            {/* 添加API PDF导出按钮（仅对HTML类型的代码块） - 隐藏按钮 */}
            {activeCodeBlock.language === 'html' && (
              <ApiHtmlToPdfExporter
                iframeRef={iframeRef}
                sessionId={sessionId}
                codeBlockId={activeCodeBlock.content_id}
                codeBlocks={codeBlocks}
                showButton={false}
                onRef={(ref: { openModal: () => void }) => pdfExporterRef.current = ref}
              />
            )}

            {/* 客户端PDF导出按钮已移除 */}

            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={handleEdit}
              title="编辑"
            />
          </>
        )}
      </div>
    );
  };

  // 更新缩放状态
  useEffect(() => {
    applyZoom(zoomLevel);
  }, [zoomLevel, activeCodeBlock]);

  // 在切换代码块时重置缩放
  useEffect(() => {
    if (activeCodeBlock) {
      setZoomLevel(1);
    }
  }, [activeCodeBlock?.content_id]);

  // 处理双击打开全屏预览
  const handleFullscreenMode = () => {
    setIsFullscreenModalVisible(true);
    // 在模态框打开后渲染SVG
    setTimeout(() => {
      if (fullscreenIframeRef.current && activeCodeBlock) {
        updateFullscreenPreview(activeCodeBlock);
      }
    }, 100);
  };

  // 关闭全屏预览
  const handleCloseFullscreen = () => {
    setIsFullscreenModalVisible(false);
  };

  // 全屏模式下的缩放控制
  const handleFullscreenZoomIn = () => {
    setFullscreenZoomLevel(prevZoom => {
      const newZoom = Math.min(prevZoom + 0.25, 5); // 全屏模式下允许更大的缩放比例
      applyFullscreenZoom(newZoom);
      return newZoom;
    });
  };

  const handleFullscreenZoomOut = () => {
    setFullscreenZoomLevel(prevZoom => {
      const newZoom = Math.max(prevZoom - 0.25, 0.5);
      applyFullscreenZoom(newZoom);
      return newZoom;
    });
  };

  const applyFullscreenZoom = (zoom: number) => {
    if (fullscreenIframeRef.current && activeCodeBlock) {
      const iframeDoc = fullscreenIframeRef.current.contentDocument || fullscreenIframeRef.current.contentWindow?.document;
      if (iframeDoc) {
        const svgContainer = iframeDoc.querySelector('.svg-container');
        if (svgContainer) {
          (svgContainer as HTMLElement).style.transform = `scale(${zoom})`;
          console.log('全屏SVG缩放已应用:', zoom);
        }
      }
    }
  };

  // 全屏预览内容更新
  const updateFullscreenPreview = (block: WorkflowCodeBlock) => {
    if (!fullscreenIframeRef.current) return;

    const iframe = fullscreenIframeRef.current;
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

    if (!iframeDoc) {
      console.error('无法获取全屏iframe文档对象');
      return;
    }

    try {
      let safeContent = block.code;

      // 检查SVG有效性
      try {
        const parser = new DOMParser();
        const svgDoc = parser.parseFromString(safeContent, 'image/svg+xml');
        const parserError = svgDoc.querySelector('parsererror');
        if (parserError) {
          safeContent = `<div class="error-container"><strong>SVG渲染错误</strong><br>${parserError.textContent}</div>`;
        }
      } catch (error) {
        console.error('SVG解析失败:', error);
      }

      // 全屏预览HTML模板
      const htmlContent = `
        <!DOCTYPE html>
        <html style="height: 100%; overflow: auto;">
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              html, body {
                margin: 0;
                padding: 20px;
                height: 100%;
                overflow: auto !important;
                background-color: #f8f9fa;
              }
              body {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                min-height: 100%;
              }
              .svg-container {
                text-align: center;
                transform: scale(${fullscreenZoomLevel});
                transform-origin: center center;
                transition: transform 0.3s ease;
                margin: 20px auto;
              }
              svg {
                max-width: 100%;
                width: auto;
                height: auto;
                display: block;
                overflow: visible;
              }
              .error-container {
                color: red;
                background-color: #fff0f0;
                border: 1px solid red;
                padding: 10px;
                margin: 10px 0;
                border-radius: 4px;
                max-width: 100%;
                overflow: auto;
              }
            </style>
            <script>
              // 添加错误捕获
              window.onerror = function(message, source, lineno, colno, error) {
                console.error('SVG渲染错误:', message);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-container';
                errorDiv.innerHTML = '<strong>SVG渲染错误:</strong><br>' + message;
                document.body.insertBefore(errorDiv, document.body.firstChild);
                return true;
              };

              // 添加SVG尺寸自适应脚本
              window.onload = function() {
                const svgElement = document.querySelector('svg');
                if (svgElement) {
                  // 确保SVG元素有正确的viewBox
                  if (!svgElement.getAttribute('viewBox') &&
                      svgElement.getAttribute('width') &&
                      svgElement.getAttribute('height')) {
                    const width = parseFloat(svgElement.getAttribute('width'));
                    const height = parseFloat(svgElement.getAttribute('height'));
                    svgElement.setAttribute('viewBox', '0 0 ' + width + ' ' + height);
                  }

                  // 移除可能限制尺寸的固定宽高
                  if (svgElement.style.width || svgElement.style.height) {
                    svgElement.style.width = '';
                    svgElement.style.height = '';
                  }

                  // 应用最大宽度保证完整显示
                  svgElement.style.maxWidth = '100%';
                  svgElement.style.width = 'auto';
                  svgElement.style.height = 'auto';

                  console.log('全屏SVG尺寸调整已应用');
                }
              };
            </script>
          </head>
          <body>
            <div class="svg-container">
              ${safeContent}
            </div>
          </body>
        </html>
      `;

      iframeDoc.open();
      iframeDoc.write(htmlContent);
      iframeDoc.close();

      console.log('全屏预览已渲染');

    } catch (error) {
      console.error('全屏预览渲染错误:', error);
      notification.error({
        message: '全屏预览失败',
        description: '无法渲染全屏预览内容'
      });
    }
  };

  // 添加iframe消息监听
  useEffect(() => {
    const handleIframeMessage = (event: MessageEvent) => {
      if (event.data === 'openFullscreen') {
        handleFullscreenMode();
      }
    };

    window.addEventListener('message', handleIframeMessage);

    return () => {
      window.removeEventListener('message', handleIframeMessage);
    };
  }, [activeCodeBlock]);

  // 保存SVG为PNG图片
  const saveSvgAsPng = () => {
    if (!activeCodeBlock || activeCodeBlock.language !== 'svg' || !iframeRef.current) return;

    try {
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) {
        throw new Error('无法获取iframe文档对象');
      }

      const svgElement = iframeDoc.querySelector('svg');
      if (!svgElement) {
        throw new Error('未找到SVG元素');
      }

      // 创建SVG数据URL
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // 创建Image对象
      const img = new Image();
      img.onload = () => {
        try {
          // 计算宽高并考虑缩放比例
          const width = svgElement.clientWidth || svgElement.getBoundingClientRect().width || 800;
          const height = svgElement.clientHeight || svgElement.getBoundingClientRect().height || 600;

          // 创建Canvas渲染SVG
          const canvas = document.createElement('canvas');
          // 使用稍高的分辨率确保图像清晰
          const scale = 2;
          canvas.width = width * scale;
          canvas.height = height * scale;
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            throw new Error('无法获取Canvas上下文');
          }

          // 使用白色背景
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // 应用缩放以提高清晰度
          ctx.scale(scale, scale);

          // 绘制SVG
          ctx.drawImage(img, 0, 0, width, height);

          // 转换为PNG并下载
          const pngUrl = canvas.toDataURL('image/png');
          const fileName = `svg-${sessionId.substring(0, 8)}-${activeCodeBlock.content_id}.png`;

          const downloadLink = document.createElement('a');
          downloadLink.href = pngUrl;
          downloadLink.download = fileName;
          document.body.appendChild(downloadLink);
          downloadLink.click();
          document.body.removeChild(downloadLink);

          // 清理资源
          URL.revokeObjectURL(svgUrl);

          notification.success({
            message: '保存成功',
            description: `SVG已保存为PNG图片: ${fileName}`
          });
        } catch (error) {
          console.error('保存SVG为PNG时出错:', error);
          notification.error({
            message: '保存失败',
            description: String(error) || '无法将SVG转换为PNG'
          });
        }
      };

      img.onerror = (error) => {
        console.error('SVG加载失败:', error);
        notification.error({
          message: '保存失败',
          description: '无法加载SVG图像'
        });
        URL.revokeObjectURL(svgUrl);
      };

      img.src = svgUrl;

    } catch (error) {
      console.error('保存SVG为PNG出错:', error);
      notification.error({
        message: '保存失败',
        description: String(error) || '无法保存PNG图片'
      });
    }
  };

  // 保存HTML为PDF
  const saveHtmlAsPdf = () => {
    if (!activeCodeBlock || activeCodeBlock.language !== 'html') return;

    try {
      // 直接调用API PDF导出器
      if (pdfExporterRef.current) {
        pdfExporterRef.current.openModal();
      } else {
        notification.info({
          message: 'PDF导出不可用',
          description: '无法找到PDF导出器'
        });
      }
    } catch (error) {
      console.error('PDF保存错误:', error);
      notification.error({
        message: '保存失败',
        description: String(error) || '无法保存为PDF'
      });
    }
  };

  // 根据类型选择保存方法
  const handleSave = () => {
    if (!activeCodeBlock) return;

    if (activeCodeBlock.language === 'svg') {
      saveSvgAsPng();
    } else if (activeCodeBlock.language === 'html') {
      saveHtmlAsPdf();
    }
  };

  // 显示编辑器
  const handleEdit = () => {
    // 确保使用最新的activeCodeBlock
    if (!activeCodeBlock) {
      console.warn('无法编辑：没有选中的代码块');
      return;
    }

    // 获取当前页对应的代码块，确保使用最新的
    const currentBlockIndex = currentPage - 1;
    if (currentBlockIndex >= 0 && currentBlockIndex < codeBlocks.length) {
      // 如果当前页的代码块与activeCodeBlock不同，更新activeCodeBlock
      const currentPageBlock = codeBlocks[currentBlockIndex];
      if (currentPageBlock.content_id !== activeCodeBlock.content_id) {
        console.log('编辑前同步activeCodeBlock:', {
          from: activeCodeBlock.content_id,
          to: currentPageBlock.content_id
        });
        setActiveCodeBlock(currentPageBlock);

        // 使用setTimeout确保状态已更新后再打开编辑器
        setTimeout(() => {
          setIsEditorVisible(true);
        }, 50);
        return;
      }
    }

    // 如果activeCodeBlock已经是最新的，直接打开编辑器
    setIsEditorVisible(true);
  };

  // 处理SVG编辑请求
  const [svgEditContent, setSvgEditContent] = useState<string>('');
  const [svgEditVisible, setSvgEditVisible] = useState<boolean>(false);
  const [svgEditPath, setSvgEditPath] = useState<string>('');
  const [htmlWithSvgPath, setHtmlWithSvgPath] = useState<string>('');

  // 处理从HTML编辑器中提取的SVG内容
  const handleEditSvg = (svgContent: string, elementPath: string) => {
    console.log('准备编辑SVG:', { svgContent, elementPath });
    setSvgEditContent(svgContent);
    setSvgEditPath(elementPath);
    setSvgEditVisible(true);

    // 保存当前HTML路径，以便在SVG编辑完成后更新
    if (activeCodeBlock && activeCodeBlock.language === 'html') {
      setHtmlWithSvgPath(activeCodeBlock.content_id);
    }
  };

  // 处理SVG编辑完成
  const handleSvgEditComplete = (newSvgContent: string) => {
    console.log('SVG编辑完成:', newSvgContent);
    setSvgEditVisible(false);

    // 如果是从HTML中提取的SVG，需要更新HTML内容
    if (htmlWithSvgPath && activeCodeBlock && activeCodeBlock.language === 'html') {
      try {
        // 创建临时DOM解析HTML内容
        const parser = new DOMParser();
        const doc = parser.parseFromString(activeCodeBlock.code, 'text/html');

        // 查找SVG元素路径
        const pathParts = svgEditPath.split(' > ');
        let currentElement: Element | null = null;

        // 简单的路径查找逻辑
        if (pathParts.includes('svg')) {
          // 查找所有SVG元素
          const svgElements = doc.querySelectorAll('svg');
          if (svgElements.length > 0) {
            // 暂时只处理第一个找到的SVG元素
            currentElement = svgElements[0];

            // 替换SVG内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newSvgContent;
            const newSvg = tempDiv.querySelector('svg');

            if (newSvg && currentElement) {
              currentElement.parentNode?.replaceChild(newSvg, currentElement);

              // 更新HTML内容
              const updatedHtml = doc.documentElement.outerHTML;

              // 创建新的代码块，保留原来的ID
              const updatedCodeBlock: WorkflowCodeBlock = {
                ...activeCodeBlock,
                code: updatedHtml
              };

              // 触发重新渲染
              setActiveCodeBlock(updatedCodeBlock);
              setTimeout(() => {
                if (iframeRef.current) {
                  updatePreview(updatedCodeBlock);
                }
              }, 100);

              notification.success({
                message: 'SVG更新成功',
                description: 'HTML中的SVG内容已更新'
              });
            }
          }
        }
      } catch (error) {
        console.error('更新HTML中的SVG失败:', error);
        notification.error({
          message: '更新失败',
          description: '无法更新HTML中的SVG内容'
        });
      }
    }

    // 清除路径
    setHtmlWithSvgPath('');
  };

  // 保存HTML编辑后的内容（来自HtmlEditor组件）
  const handleSaveEdit = (updatedWorkflowGraphList: WorkflowGraphData[]) => {
    // 提示用户更新了代码块
    notification.success({
      message: '编辑成功',
      description: 'HTML内容已更新并保存到后端'
    });

    // 关闭编辑器
    setIsEditorVisible(false);

    // 如果有activeCodeBlock，触发重新渲染
    if (activeCodeBlock) {
      // 从更新后的数据中找到对应的代码块
      const updatedCodeBlock = updatedWorkflowGraphList
        .flatMap(workflow => workflow.code_blocks || [])
        .find(block => block.content_id === activeCodeBlock.content_id);

      if (updatedCodeBlock) {
        setActiveCodeBlock(updatedCodeBlock);
        setTimeout(() => {
          if (iframeRef.current) {
            updatePreview(updatedCodeBlock);
          }
        }, 100);
      }
    }
  };

  // 保存SVG编辑后的内容（来自SvgEditor组件）
  const handleSaveSvgEdit = (newContent: string) => {
    if (!activeCodeBlock) return;

    // 创建新的代码块，保留原来的ID
    const updatedCodeBlock: WorkflowCodeBlock = {
      ...activeCodeBlock,
      code: newContent
    };

    // 提示用户更新了代码块
    notification.success({
      message: '编辑成功',
      description: `${activeCodeBlock.language === 'html' ? 'HTML' : 'SVG'} 内容已更新`
    });

    // 关闭编辑器
    setIsEditorVisible(false);

    // 触发重新渲染
    setActiveCodeBlock(updatedCodeBlock);
    setTimeout(() => {
      if (iframeRef.current) {
        updatePreview(updatedCodeBlock);
      }
    }, 100);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setIsEditorVisible(false);
  };
  // 如果没有代码块，不显示组件
  if (codeBlocks.length === 0) {
    return null;
  }

  return (
    <div className={styles.container} ref={containerRef}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          {/* 移除了批量HTML导出PDF功能 */}
        </div>
        <div className={styles.actionButtons}>
          {renderButtons()}
        </div>
      </div>

      <div className={styles.content}>
        {renderContent()}
      </div>

      {codeBlocks.length > 1 && (
        <div className={styles.pagination}>
          <Pagination
            simple
            current={currentPage}
            total={codeBlocks.length}
            pageSize={pageSize}
            onChange={handlePageChange}
          />
        </div>
      )}

      {/* 全屏预览模态框 */}
      <Modal
        title={
          <div className={styles.fullscreenHeader}>
            <span>SVG全屏预览</span>
            <div className={styles.fullscreenControls}>
              <Button type="text" icon={<ZoomInOutlined />} onClick={handleFullscreenZoomIn} />
              <Button type="text" icon={<ZoomOutOutlined />} onClick={handleFullscreenZoomOut} />
              <Button
                type="text"
                icon={<SaveOutlined />}
                onClick={saveSvgAsPng}
                title="保存为PNG图片"
              />
              <span className={styles.zoomIndicator}>{Math.round(fullscreenZoomLevel * 100)}%</span>
            </div>
          </div>
        }
        open={isFullscreenModalVisible}
        onCancel={handleCloseFullscreen}
        width="90%"
        style={{ top: 20 }}
        styles={{ body: { height: 'calc(90vh - 80px)', padding: 0 } }}
        footer={null}
      >
        <div className={styles.fullscreenContainer}>
          <iframe
            ref={fullscreenIframeRef}
            className={styles.fullscreenFrame}
            sandbox="allow-scripts allow-same-origin allow-modals"
            title="SVG全屏预览"
            style={{ width: '100%', height: '100%', border: 'none' }}
          />
        </div>
      </Modal>

      {/* 编辑器模态框 - 根据内容类型选择合适的编辑器 */}
      {activeCodeBlock?.language === 'html' ? (
        <HtmlEditor
          workflowGraphList={workflowGraphList}
          onSave={handleSaveEdit}
          onCancel={handleCancelEdit}
          visible={isEditorVisible}
          onEditSvg={handleEditSvg}
          key={`html-editor-${activeCodeBlock?.content_id || 'default'}`} // 添加key确保组件在activeCodeBlock变化时重新渲染
        />
      ) : (
        <SvgEditor
          content={activeCodeBlock?.code || ''}
          onSave={handleSaveSvgEdit}
          onCancel={handleCancelEdit}
          visible={isEditorVisible}
          key={`svg-editor-${activeCodeBlock?.content_id || 'default'}`} // 添加key确保组件在activeCodeBlock变化时重新渲染
        />
      )}

      {/* SVG编辑器模态框 - 用于编辑HTML中的SVG */}
      {svgEditVisible && (
        <SvgEditor
          content={svgEditContent}
          onSave={handleSvgEditComplete}
          onCancel={() => setSvgEditVisible(false)}
          visible={svgEditVisible}
          key={`svg-editor-embedded-${svgEditPath || 'default'}`} // 添加key确保组件在SVG内容变化时重新渲染
        />
      )}
    </div>
  );
};

export default HtmlSvgViewer;