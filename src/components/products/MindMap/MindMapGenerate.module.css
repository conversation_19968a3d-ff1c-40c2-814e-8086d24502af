/* MindMapGenerate.module.css */

/* 确保全局元素没有默认边距 */
:global(html), :global(body) {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

:global(#root) {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.leftSide {
  flex: 0 0 40%;
  height: 100vh;
  overflow: auto;
  border-right: 1px solid #f0f0f0;
  background-color: #fff;
  padding: 10px;
}

.rightSide {
  flex: 0 0 60%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.inputCard {
  height: 100%;
  box-shadow: none;
  border: none;
  padding: 10px;
}

.inputTextArea {
  /* margin-bottom: 20px; */
  font-size: 14px;
  height: 100%;
  min-height: 500px;
  resize: none;
  padding: 15px;
}

/* 思维导图界面样式 */
.mindMapContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.toolbarContainer {
  padding: 10px 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  z-index: 10;
  flex-shrink: 0; /* 防止工具栏被压缩 */
}

.contentContainer {
  height: calc(100% - 60px); /* 减去工具栏高度 */
  width: 100%;
  padding: 0;
  display: flex; /* 确保内容占满容器 */
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

/* 标签页样式 */
.contentTabs {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.contentTabs :global(.ant-tabs-content) {
  height: 100%;
  flex: 1;
}

.contentTabs :global(.ant-tabs-tabpane) {
  height: 100%;
  overflow: hidden;
}

.tabPane {
  height: 100%;
  overflow: hidden;
}

.jsonSide {
  height: 100%;
  width: 100%;
  overflow: hidden; /* 改为hidden，让内部容器处理滚动 */
  padding: 0;
  color: #0c0c0c;
  background: #f7f8f8;
  flex: 1; /* 使其占满可用空间 */
  display: flex;
  flex-direction: column;
}

/* 添加滚动容器，专门负责JSON内容的滚动 */
.jsonScrollContainer {
  height: 100%;
  width: 100%;
  overflow: auto; /* 使内容可滚动 */
  flex: 1;
}

.jsonContent {
  margin: 0;
  padding: 15px;
  color: #0f0e0e;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  min-height: 100%;
  width: 100%;
  box-sizing: border-box;
  background: #f7f8f8;
}

/* 嵌入式思维导图容器 */
.embeddedMapContainer {
  height: 100%;
  width: 100%;
  flex: 1;
  position: relative;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

/* 预览模式指示器 */
.previewIndicator {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.loadingIndicator {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mapContent {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.jsonPlaceholder {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.exportButton {
  min-width: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.fullscreenButton {
  min-width: 100px;
}

.convertButton {
  min-width: 120px;
  right: 10px;
}

.regenButton {
  min-width: 100px;
}

/* 全屏思维导图容器 */
.fullScreenContainer {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.fullScreenContainer .mapContent {
  flex: 1;
  width: 100%;
  position: relative;
}

/* 按钮布局样式 */
.actionButtonContainer {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 20px;
  margin-bottom: 10px;
  gap: 20px; /* 按钮之间的间距 */
}

.actionButton {
  min-width: 160px;
  height: 40px;
  flex: 1; /* 让按钮平均分配容器宽度 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: 4px;
} 
.headerSettings {
  position: absolute;
  right: 8px;
  top: 16px;
  transform: translateY(-50%);
}