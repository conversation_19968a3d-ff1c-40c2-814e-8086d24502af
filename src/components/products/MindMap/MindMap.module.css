/* MindMap.module.css */
.mindMapContainer {
    width: 100vw;
    height: 100vh;
    position: relative;
    overflow: hidden;
  }
  
  .mindMapNode {
    padding: 10px 15px;
    border-radius: 8px;
    border: 2px solid #ccc;
    background-color: white;
    min-width: 120px;
    max-width: 250px;
    text-align: center;
    overflow-wrap: break-word;
    word-break: break-word;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    user-select: none;
    cursor: grab;
    position: relative;
  }
  
  .mindMapNode:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #1677ff;
  }
  
  /* 节点内容容器 */
  .nodeContent {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  
  /* 折叠/展开按钮 */
  .collapseButton {
    position: absolute;
    right: -24px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2px;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }
  
  .collapseButton:hover {
    background-color: #e0e0e0;
    transform: scale(1.1);
  }
  
  .collapseButton.collapsed {
    background-color: #1677ff;
    color: white;
    border-color: #1677ff;
  }
  
  /* 为边添加样式 */
  :global(.react-flow__edge) {
    cursor: pointer;
  }
  
  :global(.react-flow__edge:hover) {
    stroke: #1677ff;
    stroke-width: 2;
  }
  
  :global(.react-flow__edge-path) {
    stroke: #000000;
    stroke-width: 2;
  }
  
  :global(.react-flow__edge-text) {
    font-size: 12px;
  }
  
  :global(.react-flow__edge-path-selector:hover) {
    stroke: #1677ff;
    stroke-width: 10;
  }
  
  :global(.react-flow__edge-arrowclosed) {
    fill: #000000;
  }
  
  :global(.react-flow__edge:hover .react-flow__edge-arrowclosed) {
    fill: #1677ff;
  }
  
  /* 添加Handle样式 */
  :global(.react-flow__handle) {
    width: 8px;
    height: 8px;
    background-color: #555;
    border: 1px solid white;
  }
  
  :global(.react-flow__handle:hover) {
    background-color: #1677ff;
    width: 10px;
    height: 10px;
  }
  
  :global(.react-flow__handle-right) {
    right: -6px;
  }
  
  :global(.react-flow__handle-left) {
    left: -6px;
  }
  
  .nodeActionsOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
  }
  
  .nodeActions {
    position: absolute;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    padding: 8px;
    z-index: 1001;
    display: flex;
    flex-direction: column;
    transform: translate(-50%, -50%);
  }
  
  .nodeActions button {
    margin: 5px;
    padding: 8px 15px;
    border: none;
    background-color: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
    text-align: left;
  }
  
  .nodeActions button:hover {
    background-color: #e6e6e6;
  }
  
  .colorPickerOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .colorPicker {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 300px;
  }
  
  .colorPickerTitle {
    margin-bottom: 15px;
    font-weight: bold;
    text-align: center;
  }
  
  .colorGrid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 15px;
  }
  
  .colorOption {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #ddd;
    cursor: pointer;
  }
  
  .colorOption:hover {
    transform: scale(1.05);
  }
  
  .closeButton {
    width: 100%;
    padding: 8px;
    border: none;
    background-color: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .closeButton:hover {
    background-color: #e6e6e6;
  }
  
  .modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .modal {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 300px;
  }
  
  .modal h3 {
    margin-top: 0;
    margin-bottom: 15px;
  }
  
  .input {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  
  .modalButtons {
    display: flex;
    justify-content: space-between;
  }
  
  .modalButtons button {
    padding: 8px 15px;
    border: none;
    background-color: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .modalButtons button:first-child {
    background-color: #4caf50;
    color: white;
  }
  
  .modalButtons button:first-child:hover {
    background-color: #43a047;
  }
  
  .modalButtons button:last-child:hover {
    background-color: #e6e6e6;
  }
  
  .modalButtons button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
  
  .instructions {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 8px;
    font-size: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 10;
  }
  
  .instructions p {
    margin: 5px 0;
  }
  
  /* New styles for full-screen functionality */
  :global(.react-flow) {
    width: 100%;
    height: 100%;
  }
  
  :global(.react-flow__container) {
    width: 100%;
    height: 100%;
  }
  
  :global(.react-flow__pane) {
    cursor: grab;
  }
  
  :global(.react-flow__pane.dragging) {
    cursor: grabbing;
  }
  
  :global(body), :global(html) {
    margin: 0;
    padding: 0;
    overflow: hidden;
  }
  
  .layoutButton {
    padding: 8px 15px;
    background-color: #1677ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
  }
  
  .layoutButton:hover {
    background-color: #4096ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .layoutButton:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
  }

  .headerSettings {
    position: absolute;
    right: 16px;
    top: 16px;
    transform: translateY(-50%);
  }