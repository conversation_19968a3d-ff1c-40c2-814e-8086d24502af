import React, { useState, useCallback, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  NodeTypes,
  MarkerType,
  ConnectionLineType,
  Panel,
  Position,
  Handle,
  NodeChange,
  EdgeChange,
  ReactFlowInstance,
  ControlButton
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import styles from './MindMap.module.css';
import HeaderSettings from '../../common/HeaderSettings';
// 导入保存图片所需的toPng函数
import { toPng } from 'html-to-image';

// Node component for the mind map
const MindMapNode = ({ data, isConnectable }: { data: any, isConnectable?: boolean }) => {
  return (
    <div 
      className={styles.mindMapNode} 
      style={{ 
        backgroundColor: data.color || '#ffffff',
        borderColor: data.highlighted ? '#ff9900' : '#ccc',
        boxShadow: data.highlighted ? '0 0 10px rgba(255, 153, 0, 0.5)' : 'none'
      }}
      onDoubleClick={data.onDoubleClick}
    >
      {/* 目标连接点（左侧） */}
      <Handle
        type="target"
        position={Position.Left}
        id="target"
        isConnectable={isConnectable}
      />
      
      <div className={styles.nodeContent}>
        {data.name}
        {data.hasChildren && (
          <button 
            className={`${styles.collapseButton} ${data.collapsed ? styles.collapsed : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              data.onToggleCollapse(data.id);
            }}
          >
            {data.collapsed ? '+' : '-'}
          </button>
        )}
      </div>
      
      {/* 源连接点（右侧） */}
      <Handle
        type="source"
        position={Position.Right}
        id="source"
        isConnectable={isConnectable}
      />
    </div>
  );
};

// Color picker component
const ColorPicker = ({ onSelect, onClose }: { onSelect: (color: string) => void, onClose: () => void }) => {
  const colors = [
    '#ffffff', '#f8d7da', '#d1e7dd', '#cfe2ff', 
    '#fff3cd', '#d3d3d3', '#e2e3e5', '#ffeaa7', 
    '#81ecec', '#74b9ff', '#a29bfe', '#fab1a0'
  ];

  return (
    <div className={styles.colorPickerOverlay} onClick={onClose}>
      <div className={styles.colorPicker} onClick={(e) => e.stopPropagation()}>
        <div className={styles.colorPickerTitle}>Select Color</div>
        <div className={styles.colorGrid}>
          {colors.map((color, index) => (
            <div 
              key={index}
              className={styles.colorOption}
              style={{ backgroundColor: color }}
              onClick={() => onSelect(color)}
            />
          ))}
        </div>
        <button className={styles.closeButton} onClick={onClose}>Cancel</button>
      </div>
    </div>
  );
};

// Node Actions Menu component
const NodeActionsMenu = ({ 
  position,
  onAdd, 
  onEdit, 
  onHighlight, 
  onClose 
}: { 
  position: { x: number, y: number }, 
  onAdd: () => void, 
  onEdit: () => void, 
  onHighlight: () => void, 
  onClose: () => void 
}) => {
  return (
    <div 
      className={styles.nodeActionsOverlay}
      onClick={onClose}
    >
      <div 
        className={styles.nodeActions}
        style={{ left: position.x, top: position.y }}
        onClick={(e) => e.stopPropagation()}
      >
        <button onClick={onAdd}>Add New</button>
        <button onClick={onEdit}>Modify</button>
        <button onClick={onHighlight}>Highlight</button>
      </div>
    </div>
  );
};

// Edit Node modal
const EditNodeModal = ({ 
  initialValue, 
  onSave, 
  onClose 
}: { 
  initialValue: string, 
  onSave: (value: string) => void, 
  onClose: () => void 
}) => {
  const [value, setValue] = useState(initialValue);
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && value.trim()) {
      onSave(value);
    }
  };

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
        <h3>{initialValue ? 'Edit Node' : 'Add New Node'}</h3>
        <input 
          type="text" 
          value={value} 
          onChange={(e) => setValue(e.target.value)} 
          onKeyDown={handleKeyDown}
          autoFocus 
          className={styles.input}
          placeholder="Enter node text"
        />
        <div className={styles.modalButtons}>
          <button onClick={() => onSave(value)} disabled={!value.trim()}>Save</button>
          <button onClick={onClose}>Cancel</button>
        </div>
      </div>
    </div>
  );
};

// Main MindMap component
interface MindMapData {
  name: string;
  children?: MindMapData[];
}

interface MindMapProps {
  data: MindMapData[];
  onExportSuccess?: (dataUrl: string) => void;
  onExportError?: (error: Error) => void;
}

// 导出接口
export interface MindMapRef {
  exportToPng: () => void;
}

// 确保TypeScript正确识别React Flow中的节点和边的类型
declare module '@xyflow/react' {
  export function useNodesState<T = Node>(
    initialNodes?: T[]
  ): [T[], React.Dispatch<React.SetStateAction<T[]>>, (changes: NodeChange[]) => void];

  export function useEdgesState<T = Edge>(
    initialEdges?: T[]
  ): [T[], React.Dispatch<React.SetStateAction<T[]>>, (changes: EdgeChange[]) => void];
}

// Constants for layout
const NODE_HEIGHT = 40;
const BASE_HORIZONTAL_SPACING = 150; // 基础固定间距
const VERTICAL_SPACING = 100;

const nodeTypes: NodeTypes = {
  mindMap: MindMapNode
};

// 使用forwardRef包装组件
const MindMap = forwardRef<MindMapRef, MindMapProps>(({ data, onExportSuccess, onExportError }, ref) => {
  const [nodes, setNodes, onNodesChange] = useNodesState<Node>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [showActionsMenu, setShowActionsMenu] = useState(false);
  const [actionMenuPosition, setActionMenuPosition] = useState({ x: 0, y: 0 });
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [isAddingNode, setIsAddingNode] = useState(false);
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(new Set());
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const layoutInitialized = useRef(false);
  // console.log(data,'data------')

  // 导出思维导图为PNG函数
  const exportToPng = useCallback(() => {
    if (!reactFlowWrapper.current) {
      onExportError && onExportError(new Error('无法找到思维导图容器'));
      return;
    }

    // 确保视图适合内容
    reactFlowInstance?.fitView({ padding: 0.2 });

    // 使用html-to-image库将DOM转为图片
    toPng(reactFlowWrapper.current, {
      backgroundColor: '#ffffff',
      quality: 1.0,
      pixelRatio: 2,
      filter: (node) => {
        // 过滤掉控制面板、背景等UI元素
        return !(
          node.classList?.contains('react-flow__controls') ||
          node.classList?.contains('react-flow__background') ||
          node.classList?.contains('react-flow__panel') ||
          node.classList?.contains('react-flow__attribution')
        );
      },
      style: {
        // 确保思维导图在导出时完全渲染
        height: `${reactFlowWrapper.current.clientHeight}px`,
        width: `${reactFlowWrapper.current.clientWidth}px`,
      }
    })
      .then((dataUrl: string) => {
        // 成功导出后调用回调
        onExportSuccess && onExportSuccess(dataUrl);
      })
      .catch((error: Error) => {
        console.error('导出思维导图失败:', error);
        onExportError && onExportError(error);
      });
  }, [reactFlowInstance, onExportSuccess, onExportError]);

  // 暴露导出方法给外部
  useImperativeHandle(ref, () => ({
    exportToPng
  }), [exportToPng]);

  // 处理节点折叠/展开
  const handleToggleCollapse = useCallback((nodeId: string) => {
    setCollapsedNodes(prev => {
      const newCollapsed = new Set(prev);
      if (newCollapsed.has(nodeId)) {
        newCollapsed.delete(nodeId);
      } else {
        newCollapsed.add(nodeId);
      }
      return newCollapsed;
    });
  }, []);

  // 检查节点是否应该可见（考虑祖先节点的折叠状态）
  const isNodeVisible = useCallback((nodeId: string): boolean => {
    // 根节点始终可见
    const parentEdge = edges.find(e => e.target === nodeId);
    if (!parentEdge) return true;
    
    // 如果任何祖先节点被折叠，则该节点不可见
    let currentParentId = parentEdge.source;
    while (currentParentId) {
      if (collapsedNodes.has(currentParentId)) {
        return false;
      }
      
      const grandParentEdge = edges.find(e => e.target === currentParentId);
      if (!grandParentEdge) break;
      
      currentParentId = grandParentEdge.source;
    }
    
    return true;
  }, [edges, collapsedNodes]);

  // 获取可见的节点和边
  const getVisibleElements = useCallback(() => {
    // 确定哪些节点应该可见
    const visibleNodeIds = new Set<string>();
    
    nodes.forEach(node => {
      if (isNodeVisible(node.id)) {
        visibleNodeIds.add(node.id);
      }
    });
    
    // 过滤节点和边
    const visibleNodes = nodes.map(node => {
      const isVisible = visibleNodeIds.has(node.id);
      
      // 检查此节点是否有子节点
      const hasChildren = edges.some(edge => edge.source === node.id);
      
      if (isVisible) {
        return {
          ...node,
          data: {
            ...node.data,
            hasChildren,
            collapsed: collapsedNodes.has(node.id),
            onToggleCollapse: handleToggleCollapse
          }
        };
      }
      return node;
    }).filter(node => visibleNodeIds.has(node.id));
    
    // visibleEdges变量未被使用，但我们仍然保留它以供将来可能的用途
    // const visibleEdges = edges.filter(
    //   edge => visibleNodeIds.has(edge.source) && visibleNodeIds.has(edge.target)
    // );
    
    return { visibleNodes };
  }, [nodes, edges, isNodeVisible, collapsedNodes, handleToggleCollapse]);

  // Calculate node positions based on hierarchy
  const calculateLayout = useCallback((nodes: Node[], edges: Edge[]) => {
    // Build parent-child relationships
    const childrenMap = new Map<string, string[]>();
    const parentMap = new Map<string, string>();
    
    // Initialize maps
    nodes.forEach(node => {
      childrenMap.set(node.id, []);
    });
    
    // Fill relationships
    edges.forEach(edge => {
      const { source, target } = edge;
      const children = childrenMap.get(source) || [];
      childrenMap.set(source, [...children, target]);
      parentMap.set(target, source);
    });
    
    // Find root nodes (without parents)
    const rootNodes = nodes.filter(node => !parentMap.has(node.id)).map(node => node.id);
    
    // Calculate levels for each node using BFS
    const levels = new Map<string, number>();
    const queue: { id: string; level: number }[] = rootNodes.map(id => ({ id, level: 0 }));
    
    while (queue.length > 0) {
      const { id, level } = queue.shift()!;
      levels.set(id, level);
      
      const children = childrenMap.get(id) || [];
      children.forEach(childId => {
        queue.push({ id: childId, level: level + 1 });
      });
    }
    
    // Count children per level for each node
    const levelCounts = new Map<string, Map<number, number>>();
    const nodePositionInLevel = new Map<string, number>();
    
    // Initialize level counts
    nodes.forEach(node => {
      const level = levels.get(node.id) || 0;
      const parentId = parentMap.get(node.id);
      
      if (parentId) {
        if (!levelCounts.has(parentId)) {
          levelCounts.set(parentId, new Map<number, number>());
        }
        const parentLevelMap = levelCounts.get(parentId)!;
        const currentCount = parentLevelMap.get(level) || 0;
        parentLevelMap.set(level, currentCount + 1);
        nodePositionInLevel.set(node.id, currentCount);
      }
    });
    
    // Calculate total children per node
    const totalChildren = new Map<string, number>();
    
    const countTotalChildren = (nodeId: string): number => {
      if (totalChildren.has(nodeId)) {
        return totalChildren.get(nodeId)!;
      }
      
      const children = childrenMap.get(nodeId) || [];
      let count = children.length;
      
      for (const childId of children) {
        count += countTotalChildren(childId);
      }
      
      totalChildren.set(nodeId, count);
      return count;
    };
    
    rootNodes.forEach(rootId => {
      countTotalChildren(rootId);
    });
    
    // Calculate node positions
    const newPositions = new Map<string, { x: number; y: number }>();
    
    // First pass: set positions for root nodes
    rootNodes.forEach((rootId, index) => {
      newPositions.set(rootId, { 
        x: 50, 
        y: 100 + index * Math.max(300, (totalChildren.get(rootId) || 0) * 70)
      });
    });
    
    // Helper function for calculating node vertical positions
    const calculateYPosition = (nodeId: string, parentPosition: { x: number; y: number }) => {
      const children = childrenMap.get(nodeId) || [];
      const totalChildCount = totalChildren.get(nodeId) || 0;
      
      if (children.length === 0) return;
      
      // 计算该层级节点的最长内容长度
      const childNodesData = children.map(childId => {
        const childNode = nodes.find(node => node.id === childId);
        return childNode?.data?.name || '';
      });
      
      // 估算内容的长度（这里简单用字符长度，实际可能需要更复杂的计算）
      const maxContentLength = Math.max(...childNodesData.map(name => String(name).length));
      
      // 根据内容长度计算动态的水平间距
      const dynamicSpacing = BASE_HORIZONTAL_SPACING + Math.min(200, maxContentLength * 10);
      
      // When a parent has only one child, place it at the same y-level
      if (children.length === 1) {
        const childId = children[0];
        const childPosition = { 
          x: parentPosition.x + dynamicSpacing, 
          y: parentPosition.y 
        };
        newPositions.set(childId, childPosition);
        calculateYPosition(childId, childPosition);
        return;
      }
      
      // Calculate total height needed
      const totalHeight = Math.max(VERTICAL_SPACING * (children.length - 1), VERTICAL_SPACING * totalChildCount * 0.6);
      const startY = parentPosition.y - totalHeight / 2;
      
      children.forEach((childId, index) => {
        const childTotalChildren = totalChildren.get(childId) || 0;
        const weight = Math.max(1, childTotalChildren);
        const totalWeight = children.reduce((sum, id) => sum + Math.max(1, totalChildren.get(id) || 0), 0);
        const ratio = weight / totalWeight;
        
        // Calculate position based on weighted distribution
        const prevWeights = children.slice(0, index).reduce(
          (sum, id) => sum + Math.max(1, totalChildren.get(id) || 0),
          0
        );
        const prevRatio = prevWeights / totalWeight;
        const yPos = startY + prevRatio * totalHeight + ratio * totalHeight / 2;
        
        const childPosition = { 
          x: parentPosition.x + dynamicSpacing, 
          y: yPos 
        };
        newPositions.set(childId, childPosition);
        calculateYPosition(childId, childPosition);
      });
    };
    
    // Calculate positions for all nodes
    rootNodes.forEach(rootId => {
      const rootPosition = newPositions.get(rootId)!;
      calculateYPosition(rootId, rootPosition);
    });
    
    // Update node positions
    const updatedNodes = nodes.map(node => {
      const position = newPositions.get(node.id);
      if (position) {
        return {
          ...node,
          position
        };
      }
      return node;
    });
    
    return updatedNodes;
  }, []);

  // Initialize the mind map from the input data
  const initializeGraph = useCallback(() => {
    const initialNodes: Node[] = [];
    const initialEdges: Edge[] = [];
    let nodeId = 1;

    const processNode = (node: MindMapData, parentId: string | null = null): string => {
      const currentId = `node-${nodeId++}`;
      
      initialNodes.push({
        id: currentId,
        type: 'mindMap',
        position: { x: 0, y: 0 }, // Temporary position
        data: {
          id: currentId, // 添加ID到数据中
          name: node.name,
          color: '#ffffff',
          highlighted: false,
          hasChildren: node.children && node.children.length > 0,
          collapsed: false,
          onToggleCollapse: handleToggleCollapse,
          onDoubleClick: (event: React.MouseEvent) => handleNodeDoubleClick(event, currentId)
        },
        sourcePosition: Position.Right,
        targetPosition: Position.Left
      });

      if (parentId) {
        initialEdges.push({
          id: `edge-${parentId}-${currentId}`,
          source: parentId,
          target: currentId,
          type: 'smoothstep',
          markerEnd: {
            type: MarkerType.ArrowClosed,
          },
          style: { stroke: '#888' }
        });
      }

      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => {
          processNode(child, currentId);
        });
      }

      return currentId;
    };

    // Process each root node
    data.forEach((rootNode) => {
      processNode(rootNode, null);
    });

    // Calculate positions
    const positionedNodes = calculateLayout(initialNodes, initialEdges);
    
    setNodes(positionedNodes);
    setEdges(initialEdges);
  }, [data, setNodes, setEdges, calculateLayout, handleToggleCollapse]);

  React.useEffect(() => {
    initializeGraph();
  }, [initializeGraph]);

  // Handle node double click to show actions menu
  const handleNodeDoubleClick = (event: React.MouseEvent, nodeId: string) => {
    event.stopPropagation();
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      setSelectedNode(node);
      setActionMenuPosition({ 
        x: event.clientX, 
        y: event.clientY 
      });
      setShowActionsMenu(true);
    }
  };

  // Handle adding a new edge between nodes
  const onConnect = useCallback(
    (connection: Connection) => {
      // Prevent creating cycles or multiple parents
      const existingParentEdge = edges.find(e => e.target === connection.target);
      
      if (existingParentEdge) {
        // Remove the existing edge to the target
        setEdges(edges => edges.filter(e => e.id !== existingParentEdge.id));
      }
      
      const newEdges = addEdge({
        ...connection,
        type: 'smoothstep',
        markerEnd: {
          type: MarkerType.ArrowClosed,
        },
        style: { stroke: '#888' }
      }, edges);
      
      setEdges(newEdges);
      
      // 不再自动重新计算布局
      // setTimeout(() => {
      //   setNodes(nodes => calculateLayout(nodes, newEdges));
      // }, 50);
    },
    [edges, setEdges]
  );

  // Handle node drag & drop
  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // Handle node deletion
  const handleDeleteNode = useCallback((nodeId: string) => {
    // Get all descendant nodes to remove
    const nodesToRemove = new Set<string>();
    
    const findDescendants = (id: string) => {
      nodesToRemove.add(id);
      edges.forEach(edge => {
        if ((edge as Edge).source === id) {
          findDescendants((edge as Edge).target);
        }
      });
    };
    
    findDescendants(nodeId);
    
    // Remove nodes and their connecting edges
    const newNodes = nodes.filter(node => !nodesToRemove.has((node as Node).id));
    const newEdges = edges.filter(edge => 
      !nodesToRemove.has((edge as Edge).source) && !nodesToRemove.has((edge as Edge).target)
    );
    
    setNodes(newNodes);
    setEdges(newEdges);
  }, [edges, nodes, setNodes, setEdges]);

  // After nodes change, update the layout
  useEffect(() => {
    if (nodes.length > 0 && edges.length > 0 && !layoutInitialized.current) {
      const timer = setTimeout(() => {
        setNodes(nodes => calculateLayout(nodes, edges));
        layoutInitialized.current = true;
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [nodes.length, edges.length, calculateLayout, setNodes, edges]);

  // Actions menu handlers
  const handleAddNode = () => {
    setIsAddingNode(true);
    setShowEditModal(true);
    setShowActionsMenu(false);
  };

  const handleEditNode = () => {
    setIsAddingNode(false);
    setShowEditModal(true);
    setShowActionsMenu(false);
  };

  const handleHighlightNode = () => {
    setShowColorPicker(true);
    setShowActionsMenu(false);
  };

  // Find an empty position for a new child node
  const findEmptyPosition = useCallback((parentId: string) => {
    const parentNode = nodes.find(n => n.id === parentId);
    if (!parentNode) return { x: 0, y: 0 };
    
    const childEdges = edges.filter(e => e.source === parentId);
    const childNodes = childEdges.map(e => nodes.find(n => n.id === e.target)).filter(Boolean) as Node[];
    
    // 获取所有子节点内容，估算最长长度
    const childContents = childNodes.map(node => String(node?.data?.name || ''));
    const maxContentLength = Math.max(...childContents.map(name => name.length), 0);
    
    // 计算动态间距
    const dynamicSpacing = BASE_HORIZONTAL_SPACING + Math.min(200, maxContentLength * 10);
    
    // Default position to the right of parent
    let newX = parentNode.position.x + dynamicSpacing;
    let newY = parentNode.position.y;
    
    // If parent has children, find a suitable Y position
    if (childNodes.length > 0) {
      // Sort children by Y position
      const sortedChildren = [...childNodes].sort((a, b) => a.position.y - b.position.y);
      
      // Try to find a gap between children
      let foundGap = false;
      for (let i = 0; i < sortedChildren.length - 1; i++) {
        const current = sortedChildren[i];
        const next = sortedChildren[i + 1];
        const gap = next.position.y - current.position.y;
        
        if (gap > NODE_HEIGHT * 2) {
          newY = current.position.y + gap / 2;
          foundGap = true;
          break;
        }
      }
      
      // If no suitable gap, place below the last child
      if (!foundGap) {
        newY = sortedChildren[sortedChildren.length - 1].position.y + VERTICAL_SPACING;
      }
    }
    
    return { x: newX, y: newY };
  }, [nodes, edges]);

  // Save edited node text
  const handleSaveNodeText = (newText: string) => {
    if (selectedNode) {
      setNodes(nodes.map(node => {
        if (node.id === selectedNode.id) {
          // Update existing node
          return {
            ...node,
            data: {
              ...node.data,
              name: newText
            }
          };
        }
        return node;
      }));

      // If adding a new node
      if (isAddingNode) {
        // Create a new node
        const newNodeId = `node-${Date.now()}`;
        const parentNode = nodes.find(n => n.id === selectedNode.id);
        
        if (parentNode) {
          // Calculate position for the new node
          const newPosition = findEmptyPosition(selectedNode.id);
          
          // Add the new node
          const newNode: Node = {
            id: newNodeId,
            type: 'mindMap',
            position: newPosition,
            data: {
              name: newText,
              color: '#ffffff',
              highlighted: false,
              onDoubleClick: (event: React.MouseEvent) => handleNodeDoubleClick(event, newNodeId)
            },
            sourcePosition: Position.Right,
            targetPosition: Position.Left
          };
          
          const newNodes = [...nodes, newNode];
          
          // Connect the new node to the parent
          const newEdge: Edge = {
            id: `edge-${selectedNode.id}-${newNodeId}`,
            source: selectedNode.id,
            target: newNodeId,
            type: 'smoothstep',
            markerEnd: {
              type: MarkerType.ArrowClosed,
            },
            style: { stroke: '#888' }
          };
          
          const newEdges = [...edges, newEdge];
          
          setNodes(newNodes);
          setEdges(newEdges);
          
          // 不再自动重新计算布局
          // setTimeout(() => {
          //   setNodes(nodes => calculateLayout(nodes, newEdges));
          // }, 50);
        }
      }
    }
    
    setShowEditModal(false);
  };

  // Handle color selection for highlighting
  const handleColorSelect = (color: string) => {
    if (selectedNode) {
      setNodes(nodes.map(node => {
        if (node.id === selectedNode.id) {
          return {
            ...node,
            data: {
              ...node.data,
              color: color,
              highlighted: color !== '#ffffff'
            }
          };
        }
        return node;
      }));
    }
    setShowColorPicker(false);
  };

  // UI Elements for keyboard shortcuts
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Delete' || event.key === 'Backspace') {
      const selectedNodes = nodes.filter(node => node.selected);
      selectedNodes.forEach(node => handleDeleteNode(node.id));
    }
  }, [nodes, handleDeleteNode]);

  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Handle node drag end
  const onNodeDragStop = useCallback(() => {
    // 不再自动重新计算布局，保留用户拖拽后的位置
    // setTimeout(() => {
    //   setNodes(nodes => calculateLayout(nodes, edges));
    // }, 50);
  }, []);

  // 当折叠状态改变时，更新节点
  useEffect(() => {
    if (nodes.length > 0) {
      const { visibleNodes } = getVisibleElements();
      // 这里不直接设置nodes和edges，避免触发重新布局
      // 而是更新现有nodes的data
      setNodes(prevNodes => 
        prevNodes.map(node => {
          const updatedNode = visibleNodes.find(n => n.id === node.id);
          if (updatedNode) {
            return {
              ...node,
              data: updatedNode.data
            };
          }
          return node;
        })
      );
    }
  }, [collapsedNodes, nodes.length, getVisibleElements, setNodes]);

  return (
    <div className={styles.mindMapContainer} ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes.filter(node => isNodeVisible(node.id))}
        edges={edges.filter(edge => isNodeVisible(edge.source) && isNodeVisible(edge.target))}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={setReactFlowInstance}
        nodeTypes={nodeTypes}
        connectionLineType={ConnectionLineType.SmoothStep}
        onDragOver={onDragOver}
        onNodeDragStop={onNodeDragStop}
        fitView
      >
        <Background />
        <Controls>
          <ControlButton onClick={exportToPng} title="导出为图片">
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              fontSize: '18px' 
            }}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 16L12 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M9 11L12 8 15 11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3 15L3 16C3 18.2091 4.79086 20 7 20L17 20C19.2091 20 21 18.2091 21 16L21 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </ControlButton>
        </Controls>
        <Panel position="top-right">
          <HeaderSettings />
        </Panel>
        <Panel position="top-left">
          <button 
            className={styles.layoutButton}
            onClick={() => setNodes(nodes => calculateLayout(nodes, edges))}
          >
            重新布局
          </button>
        </Panel>
      </ReactFlow>

      {showActionsMenu && (
        <NodeActionsMenu
          position={actionMenuPosition}
          onAdd={handleAddNode}
          onEdit={handleEditNode}
          onHighlight={handleHighlightNode}
          onClose={() => setShowActionsMenu(false)}
        />
      )}

      {showColorPicker && (
        <ColorPicker
          onSelect={handleColorSelect}
          onClose={() => setShowColorPicker(false)}
        />
      )}

      {showEditModal && selectedNode && (
        <EditNodeModal
          initialValue={isAddingNode ? '' : (selectedNode.data.name as string)}
          onSave={handleSaveNodeText}
          onClose={() => setShowEditModal(false)}
        />
      )}
    </div>
  );
});

export default MindMap;