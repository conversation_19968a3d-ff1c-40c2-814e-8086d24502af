import React, { useState, useRef, useEffect } from 'react';
import { Input, Button, Tooltip } from 'antd';
import { DownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import styles from '../PodcastGen.module.css';

const { TextArea } = Input;

interface CollapsibleInputProps {
  initialCollapsed?: boolean;
  onInputChange: (value: string) => void;
  onGenerateClick: () => void;
  inputValue: string;
  isLoading: boolean;
  instruction?: string;
  isAudioProcessing?: boolean;
}

const CollapsibleInput: React.FC<CollapsibleInputProps> = ({
  initialCollapsed = false,
  onInputChange,
  onGenerateClick,
  inputValue,
  isLoading,
  instruction,
  isAudioProcessing = false,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(initialCollapsed);
  const textAreaRef = useRef<any>(null);

  useEffect(() => {
    if (!isCollapsed && textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [isCollapsed]);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onInputChange(e.target.value);
  };

  return (
    <div className={styles.collapsibleInput}>
      <div
        className={styles.collapsibleHeader}
        onClick={toggleCollapse}
      >
        <h3>主题/文本输入</h3>
        <DownOutlined
          className={classNames(styles.toggleIcon, {
            [styles.rotated]: !isCollapsed,
          })}
        />
      </div>
      <div
        className={classNames(styles.collapsibleContent, {
          [styles.collapsed]: isCollapsed,
        })}
      >
        <div className={styles.inputWithTip}>
          <div className={styles.inputTip}>
            <span>输入播客主题，如科技创新、健康生活等</span>
            {instruction && (
              <Tooltip title={instruction}>
                <InfoCircleOutlined className={styles.infoIcon} />
              </Tooltip>
            )}
          </div>
          
          <TextArea
            ref={textAreaRef}
            value={inputValue}
            onChange={handleInputChange}
            placeholder="请输入播客主题内容，如科技创新、健康生活等"
            rows={6}
            style={{ width: '100%', marginBottom: 16 }}
          />
        </div>
      </div>
      
      <div className={styles.buttonGroupFixed}>
        <Button
          type="primary"
          onClick={onGenerateClick}
          disabled={!inputValue.trim() || isLoading || isAudioProcessing}
          loading={isLoading}
        >
          生成播客脚本
        </Button>
        {isAudioProcessing && (
          <div style={{ marginTop: '8px', color: '#ff4d4f', fontSize: '12px' }}>
            音频处理中，请等待完成后再生成新脚本
          </div>
        )}
      </div>
    </div>
  );
};

export default CollapsibleInput; 