import React from 'react';
import { Button, Typography, Space, Alert, Spin, List, Avatar, Tag, Progress, Card } from 'antd';
import { SoundOutlined, LoadingOutlined, MergeCellsOutlined, PlayCircleOutlined, AudioOutlined, RobotOutlined } from '@ant-design/icons';
import { PodcastContentItem } from '../types';
// import { parseScriptContent } from '../services/scriptService';
import styles from '../PodcastGen.module.css';

const { Title, Text } = Typography;

interface ScriptInterfaceProps {
  streamContent: string;
  podcastContent: PodcastContentItem[];
  isGeneratingScript: boolean;
  isGeneratingAudio: boolean;
  isMergingAudio: boolean;
  // scriptReady: boolean;
  generateProgress: number;
  // shouldAutoScroll: boolean;
  mergedAudioUrl?: string;
  // onBackToInput: () => void;
  onGenerateAudio: () => void;
  onMergeAudio: () => void;
  // onToggleAutoScroll: () => void;
  // onManualParse: (content: PodcastContentItem[]) => void;
  onGenerateSingleAudio: (index: number) => void;
  containerRef: React.RefObject<HTMLDivElement>;
  hasRecordings?: boolean;
  onRecordVoices?: () => void;
  onGenerateClonedAudio?: () => void;
}

const ScriptInterface: React.FC<ScriptInterfaceProps> = ({
  streamContent,
  podcastContent,
  isGeneratingScript,
  isGeneratingAudio,
  isMergingAudio,
  // scriptReady,
  generateProgress,
  // shouldAutoScroll,
  mergedAudioUrl,
  // onBackToInput,
  onGenerateAudio,
  onMergeAudio,
  // onToggleAutoScroll,
  // onManualParse,
  onGenerateSingleAudio,
  containerRef,
  hasRecordings,
  onRecordVoices,
  onGenerateClonedAudio
}) => {
  // 手动尝试解析脚本内容
  // const handleManualParse = () => {
  //   try {
  //     const parsedContent = parseScriptContent(streamContent);
  //     onManualParse(parsedContent);
  //   } catch (error: any) {
  //     console.error('手动解析失败:', error);
  //     alert('无法解析为有效的播客脚本，请重新生成');
  //   }
  // };

  // 渲染脚本列表内容
  const renderScriptContent = () => {
    // 正在生成脚本，显示加载状态
    if (isGeneratingScript) {
      return (
        <>
          <div className={styles.loadingContainer}>
            <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
            <Text>正在生成播客脚本...</Text>
          </div>
          
          {/* 如果有流式内容，但尚未解析出对话，则直接显示流式内容 */}
          {streamContent && !podcastContent.length && (
            <div className={styles.streamContent}>
              <pre>{streamContent}</pre>
            </div>
          )}
        </>
      );
    }

    // 如果已经解析出对话内容
    if (podcastContent.length > 0) {
      return (
        <div>
          <div className={styles.scriptHeader}>
            <Title level={4} className={styles.scriptTitle}>播客脚本预览</Title>
            
            <Space>
              {/* 语音克隆部分 */}
              {onRecordVoices && (
                <Button
                  type="default"
                  icon={<AudioOutlined />}
                  onClick={onRecordVoices}
                  disabled={isGeneratingAudio || isMergingAudio}
                >
                  录制角色声音
                </Button>
              )}
              
              {/* 语音克隆生成按钮 */}
              {hasRecordings && onGenerateClonedAudio && (
                <Button
                  type="primary"
                  ghost
                  icon={<RobotOutlined />}
                  onClick={onGenerateClonedAudio}
                  loading={isGeneratingAudio}
                  disabled={isGeneratingAudio || isMergingAudio}
                  title="使用已录制的角色声音生成克隆语音，未录制的角色将被跳过"
                >
                  生成克隆语音
                </Button>
              )}
              
              {/* 自动生成按钮 */}
              <Button
                type="primary"
                icon={<SoundOutlined />}
                onClick={onGenerateAudio}
                loading={isGeneratingAudio}
                disabled={isGeneratingAudio || isMergingAudio}
                className={styles.audioButton}
              >
                生成AI语音
              </Button>
              
              {/* 合并音频按钮 */}
              <Button
                type="primary"
                onClick={onMergeAudio}
                icon={<MergeCellsOutlined />}
                loading={isMergingAudio}
                disabled={isGeneratingAudio || isMergingAudio || !podcastContent.some(item => item.audioUrl)}
                className={styles.mergeButton}
              >
                合并音频
              </Button>
            </Space>
          </div>
          
          <Alert
            message="脚本生成成功"
            description={`已成功解析 ${podcastContent.length} 条对话内容，您可以点击右上角的按钮生成语音。生成完成后将自动合并所有音频。`}
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          {isGeneratingAudio && (
            <div className={styles.progressContainer}>
              <Progress 
                percent={generateProgress} 
                status="active" 
              />
              <Text type="secondary">
                已完成 {Math.round(generateProgress)}%，
                按顺序生成中，请耐心等待...
              </Text>
            </div>
          )}
          
          <List
            className={styles.dialogueList}
            itemLayout="horizontal"
            dataSource={podcastContent}
            renderItem={(item, index) => (
              <List.Item className={styles.dialogueItem}>
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      style={{ 
                        backgroundColor: item.role === 'Alice' ? '#ff85c0' : '#1890ff',
                        color: '#fff'
                      }}
                    >
                      {item.role}
                    </Avatar>
                  }
                  title={
                    <div className={styles.dialogueTitle}>
                      <Text strong>{index + 1}</Text>
                      <Tag color={item.role === 'Alice' ? 'pink' : 'blue'}>
                        {item.role === '女' ? '主持人' : '嘉宾'}
                      </Tag>
                    </div>
                  }
                  description={
                    <div className={styles.dialogueContent}>
                      {item.content}
                    </div>
                  }
                />
                <div className={styles.audioControls}>
                  {item.isGenerating ? (
                    <div>
                      <Spin size="small" />
                      <Text type="secondary" style={{ marginLeft: 8 }}>
                        {item.status || '生成中...'}
                      </Text>
                    </div>
                  ) : item.audioUrl ? (
                    <div>
                      <audio 
                        controls 
                        src={item.audioUrl} 
                        className={styles.dialogueAudio}
                      ></audio>
                    </div>
                  ) : isGeneratingAudio ? (
                    <Text type="secondary">
                      {item.status || '等待生成...'}
                    </Text>
                  ) : (
                    <Button 
                      size="small" 
                      icon={<SoundOutlined />}
                      onClick={() => onGenerateSingleAudio(index)}
                    >
                      生成此段
                    </Button>
                  )}
                </div>
              </List.Item>
            )}
          />
        </div>
      );
    }

    // 如果有流式内容，但尚未解析出对话，则显示流式内容
    if (streamContent) {
      return (
        <div className={styles.streamContent}>
          <pre>{streamContent}</pre>
        </div>
      );
    }

    // 没有任何内容，显示空状态
    return (
      <div className={styles.noContentMessage}>
        <Alert
          message="请输入主题描述并点击生成按钮"
          description="输入您想要讨论的主题，系统将自动生成播客脚本"
          type="info"
          showIcon
        />
      </div>
    );
  };

  // 渲染合并后的音频播放器
  const renderMergedAudio = () => {
    if (!mergedAudioUrl) return null;
    
    return (
      <div className={styles.mergedAudioSection}>
        <Card 
          title={<div><PlayCircleOutlined /> 完整音频</div>}
          className={styles.mergedAudioCard}
        >
          <div className={styles.audioPlayer}>
            <audio 
              controls 
              src={mergedAudioUrl} 
              className={styles.mergedAudio}
              style={{ width: '100%' }}
            ></audio>
            <div className={styles.audioInfo}>
              <a href={mergedAudioUrl} target="_blank" rel="noopener noreferrer">
                查看音频链接
              </a>
            </div>
          </div>
        </Card>
      </div>
    );
  };

  return (
    <div className={styles.scriptContainer}>
      <div className={styles.scriptContent} ref={containerRef}>
        {renderScriptContent()}
        {renderMergedAudio()}
      </div>
    </div>
  );
};

export default ScriptInterface; 