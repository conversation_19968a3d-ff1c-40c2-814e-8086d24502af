import React, { useState, useRef, useEffect } from 'react';
import { Modal, Button, Typography, Space, message, Progress, Alert, List } from 'antd';
import { AudioOutlined, AudioMutedOutlined, CheckCircleOutlined, DeleteOutlined, SoundOutlined } from '@ant-design/icons';
import type { PodcastContentItem } from '../types';
import styles from '../PodcastGen.module.css';


const { Text} = Typography;

// 提示文本，用户需要朗读这段文本作为声音样本
const PROMPT_TEXT = "2025年是AI科技爆发的元年";

// WAV格式转换函数
const convertToWav = async (audioBlob: Blob): Promise<Blob> => {
  console.log('开始转换WAV格式，原始Blob:', { type: audioBlob.type, size: audioBlob.size });
  
  return new Promise((resolve) => {
    try {
      // 创建音频上下文
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 44100 // 使用更高的采样率
      });
      console.log('音频上下文已创建');
      
      // 创建文件读取器
      const fileReader = new FileReader();
      
      fileReader.onload = async (event) => {
        try {
          const arrayBuffer = event.target?.result as ArrayBuffer;
          console.log('Blob读取成功，ArrayBuffer大小:', arrayBuffer.byteLength);
          
          // 解码音频数据
          try {
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            console.log('音频解码成功，频道数:', audioBuffer.numberOfChannels, '采样率:', audioBuffer.sampleRate);
            
            // 创建WAV格式数据
            const wavBuffer = createWavBuffer(audioBuffer);
            console.log('WAV缓冲区创建成功，大小:', wavBuffer.byteLength);
            
            // 创建WAV格式的Blob
            const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });
            console.log('WAV Blob创建成功，大小:', wavBlob.size);
            
            resolve(wavBlob);
          } catch (decodeError) {
            console.error('解码音频失败:', decodeError);
            // 如果解码失败，直接返回原始Blob
            message.warning('音频格式转换失败，将使用原始录音');
            resolve(audioBlob);
          }
        } catch (error) {
          console.error('处理音频数据失败:', error);
          // 任何错误都返回原始Blob而不是reject
          resolve(audioBlob);
        }
      };
      
      fileReader.onerror = (error) => {
        console.error('读取音频文件失败:', error);
        // 同样返回原始Blob
        resolve(audioBlob);
      };
      
      // 开始读取Blob数据
      fileReader.readAsArrayBuffer(audioBlob);
    } catch (error) {
      console.error('转换过程中发生错误:', error);
      // 最终保底，返回原始Blob
      resolve(audioBlob);
    }
  });
};

// 创建WAV格式的Buffer
const createWavBuffer = (audioBuffer: AudioBuffer): ArrayBuffer => {
  const numOfChannels = audioBuffer.numberOfChannels;
  const length = audioBuffer.length * numOfChannels * 2; // 2字节每样本
  const sampleRate = audioBuffer.sampleRate;
  
  // WAV文件头 + 数据大小
  const buffer = new ArrayBuffer(44 + length);
  const view = new DataView(buffer);
  
  // WAV文件头
  // "RIFF"标识
  writeString(view, 0, 'RIFF');
  // 文件大小
  view.setUint32(4, 36 + length, true);
  // "WAVE"标识
  writeString(view, 8, 'WAVE');
  // "fmt "子块
  writeString(view, 12, 'fmt ');
  // 子块大小
  view.setUint32(16, 16, true);
  // 音频格式 (1 表示 PCM)
  view.setUint16(20, 1, true);
  // 通道数
  view.setUint16(22, numOfChannels, true);
  // 采样率
  view.setUint32(24, sampleRate, true);
  // 字节率
  view.setUint32(28, sampleRate * numOfChannels * 2, true);
  // 数据块对齐
  view.setUint16(32, numOfChannels * 2, true);
  // 每个样本位数
  view.setUint16(34, 16, true);
  // "data"子块
  writeString(view, 36, 'data');
  // 数据大小
  view.setUint32(40, length, true);
  
  // 写入实际音频数据
  const channelData = [];
  for (let i = 0; i < numOfChannels; i++) {
    channelData.push(audioBuffer.getChannelData(i));
  }
  
  let offset = 44;
  for (let i = 0; i < audioBuffer.length; i++) {
    for (let channel = 0; channel < numOfChannels; channel++) {
      // 将-1.0 - 1.0的浮点值转换为-32768 - 32767的整数
      const sample = Math.max(-1, Math.min(1, channelData[channel][i]));
      const value = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(offset, value, true);
      offset += 2;
    }
  }
  
  return buffer;
};

// 辅助函数：将字符串写入DataView
const writeString = (view: DataView, offset: number, string: string): void => {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
};

interface VoiceCloneModalProps {
  visible: boolean;
//   onCancel: () => void;
  onComplete: (recordingsMap: Map<string, Blob>) => void;
  podcastContent: PodcastContentItem[];
  onGenerateClonedAudio?: () => void;
}

const VoiceCloneModal: React.FC<VoiceCloneModalProps> = ({
  visible,
//   onCancel,
  onComplete,
  podcastContent,
  onGenerateClonedAudio
}) => {
  // 使用useState保持所有状态
  const [recordingsMap, setRecordingsMap] = useState<Map<string, Blob>>(new Map());
  const [recordingRole, setRecordingRole] = useState<string | null>(null);
  const [playingRole, setPlayingRole] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordingDuration, setRecordingDuration] = useState<number>(0);
  const [volumeLevel, setVolumeLevel] = useState<number>(0);
  const [microphoneDetected, setMicrophoneDetected] = useState<boolean>(true);
  const [noSoundWarning, setNoSoundWarning] = useState<boolean>(false);
//   const [currentAudioUrl, setCurrentAudioUrl] = useState<string | null>(null);
  const [allRolesRecorded, setAllRolesRecorded] = useState<boolean>(false);
  
  // 使用useRef保持引用类型的数据
  const roles = Array.from(new Set(podcastContent.map(item => item.role)));
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioURLsRef = useRef<Map<string, string>>(new Map());
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const volumeMonitorTimerRef = useRef<number | null>(null);

  // 在组件卸载时清理资源
  useEffect(() => {
    return () => {
      stopVolumeMonitoring();
      stopAllAudioAndRecording();
      
      // 清理所有URL对象
      audioURLsRef.current.forEach(url => {
        URL.revokeObjectURL(url);
      });
    };
  }, []);

  // 检查是否所有角色都已录制
  useEffect(() => {
    const allRoles = new Set(roles);
    const recordedRoles = new Set(recordingsMap.keys());
    
    // 如果录制的角色数量与总角色数量相同，且每个录制的角色都在角色列表中
    const allRecorded = allRoles.size === recordedRoles.size && 
                        [...recordedRoles].every(role => allRoles.has(role));
    
    setAllRolesRecorded(allRecorded);
  }, [recordingsMap, roles]);

  // 设置音量监测
  const setupVolumeMonitoring = (stream: MediaStream) => {
    try {
      console.log('设置音量监测...');
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      
      // 配置分析器参数
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.8;
      microphone.connect(analyser);
      
      analyserRef.current = analyser;
      
      // 创建数据数组
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      
      // 设置定期检查音量的函数
      const checkVolume = () => {
        if (analyserRef.current) {
          analyserRef.current.getByteFrequencyData(dataArray);
          
          // 计算平均音量
          let sum = 0;
          for (let i = 0; i < dataArray.length; i++) {
            sum += dataArray[i];
          }
          const average = sum / dataArray.length;
          const volume = Math.round((average / 255) * 100); // 转换为0-100范围
          setVolumeLevel(volume);
          
          // 检测是否有声音
          if (isRecording && recordingDuration > 1 && volume < 5) {
            setNoSoundWarning(true);
          } else {
            setNoSoundWarning(false);
          }
          
          // 继续监测
          if (isRecording) {
            volumeMonitorTimerRef.current = window.requestAnimationFrame(checkVolume);
          }
        }
      };
      
      // 启动音量监测
      volumeMonitorTimerRef.current = window.requestAnimationFrame(checkVolume);
      console.log('音量监测已启动');
      
    } catch (error) {
      console.error('设置音量监测失败:', error);
    }
  };

  // 停止音量监测
  const stopVolumeMonitoring = () => {
    if (volumeMonitorTimerRef.current) {
      window.cancelAnimationFrame(volumeMonitorTimerRef.current);
      volumeMonitorTimerRef.current = null;
    }
    
    if (analyserRef.current) {
      analyserRef.current = null;
    }
    
    setVolumeLevel(0);
  };

  // 开始录音 - 参考VoiceClone中的方法
  const startRecording = async (role: string) => {
    try {
      console.log(`开始为角色 "${role}" 录音...`);
      
      // 如果存在之前的录音，先停止
      if (isRecording) {
        stopRecording();
      }
      
      // 获取媒体流，使用更高质量的设置
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          channelCount: 1,
          sampleRate: 44100, // 使用更高的采样率
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });
      
      // 保存流的引用，以便后续停止
      streamRef.current = stream;
      setMicrophoneDetected(true);
      
      // 设置音量监测
      setupVolumeMonitoring(stream);
      
      // 尝试使用常见的音频格式
      let options;
      
      // 检测浏览器支持的音频格式
      if (MediaRecorder.isTypeSupported('audio/webm')) {
        options = { mimeType: 'audio/webm' };
      } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
        options = { mimeType: 'audio/mp4' };
      } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
        options = { mimeType: 'audio/ogg; codecs=opus' };
      } else {
        // 使用默认格式
        options = {};
      }
        
      console.log('使用录音格式:', options.mimeType || '默认格式');
      const mediaRecorder = new MediaRecorder(stream, options);
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        // 将录音数据组合成一个Blob，使用媒体记录器的实际类型
        const mimeType = mediaRecorder.mimeType || 'audio/webm';
        const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
        
        console.log('录音完成, Blob 信息:', {
          type: audioBlob.type,
          size: audioBlob.size,
          mimeType: mimeType
        });
        
        // 检查录音是否太小(可能没声音)
        if (audioBlob.size < 1000) {
          message.warning(`录音似乎太短或没有声音，大小仅有 ${audioBlob.size} 字节`);
        }
        
        // 停止音量监测
        stopVolumeMonitoring();
        
        // 停止所有轨道以释放麦克风
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
        
        // 处理并保存录音
        await processAndSaveRecording(role, audioBlob);
      };

      // 每1秒获取一次录音数据
      mediaRecorder.start(1000);
      setIsRecording(true);
      setRecordingRole(role);
      setRecordingDuration(0);
      
      // 设置计时器来更新录制时间
      timerRef.current = setInterval(() => {
        setRecordingDuration(prev => {
          const newDuration = prev + 1;
          return newDuration;
        });
      }, 1000);
      
      message.success(`开始为"${role}"录音`);
    } catch (error: any) {
      console.error('录音失败:', error);
      message.error(`录音失败: ${error.message || '无法访问麦克风'}`);
      setMicrophoneDetected(false);
      setIsRecording(false);
      setRecordingRole(null);
    }
  };
  
  // 处理并保存录音
  const processAndSaveRecording = async (role: string, audioBlob: Blob) => {
    try {
      console.log(`处理并保存角色 "${role}" 的录音...`);
      
      // 转换为WAV格式
      console.log('转换为WAV格式...');
      const wavBlob = await convertToWav(audioBlob);
      console.log('WAV转换完成:', { size: wavBlob.size, type: wavBlob.type });
      
      // 检查WAV文件大小
      if (wavBlob.size < 1000) {
        console.warn('WAV文件大小异常小:', wavBlob.size);
        message.warning('录音文件可能没有声音，请检查您的麦克风');
      }
      
      // 保存录音
      setRecordingsMap(prev => {
        const newMap = new Map(prev);
        newMap.set(role, wavBlob);
        return newMap;
      });
      
      // 如果有旧的音频URL，先释放
      if (audioURLsRef.current.has(role)) {
        URL.revokeObjectURL(audioURLsRef.current.get(role)!);
      }
      
      // 创建新的URL对象
      const audioURL = URL.createObjectURL(wavBlob);
      audioURLsRef.current.set(role, audioURL);
    //   setCurrentAudioUrl(audioURL);
      
      message.success(`角色"${role}"的录音已保存`);
      
      // // 自动播放新录制的音频
      // setTimeout(() => {
      //   playRecording(role);
      // }, 500);
      
    } catch (error: any) {
      console.error('处理录音失败:', error);
      message.error(`保存录音失败: ${error.message || '请重试'}`);
    } finally {
      setIsRecording(false);
      setRecordingRole(null);
    }
  };

  useEffect(() => {
    if (recordingsMap.size > 0) {
      playRecording(Array.from(recordingsMap.keys())[0]);
    }
  }, [recordingsMap]);

  // 停止录音
  const stopRecording = () => {
    console.log('停止录音');
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      
      // 清除计时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };
  
  // 格式化录音时间
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 播放录音
  const playRecording = (role: string) => {
    console.log(`播放角色 "${role}" 的录音`);
    
    // 如果此角色没有录音，直接返回
    if (!recordingsMap.has(role)) {
      message.warning(`角色"${role}"尚未录制声音`);
      return;
    }
    
    // 如果正在播放其他录音，先停止
    if (playingRole) {
      stopPlaying();
    }
    
    // 获取或创建音频元素
    if (!audioElementRef.current) {
      audioElementRef.current = document.createElement('audio');
      audioElementRef.current.controls = true;
      audioElementRef.current.style.display = 'block';
      audioElementRef.current.style.width = '100%';
      audioElementRef.current.style.marginTop = '8px';
      audioElementRef.current.style.marginBottom = '8px';
      audioElementRef.current.preload = 'auto';
      
      // 在播放结束时清理状态
      audioElementRef.current.onended = () => {
        setPlayingRole(null);
      };
      
      // 添加到DOM中
      const container = document.querySelector(`.${styles.audioPlayerContainer}`);
      if (container) {
        container.appendChild(audioElementRef.current);
      }
    }
    
    // 设置音频URL
    const audioURL = audioURLsRef.current.get(role);
    if (audioURL) {
      audioElementRef.current.src = audioURL;
      audioElementRef.current.style.display = 'block';
      
      // 尝试自动播放（可能会被浏览器阻止）
      const playPromise = audioElementRef.current.play();
      
      if (playPromise !== undefined) {
        playPromise.then(() => {
          setPlayingRole(role);
          console.log('音频播放成功启动');
        }).catch(error => {
          console.warn('自动播放被阻止，需要用户交互:', error);
          
          // 显示音频元素并设置当前播放角色，即使未自动播放
          setPlayingRole(role);
          
          // 提示用户手动点击播放
          message.info({
            content: '请点击音频播放器中的播放按钮开始播放',
            duration: 3
          });
        });
      }
    } else {
      message.error('播放失败，录音文件可能已损坏');
    }
  };

  // 停止播放
  const stopPlaying = () => {
    if (audioElementRef.current) {
      // 暂停音频
      audioElementRef.current.pause();
      // 重置播放时间
      audioElementRef.current.currentTime = 0;
      // 隐藏音频元素
      audioElementRef.current.style.display = 'none';
    }
    
    setPlayingRole(null);
  };

  // 删除录音
  const deleteRecording = (role: string) => {
    setRecordingsMap(prev => {
      const newMap = new Map(prev);
      newMap.delete(role);
      return newMap;
    });
    
    // 清除缓存的URL
    const url = audioURLsRef.current.get(role);
    if (url) {
      URL.revokeObjectURL(url);
      audioURLsRef.current.delete(role);
    }
    
    message.success(`已删除${role}的录音`);
  };
  
  // 重置所有录音
  const resetAllRecordings = () => {
    // 清理所有URL对象
    audioURLsRef.current.forEach(url => {
      URL.revokeObjectURL(url);
    });
    audioURLsRef.current.clear();
    
    // 清空录音Map
    setRecordingsMap(new Map());
    
    message.success('已重置所有录音');
  };

  // 完成录音
  const handleComplete = () => {
    // 检查是否有录音
    if (recordingsMap.size === 0) {
      message.error('请至少录制一个角色的声音');
      return;
    }
    
    // 不再强制要求所有角色都必须录制
    // 如果有未录制的角色，只提示一下，但仍然允许继续
    if (!allRolesRecorded) {
      message.warning('注意：有些角色尚未录制声音，但您可以继续操作');
      // 简单延迟一下，让用户看到提示
      setTimeout(() => {
        // 停止当前可能正在进行的录音
        if (isRecording) {
          stopRecording();
        }
        
        // 停止正在播放的音频
        if (playingRole) {
          stopPlaying();
        }
        
        // 将录音传递给父组件
        console.log('完成录音，传递录音数据，数量:', recordingsMap.size, '角色:', [...recordingsMap.keys()].join(', '));
        onComplete(new Map(recordingsMap)); // 创建一个新的Map对象，确保数据正确传递
        
        // 如果提供了生成克隆语音的回调，则调用它
        if (onGenerateClonedAudio) {
          onGenerateClonedAudio();
        }
      }, 1000);
      return;
    }
    
    // 停止当前可能正在进行的录音
    if (isRecording) {
      stopRecording();
    }
    
    // 停止正在播放的音频
    if (playingRole) {
      stopPlaying();
    }
    
    // 将录音传递给父组件
    console.log('完成录音，传递录音数据，数量:', recordingsMap.size, '角色:', [...recordingsMap.keys()].join(', '));
    onComplete(new Map(recordingsMap)); // 创建一个新的Map对象，确保数据正确传递
    
    // 如果提供了生成克隆语音的回调，则调用它
    // if (onGenerateClonedAudio) {
    //   onGenerateClonedAudio();
    // }
  };

  // 处理关闭
  const handleClose = () => {
    // 停止任何进行中的录音
    if (isRecording) {
      stopRecording();
    }
    
    // 停止正在播放的音频
    if (playingRole) {
      stopPlaying();
    }
    
    // 调用父组件的取消函数，同时传递当前的recordingsMap
    console.log('关闭录音窗口，传递录音数据，数量:', recordingsMap.size, '角色:', [...recordingsMap.keys()].join(', '));
    onComplete(new Map(recordingsMap)); // 创建一个新的Map对象，确保数据正确传递
  };

  // 停止所有音频和录音
  const stopAllAudioAndRecording = () => {
    // 停止录音
    if (isRecording && mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
    }
    
    // 清除计时器
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    // 停止音量监测
    stopVolumeMonitoring();
    
    // 停止所有媒体流轨道
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    // 停止正在播放的音频
    if (audioElementRef.current) {
      audioElementRef.current.pause();
    }
    
    setIsRecording(false);
    setRecordingRole(null);
    setPlayingRole(null);
  };

  return (
    <Modal
      title="录制角色声音克隆"
      open={visible}
      width={800}
      footer={null}
      onCancel={handleClose}
      maskClosable={false}
    >
      <div className={styles.recorderContainer}>
        <Alert
          message="语音克隆说明"
          description={
            <ul className={styles.recordingTips}>
              <li>请在安静的环境中录制</li>
              <li>请确保麦克风正常工作并已授权浏览器使用</li>
              <li>不必为所有角色录制声音样本，您可以只录制需要的角色</li>
              <li>请朗读提示文本，声音自然、清晰</li>
              <li>录制完成后，请点击播放按钮检查录音效果</li>
            </ul>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        {noSoundWarning && (
          <Alert
            message="检测不到声音"
            description="我们未能检测到麦克风输入。请确保您的麦克风已连接并正常工作，且您在录音时有发出声音。"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        
        <div className={styles.promptBox}>
          请朗读以下文本作为声音样本：<br/>
          <Text strong>"{PROMPT_TEXT}"</Text>
        </div>
        
        {isRecording && (
          <div className={styles.recordingStatus}>
            <Space>
              <span style={{ display: 'inline-block', width: 16, height: 16, backgroundColor: 'red', borderRadius: '50%', animation: 'pulse 1.5s infinite' }}></span>
              <Text strong>正在录制 {recordingRole} 的声音... {formatDuration(recordingDuration)}</Text>
            </Space>
            <div style={{ marginTop: 8 }}>
              <Text>音量: </Text>
              <Progress percent={volumeLevel} size="small" status={volumeLevel < 5 ? "exception" : "active"} />
            </div>
          </div>
        )}
        
        <div className={styles.audioPlayerContainer}>
          {playingRole && (
            <div className={styles.playingInfo}>
              <Text>正在播放: {playingRole} 的声音样本</Text>
              <Button type="primary" size="small" onClick={stopPlaying}>停止播放</Button>
              <div style={{ fontStyle: 'italic', marginTop: 5, fontSize: '0.9em' }}>
                如果没有自动播放，请点击音频播放器中的播放按钮手动开始播放
              </div>
            </div>
          )}
        </div>
        
        <List
          itemLayout="horizontal"
          dataSource={roles}
          style={{ marginTop: 16 }}
          renderItem={role => (
            <List.Item
              style={{ 
                border: '1px solid #f0f0f0',
                borderRadius: 4,
                padding: '12px 16px',
                marginBottom: 8,
                backgroundColor: recordingRole === role ? '#fff2e8' : 
                                recordingsMap.has(role) ? '#f6ffed' : '#fff7e6'
              }}
              actions={[
                recordingsMap.has(role) ? (
                  <Button 
                    icon={<SoundOutlined />} 
                    onClick={() => playRecording(role)}
                    disabled={isRecording || playingRole === role}
                  >
                    播放
                  </Button>
                ) : null,
                recordingsMap.has(role) ? (
                  <Button 
                    danger 
                    icon={<DeleteOutlined />} 
                    onClick={() => deleteRecording(role)}
                    disabled={isRecording}
                  >
                    删除
                  </Button>
                ) : null,
                <Button
                  type={recordingRole === role && isRecording ? "primary" : "default"}
                  danger={recordingRole === role && isRecording}
                  icon={recordingRole === role && isRecording ? <AudioMutedOutlined /> : <AudioOutlined />}
                  onClick={recordingRole === role && isRecording ? stopRecording : () => startRecording(role)}
                  disabled={(isRecording && recordingRole !== role) || !microphoneDetected}
                >
                  {recordingRole === role && isRecording ? '停止' : '录制'}
                </Button>
              ]}
            >
              <List.Item.Meta
                avatar={recordingsMap.has(role) ? 
                  <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 24 }} /> : 
                  <span style={{ width: 24, height: 24, display: 'inline-block', textAlign: 'center', color: '#faad14' }}>!</span>}
                title={<Text strong>{role}</Text>}
                description={recordingsMap.has(role) ? 
                  <Text type="success">已录制声音样本</Text> : 
                  <Text type="warning">尚未录制声音，但不是必须的</Text>}
              />
            </List.Item>
          )}
        />
        
        <div style={{ marginTop: 24, display: 'flex', justifyContent: 'space-between' }}>
          <Button 
            onClick={resetAllRecordings} 
            disabled={isRecording || recordingsMap.size === 0}
          >
            重置所有录音
          </Button>
          
          <Space>
            <Button onClick={handleClose}>
              取消
            </Button>
            <Button 
              type="primary" 
              onClick={handleComplete}
              disabled={recordingsMap.size === 0 || isRecording}
            >
              {onGenerateClonedAudio ? 
                `完成录音${allRolesRecorded ? '' : '(部分角色)'} 并生成克隆语音` : 
                `完成录音${allRolesRecorded ? '' : '(部分角色)'}`}
            </Button>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default VoiceCloneModal; 