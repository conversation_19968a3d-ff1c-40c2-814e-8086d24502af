import React from 'react';
import { But<PERSON>, Card, Typography, Input } from 'antd';
import { SendOutlined } from '@ant-design/icons';
import styles from '../PodcastGen.module.css';

const { TextArea } = Input;
const { Paragraph } = Typography;

interface InputInterfaceProps {
  inputText: string;
  onInputChange: (text: string) => void;
  onGenerate: () => void;
  isGenerating: boolean;
}

const InputInterface: React.FC<InputInterfaceProps> = ({
  inputText,
  onInputChange,
  onGenerate,
  isGenerating
}) => {
  return (
    <Card title="播客内容描述" className={styles.card}>
      <div className={styles.cardContent}>
        <Paragraph className={styles.description}>
          请输入您想要生成的播客主题、风格或内容描述。系统将为您生成包含多个角色对话的播客脚本，并转换为逼真的语音。
        </Paragraph>
        
        <div className={styles.inputSection}>
          <TextArea
            value={inputText}
            onChange={(e) => onInputChange(e.target.value)}
            placeholder="例如：一段关于人工智能未来发展的讨论，包含技术专家和主持人的对话..."
            autoSize={{ minRows: 6, maxRows: 12 }}
            className={styles.textInput}
          />
        </div>
        
        <div className={styles.buttonGroup}>
          <Button 
            type="primary" 
            icon={<SendOutlined />} 
            onClick={onGenerate}
            loading={isGenerating}
            disabled={!inputText.trim()}
            className={styles.generateButton}
          >
            生成播客脚本
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default InputInterface; 