import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import { LeftOutlined, FileTextOutlined, LoadingOutlined, PlayCircleOutlined } from '@ant-design/icons';
import styles from '../PodcastGen.module.css';

const { Title, Text } = Typography;

interface AudioInterfaceProps {
  audioUrl: string;
  isMergingAudio: boolean;
  onBackToScript: () => void;
  onReset: () => void;
}

const AudioInterface: React.FC<AudioInterfaceProps> = ({
  audioUrl,
  isMergingAudio,
  onBackToScript,
  onReset
}) => {
  return (
    <div className={styles.audioContainer}>
      <div className={styles.audioHeader}>
        <Button 
          icon={<LeftOutlined />} 
          onClick={onBackToScript}
          className={styles.backButton}
        >
          返回脚本
        </Button>
        <Title level={4} className={styles.audioTitle}>完整播客音频</Title>
        <Button
          icon={<FileTextOutlined />}
          onClick={onBackToScript}
          className={styles.viewScriptButton}
        >
          查看脚本
        </Button>
      </div>
      
      <div className={styles.audioContent}>
        {isMergingAudio ? (
          <div className={styles.loadingContainer}>
            <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
            <Text>正在合并所有音频片段...</Text>
          </div>
        ) : (
          audioUrl ? (
            <div className={styles.audioPlayer}>
              <div className={styles.playerCard}>
                <div className={styles.playerIcon}>
                  <PlayCircleOutlined />
                </div>
                <div className={styles.playerContent}>
                  <Title level={5}>播客节目</Title>
                  <audio controls src={audioUrl} className={styles.audioElement}></audio>
                </div>
              </div>
              
              <div className={styles.downloadSection}>
                <Text strong>下载链接:</Text>
                <a href={audioUrl} download className={styles.downloadLink}>
                  下载播客音频
                </a>
              </div>
              
              <div className={styles.resetSection}>
                <Button onClick={onReset} className={styles.resetButton}>
                  创建新播客
                </Button>
              </div>
            </div>
          ) : (
            <div className={styles.noAudioMessage}>
              <Alert
                message="尚未生成完整音频"
                description="请先返回脚本页面生成各个片段，然后点击合并按钮"
                type="info"
                showIcon
              />
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default AudioInterface; 