.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100vh;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.description {
  display: block;
  margin-top: 8px;
  font-size: 16px;
  color: #666;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card {
  width: 100%;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  transition: all 0.3s;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cardContent {
  padding: 8px 0;
}

.inputSection {
  margin: 16px 0;
}

.textInput {
  border-radius: 4px;
  font-size: 14px;
  resize: none;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.buttonGroup {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.generateButton {
  min-width: 160px;
  height: 40px;
  font-size: 16px;
}

/* 脚本容器样式 */
.scriptContainer {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px;
}

.scriptHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.scriptTitle {
  margin: 0;
}

.backButton, .audioButton, .viewScriptButton, .mergeButton {
  transition: all 0.3s;
}

.audioButton {
  min-width: 140px;
}

.mergeButton {
  min-width: 120px;
}

.progressContainer {
  margin: 0 0 24px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.scriptContent {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  gap: 16px;
}

.dialogueList {
  padding: 8px 0;
}

.dialogueItem {
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
  transition: all 0.3s;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.dialogueItem:hover {
  background-color: #f0f0f0;
}

.dialogueTitle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dialogueContent {
  margin-top: 8px;
  line-height: 1.6;
}

.audioControls {
  margin-left: 16px;
  min-width: 250px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
}

.dialogueAudio {
  max-width: 250px;
  height: 36px;
}

.rawScriptContent {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* 消息提示样式 */
.noContentMessage, .noAudioMessage {
  max-width: 600px;
  width: 100%;
  margin: 40px auto;
}

/* 音频界面样式 */
.audioContainer {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px;
}

.audioHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.audioTitle {
  margin: 0;
}

.audioContent {
  flex: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.audioPlayer {
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.playerCard {
  display: flex;
  padding: 24px;
  background-color: #f9f9f9;
  border-radius: 12px;
  align-items: center;
  gap: 24px;
}

.playerIcon {
  font-size: 48px;
  color: #1890ff;
}

.playerContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.audioElement {
  width: 100%;
  margin-top: 8px;
}

.downloadSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.downloadLink {
  color: #1890ff;
  text-decoration: underline;
}

.resetSection {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.resetButton {
  min-width: 140px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .scriptHeader, .audioHeader {
    padding: 12px 16px;
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .scriptContent, .audioContent {
    padding: 16px;
  }
  
  .playerCard {
    flex-direction: column;
    padding: 16px;
  }
  
  .buttonGroup {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  
  .audioButton, .generateButton, .resetButton, .mergeButton {
    width: 100%;
  }
  
  .dialogueItem {
    flex-direction: column;
  }
  
  .audioControls {
    margin-left: 0;
    margin-top: 16px;
    width: 100%;
    justify-content: center;
  }
  
  .dialogueAudio {
    max-width: 100%;
    width: 100%;
  }
}

.audioInfo {
  margin-top: 4px;
  font-size: 12px;
  color: #1890ff;
}

.audioInfo a {
  color: #1890ff;
  text-decoration: underline;
}

.audioInfo a:hover {
  color: #40a9ff;
}

/* 可折叠输入框样式 */
.collapsibleInput {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  transition: all 0.3s ease;
  overflow: visible;
  position: relative;
  padding-bottom: 60px; /* 为固定按钮留出空间 */
}

.collapsibleHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  background-color: #fafafa;
}

.collapsibleHeader:hover {
  background-color: #f5f5f5;
}

.collapsibleContent {
  padding: 24px;
  transition: all 0.3s ease;
}

.collapsibleContent.collapsed {
  max-height: 0;
  padding: 0;
  overflow: hidden;
  opacity: 0;
}

.toggleIcon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.toggleIcon.rotated {
  transform: rotate(180deg);
}

/* 固定位置的按钮组 */
.buttonGroupFixed {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 10px 0;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  z-index: 5;
  border-radius: 0 0 8px 8px;
}

/* 同时显示输入和脚本 */
.unifiedContainer {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

/* 流式内容样式 */
.streamContent {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 24px;
  overflow-y: auto;
  max-height: 400px;
  transition: all 0.3s ease;
}

.streamContent pre {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
  padding: 0;
}

/* 输入框提示样式 */
.inputWithTip {
  width: 100%;
}

.inputTip {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.infoIcon {
  margin-left: 8px;
  color: #1890ff;
  cursor: pointer;
}

/* 合并音频区域样式 */
.mergedAudioSection {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.mergedAudioCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.mergedAudio {
  border-radius: 4px;
  background-color: #f9f9f9;
  width: 100%;
  height: 40px;
}

/* 录音相关样式 */
.recorderContainer {
  padding: 2px;
}

.promptBox {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 16px;
  margin: 16px 0;
  font-size: 18px;
  text-align: center;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.recordingStatus {
  margin: 16px 0;
  padding: 12px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  animation: pulse 1.5s infinite;
}

.recordingTips {
  padding-left: 20px;
  margin-bottom: 0;
}

.recordingTips li {
  margin-bottom: 8px;
}

.audioPlayerContainer {
  margin: 16px 0;
  min-height: 60px;
}

.playingInfo {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

/* 控制闪烁动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
} 

:global(.ant-modal-confirm.ant-modal .ant-modal-body) {
  padding: 16px !important;
}


.headerSettings {
  position: absolute;
  right: 8px;
  top: 16px;
  transform: translateY(-50%);
}