import { MANAGER_API_BASE_URL } from '../../../Constant/ServerConstant';

// PodcastContentItem接口 - 描述单个播客内容项
export interface PodcastContentItem {
  role: string;
  content: string;
  name?: string;
  audioUrl?: string; // 单个音频URL
  isGenerating?: boolean; // 是否正在生成中
  status?: string; // 生成状态：等待中、生成中、已完成、生成失败
}

// Constants
export const BATCH_SIZE = 4; // 每批处理的对话数量

// 后端API端点常量
export const API_ENDPOINTS = {
  // 工作流ID
  WORKFLOW_ID: '4e3fc849-4643-1d90-62c4-e5b0604f18e8',
  // 后端API地址 - 单个语音合成接口
  SYNTHESIZE_API_URL: `${MANAGER_API_BASE_URL}/api/v1/audio/tts/synthesize`,
  // 后端API地址 - 音频合并接口
  MERGE_API_URL: `${MANAGER_API_BASE_URL}/api/v1/audio/tts/merge_audio`
  
};

// View类型 - 控制当前显示的视图
export type ViewType = 'input' | 'script' | 'audio'; 