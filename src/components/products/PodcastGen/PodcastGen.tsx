import React, { useState, useRef, useEffect } from 'react';
import { message, Typography, Modal } from 'antd';
import styles from './PodcastGen.module.css';
import { PodcastContentItem, ViewType } from './types';
import { jsonrepair } from 'jsonrepair';
import { PODCAST_STATUS } from './constants';

// 导入组件
import ScriptInterface from './components/ScriptInterface';
import AudioInterface from './components/AudioInterface';
import CollapsibleInput from './components/CollapsibleInput';
import VoiceCloneModal from './components/VoiceCloneModal';

// 导入服务
import { generatePodcastScript, parseScriptContent } from './services/scriptService';
import { processAudioQueue, generateSingleAudio, mergeAudioFiles } from './services/audioService';
import { generateClonedVoices } from './services/voiceCloneService';
import HeaderSettings from '../../common/HeaderSettings';

const { Title, Text } = Typography;

// 累积更新的计数器

interface PodcastGenProps {}

const PodcastGen: React.FC<PodcastGenProps> = () => {
  // 输入文本
  const [inputText, setInputText] = useState('');
  // 生成的播客脚本内容
  const [streamContent, setStreamContent] = useState('');
  // 处理流式内容的计数
  const [streamUpdateCount, setStreamUpdateCount] = useState(0);
  // 解析后的播客内容
  const [podcastContent, setPodcastContent] = useState<PodcastContentItem[]>([]);
  // 是否正在生成脚本
  const [isGeneratingScript, setIsGeneratingScript] = useState(false);
  // 是否正在生成音频
  const [isGeneratingAudio, setIsGeneratingAudio] = useState(false);
  // 是否脚本准备就绪
  const [scriptReady, setScriptReady] = useState(false);
  // 生成的音频URL
  const [audioUrl, setAudioUrl] = useState('');
  // 当前视图（'input', 'script', 'audio'）
  const [currentView, setCurrentView] = useState<ViewType>('input');
  // 容器的引用，用于滚动
  const containerRef = useRef<HTMLDivElement>(null);
  // 是否正在合并音频
  const [isMergingAudio, setIsMergingAudio] = useState(false);
  // 音频生成进度
  const [generateProgress, setGenerateProgress] = useState(0);
  // 暂存流式内容用于处理
  const streamContentRef = useRef('');

  // 添加录音和语音克隆相关状态
  const [isRecorderVisible, setIsRecorderVisible] = useState(false);
  const [recordingsMap, setRecordingsMap] = useState<Map<string, Blob>>(new Map());
  const [isGeneratingClonedAudio, setIsGeneratingClonedAudio] = useState(false);

  // 滚动到底部，确保用户能看到最新内容
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [streamContent]);

  // 尝试解析流式内容为播客内容
  const tryParseStreamContent = (content: string) => {
    try {
      // 使用jsonrepair尝试修复可能不完整的JSON
      const repairedContent = jsonrepair(content);
      // 解析JSON并尝试将其转换为播客内容项
      const parsedContent = parseScriptContent(repairedContent);
      if (parsedContent && parsedContent.length > 0) {
        setPodcastContent(parsedContent);
        setScriptReady(true);
      }
    } catch (error) {
      // 解析错误，但不显示错误，因为内容可能还在生成中
      console.log('流式内容解析尝试失败，继续等待完整内容');
    }
  };
  console.log(streamUpdateCount, 'setStreamUpdateCount');

  // 生成播客脚本
  const handleGenerateScript = async () => {
    if (!inputText.trim()) {
      message.warning('请输入内容描述');
      return;
    }

    // 重置状态
    setStreamContent('');
    setScriptReady(false);
    setPodcastContent([]);
    setAudioUrl('');
    streamContentRef.current = '';

    // 调用脚本生成服务
    await generatePodcastScript(inputText, {
      onStart: () => setIsGeneratingScript(true),
      onStream: (content) => {
        // 更新暂存内容
        streamContentRef.current = content;
        // 更新流内容累计计数
        setStreamUpdateCount((prev) => {
          const newCount = prev + 1;
          // 移除这里的解析尝试，只保存内容
          return newCount;
        });

        // 更新显示的流内容
        setStreamContent(content);
      },
      onParse: (content) => {
        setPodcastContent(content);
        setScriptReady(true);
        // 不再切换视图，保持在同一界面
        message.success('播客脚本生成完成');
      },
      onComplete: () => {
        setIsGeneratingScript(false);
        // 只在完成时解析最终内容
        console.log('流式输出完成，开始解析最终内容');
        tryParseStreamContent(streamContentRef.current);
      },
      onError: (error) => {
        console.error('脚本生成失败:', error);
        message.error('脚本生成失败，请重试');
        setIsGeneratingScript(false);
      }
    });
  };

  // 生成所有音频
  const handleGenerateAudio = async () => {
    if (podcastContent.length === 0) {
      message.error('请先生成播客脚本');
      return;
    }

    // 重置所有音频相关状态
    const resetContent = podcastContent.map(item => ({
      ...item,
      audioUrl: undefined,
      isGenerating: false,
      status: PODCAST_STATUS.WAITING // 添加状态字段
    }));
    setPodcastContent(resetContent);
    setAudioUrl('');
    setGenerateProgress(0);

    message.info('开始生成所有语音片段，同时处理4个音频');

    // 处理音频生成
    await processAudioQueue(resetContent, {
      onStart: () => setIsGeneratingAudio(true),
      onItemStart: (index) => {
        // 标记为生成中
        setPodcastContent(prevContent => {
          const updatedContent = [...prevContent];
          updatedContent[index] = {
            ...updatedContent[index],
            isGenerating: true,
            status: PODCAST_STATUS.GENERATING
          };
          return updatedContent;
        });

        message.loading({
          content: `正在生成第 ${index + 1}/${resetContent.length} 个语音片段...`,
          key: `audio-gen-${index}`
        });
      },
      onItemComplete: (index, url) => {
        // 更新音频URL
        setPodcastContent(prevContent => {
          const updatedContent = [...prevContent];
          updatedContent[index] = {
            ...updatedContent[index],
            audioUrl: url,
            isGenerating: false,
            status: PODCAST_STATUS.COMPLETED
          };
          return updatedContent;
        });

        message.success({
          content: `第 ${index + 1}/${resetContent.length} 个语音片段生成完成`,
          key: `audio-gen-${index}`
        });
      },
      onItemError: (index, error) => {
        // 标记为生成失败
        setPodcastContent(prevContent => {
          const updatedContent = [...prevContent];
          updatedContent[index] = {
            ...updatedContent[index],
            isGenerating: false,
            status: PODCAST_STATUS.FAILED
          };
          return updatedContent;
        });

        message.error({
          content: `第 ${index + 1} 个音频生成失败: ${error.message}`,
          key: `audio-gen-${index}`
        });
      },
      onProgress: (completed, total) => {
        const progress = Math.floor((completed / total) * 100);
        setGenerateProgress(progress);
      },
      onComplete: () => {
        setIsGeneratingAudio(false);
        message.success('所有音频片段生成完成');

        // 音频生成完成后自动合并
        message.info('正在自动合并所有音频...');
        setTimeout(() => {
          handleMergeAudio();
        }, 1000); // 延迟1秒后自动合并
      },
      onError: (error) => {
        console.error('音频处理失败:', error);
        message.error('部分音频生成失败，请重试');
        setIsGeneratingAudio(false);
      }
    });
  };

  // 生成单个音频
  const handleGenerateSingleAudio = async (index: number) => {
    const item = podcastContent[index];

    // 标记为生成中
    const updatedContent = [...podcastContent];
    updatedContent[index] = { ...item, isGenerating: true, status: PODCAST_STATUS.GENERATING };
    setPodcastContent(updatedContent);

    const messageKey = `audio-${index}`;
    message.loading({ content: `正在生成第 ${index + 1} 个语音片段...`, key: messageKey });

    try {
      // 生成音频
      const url = await generateSingleAudio(item);

      // 更新状态
      updatedContent[index] = {
        ...item,
        audioUrl: url,
        isGenerating: false,
        status: PODCAST_STATUS.COMPLETED
      };
      setPodcastContent([...updatedContent]);
      message.success({ content: `第 ${index + 1} 个音频生成成功`, key: messageKey });
    } catch (error: any) {
      console.error(`音频 ${index + 1} 生成失败:`, error);
      // 标记为生成失败
      updatedContent[index] = {
        ...item,
        isGenerating: false,
        status: PODCAST_STATUS.FAILED
      };
      setPodcastContent([...updatedContent]);
      message.error({ content: `音频生成失败: ${error.message || '请重试'}`, key: messageKey });
    }
  };

  // 合并所有音频
  const handleMergeAudio = async () => {
    // 获取所有音频URL
    const audioUrls = podcastContent
      .filter(item => item.audioUrl)
      .map(item => item.audioUrl as string);

    if (audioUrls.length < 1) {
      message.warning('请至少生成一个音频片段');
      return;
    }

    message.loading({ content: '正在合并音频...', key: 'mergingAudio' });
    setIsMergingAudio(true);

    try {
      // 调用音频合并服务
      const mergedUrl = await mergeAudioFiles(audioUrls);

      // 更新音频URL
      setAudioUrl(mergedUrl);

      message.success({ content: '音频合并成功！', key: 'mergingAudio' });

      // 不再切换到音频视图，保持在脚本预览页面
      // setCurrentView('audio');
    } catch (error: any) {
      console.error('音频合并失败:', error);
      message.error({
        content: `音频合并失败: ${error.message || '请重试'}`,
        key: 'mergingAudio'
      });
    } finally {
      setIsMergingAudio(false);
    }
  };

  // 重置所有状态
  const handleReset = () => {
    // 重置所有状态
    setInputText('');
    setStreamContent('');
    setPodcastContent([]);
    setIsGeneratingScript(false);
    setIsGeneratingAudio(false);
    setScriptReady(false);
    setAudioUrl('');
    setCurrentView('input');
    setIsMergingAudio(false);
    setGenerateProgress(0);
    streamContentRef.current = '';

    message.success('已重置所有内容');
  };


  // 处理录制角色声音
  const handleRecordVoices = () => {
    setIsRecorderVisible(true);
  };

  // 处理录音完成
  const handleRecordingComplete = (recordings: Map<string, Blob>) => {
    console.log('录音完成，录音数量:', recordings.size, '角色:', [...recordings.keys()].join(', '));
    console.log(recordings, 'podcastContent');
    // 确保正确设置recordingsMap
    setRecordingsMap(recordings);
    setIsRecorderVisible(false);

    // 提示用户录音已保存
    if (recordings.size > 0) {
      message.success('角色声音样本已保存，可以生成克隆语音');

      // 创建自定义消息，询问是否立即生成克隆语音
      if (podcastContent.length > 0) {
        Modal.confirm({
          title: '生成克隆语音',
          content: '是否要立即使用录制的声音生成语音片段？',
          okText: '立即生成',
          cancelText: '稍后生成',
          onOk: () => {
            // 直接传递 recordings 而不依赖 recordingsMap 状态
            handleGenerateClonedAudio(recordings);
          }
        });
      }
    }
  };

  // 处理生成克隆语音 - 修改函数签名接受可选参数
  const handleGenerateClonedAudio = async (recordingsToUse?: Map<string, Blob>) => {
    // 使用传入的 recordings 或者状态中的 recordingsMap
    const recordings = recordingsToUse || recordingsMap;

    // 再次检查recordingsMap大小并输出日志
    console.log('准备生成克隆语音，录音数量:', recordings.size, '角色:', [...recordings.keys()].join(', '));

    if (recordings.size === 0) {
      message.error('请至少录制一个角色的声音样本');
      return;
    }

    if (podcastContent.length === 0) {
      message.error('请先生成播客脚本');
      return;
    }

    // 重置所有音频相关状态
    const resetContent = podcastContent.map(item => ({
      ...item,
      audioUrl: undefined,
      isGenerating: false,
      status: PODCAST_STATUS.WAITING
    }));
    setPodcastContent(resetContent);
    setAudioUrl('');
    setGenerateProgress(0);

    message.info('开始使用克隆声音生成语音片段');

    // 设置提示文本
    const promptText = "生活就像一杯茶，不会苦一辈子，但总会苦一阵子。重要的不是此刻的苦涩，而是你如何品味其中的回甘。保持微笑，继续前行，美好终将如期而至";

    // 处理克隆语音生成
    try {
      await generateClonedVoices(recordings, promptText, resetContent, {
        onStart: () => setIsGeneratingClonedAudio(true),
        onItemStart: (index) => {
          // 标记为生成中
          setPodcastContent(prevContent => {
            const updatedContent = [...prevContent];
            updatedContent[index] = {
              ...updatedContent[index],
              isGenerating: true,
              status: PODCAST_STATUS.CLONING
            };
            return updatedContent;
          });

          message.loading({
            content: `正在为第 ${index + 1}/${resetContent.length} 个语音片段生成克隆语音...`,
            key: `clone-gen-${index}`
          });
        },
        onItemComplete: (index, url) => {
          // 更新音频URL
          setPodcastContent(prevContent => {
            const updatedContent = [...prevContent];
            updatedContent[index] = {
              ...updatedContent[index],
              audioUrl: url,
              isGenerating: false,
              status: PODCAST_STATUS.COMPLETED
            };
            return updatedContent;
          });

          message.success({
            content: `第 ${index + 1}/${resetContent.length} 个克隆语音生成完成`,
            key: `clone-gen-${index}`
          });
        },
        onItemError: (index, error) => {
          // 标记为生成失败
          setPodcastContent(prevContent => {
            const updatedContent = [...prevContent];
            updatedContent[index] = {
              ...updatedContent[index],
              isGenerating: false,
              status: PODCAST_STATUS.FAILED
            };
            return updatedContent;
          });

          message.error({
            content: `第 ${index + 1} 个克隆语音生成失败: ${error.message}`,
            key: `clone-gen-${index}`
          });
        },
        onProgress: (completed, total) => {
          const progress = Math.floor((completed / total) * 100);
          setGenerateProgress(progress);
        },
        onComplete: () => {
          setIsGeneratingClonedAudio(false);
          message.success('所有克隆语音生成完成');

          // 克隆语音生成完成后自动合并
          message.info('正在自动合并所有音频...');
          setTimeout(() => {
            handleMergeAudio();
          }, 1000); // 延迟1秒后自动合并
        },
        onError: (error) => {
          console.error('克隆语音处理失败:', error);
          message.error('部分克隆语音生成失败，请重试');
          setIsGeneratingClonedAudio(false);
        }
      });
    } catch (error: any) {
      console.error('克隆语音生成失败:', error);
      message.error(`克隆语音生成失败: ${error.message || '请重试'}`);
      setIsGeneratingClonedAudio(false);
    }
  };

  // 渲染当前视图
  const renderCurrentView = () => {
    // 音频界面单独显示
    if (currentView === 'audio') {
      return (
        <AudioInterface
          audioUrl={audioUrl}
          isMergingAudio={isMergingAudio}
          onBackToScript={() => setCurrentView('script')}
          onReset={handleReset}
        />
      );
    }

    // 统一界面：包含可折叠输入框和脚本/流式内容
    return (
      <div className={styles.unifiedContainer}>
        {/* 可折叠输入框 */}
        <CollapsibleInput
          inputValue={inputText}
          onInputChange={setInputText}
          onGenerateClick={handleGenerateScript}
          isLoading={isGeneratingScript}
          initialCollapsed={scriptReady}
          isAudioProcessing={isGeneratingAudio || isGeneratingClonedAudio || isMergingAudio}
        />

        {/* 直接显示脚本内容，无需单独的流式内容显示 */}
        <ScriptInterface
          streamContent={streamContent}
          podcastContent={podcastContent}
          isGeneratingScript={isGeneratingScript}
          isGeneratingAudio={isGeneratingAudio || isGeneratingClonedAudio}
          isMergingAudio={isMergingAudio}
          // scriptReady={scriptReady}
          generateProgress={generateProgress}
          // shouldAutoScroll={true}
          mergedAudioUrl={audioUrl}
          onGenerateAudio={handleGenerateAudio}
          onMergeAudio={handleMergeAudio}
          // onToggleAutoScroll={() => {}}
          // onManualParse={handleManualParse}
          onGenerateSingleAudio={handleGenerateSingleAudio}
          containerRef={containerRef}
          hasRecordings={recordingsMap.size > 0}
          onRecordVoices={handleRecordVoices}
          onGenerateClonedAudio={handleGenerateClonedAudio}
        />

        {/* 语音克隆录制模态框 */}
        <VoiceCloneModal
          visible={isRecorderVisible}
          // onCancel={() => setIsRecorderVisible(false)}
          onComplete={handleRecordingComplete}
          podcastContent={podcastContent}
          onGenerateClonedAudio={handleGenerateClonedAudio}
        />
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Title level={2}>AI播客生成器</Title>
        <Text className={styles.description}>
          通过AI生成逼真的多人播客节目，包含脚本和语音
        </Text>
      </div>
      <div className={styles.headerSettings}>
        <HeaderSettings />
      </div>
      <div className={styles.content}>
        {renderCurrentView()}
      </div>
    </div>
  );
};

export default PodcastGen;