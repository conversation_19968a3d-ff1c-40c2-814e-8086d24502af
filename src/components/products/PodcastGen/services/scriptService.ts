import { v4 as uuidv4 } from 'uuid';
import { jsonrepair } from 'jsonrepair';
import createOpenAIClient from '../../../../utils/openaiClient';
import { PodcastContentItem, API_ENDPOINTS } from '../types';

/**
 * 使用AI生成播客脚本
 * @param inputText 用户输入的播客主题/要求
 * @param callbacks 回调函数对象，用于更新UI状态
 */
export const generatePodcastScript = async (
  inputText: string,
  callbacks: {
    onStart: () => void,
    onStream: (content: string) => void,
    onParse: (content: PodcastContentItem[]) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  }
): Promise<void> => {
  callbacks.onStart();
  
  try {
    // 构建请求内容
    const chat_content = {
      content: inputText,
      session_id: uuidv4(),
      workflow_or_agent: 'workflow',
      source: "product"
    };

    const chatContentString = JSON.stringify(chat_content);
    
    // 创建OpenAI客户端
    const client = createOpenAIClient();
    
    // 发送流式请求
    const stream = await client.chat.completions.create({
      model: API_ENDPOINTS.WORKFLOW_ID,
      messages: [{ role: 'user', content: chatContentString }],
      stream: true,
    });

    let accumulatedContent = '';
    
    // 接收流式返回数据，更新显示的响应文本
    for await (const chunk of stream) {
      const deltaContent = chunk.choices[0]?.delta?.content || '';
      if (deltaContent) {
        accumulatedContent += deltaContent;
        callbacks.onStream(accumulatedContent);
      }
    }
    
    // 尝试解析JSON内容
    try {
      // 找到JSON开始的位置（第一个[）和结束的位置（最后一个]）
      const startIndex = accumulatedContent.indexOf('[');
      const endIndex = accumulatedContent.lastIndexOf(']') + 1;
      
      if (startIndex >= 0 && endIndex > startIndex) {
        // 提取JSON字符串并使用jsonrepair修复格式问题
        const jsonString = accumulatedContent.substring(startIndex, endIndex);
        // console.log('原始JSON字符串:', jsonString);
        
        // 使用jsonrepair修复JSON
        const repairedJsonString = jsonrepair(jsonString);
        console.log('修复后的JSON字符串:', repairedJsonString);
        
        // 解析修复后的JSON
        const parsedContent = JSON.parse(repairedJsonString) as PodcastContentItem[];
        
        // 验证解析后的数据
        if (Array.isArray(parsedContent) && parsedContent.length > 0) {
          callbacks.onParse(parsedContent);
          callbacks.onComplete();
        } else {
          throw new Error('解析的内容不是有效的播客脚本数组');
        }
      } else {
        throw new Error('无法识别JSON内容');
      }
    } catch (parseError) {
      console.error('解析JSON失败:', parseError);
      callbacks.onError(new Error('解析脚本内容失败，请重试'));
    }
    
  } catch (error: any) {
    console.error('请求失败:', error);
    callbacks.onError(error);
  }
};

/**
 * 手动尝试解析播客脚本内容
 * @param content 原始内容字符串
 */
export const parseScriptContent = (content: string): PodcastContentItem[] => {
  // 找到JSON开始的位置（第一个[）和结束的位置（最后一个]）
  const startIndex = content.indexOf('[');
  const endIndex = content.lastIndexOf(']') + 1;
  
  if (startIndex < 0 || endIndex <= startIndex) {
    throw new Error('无法识别JSON内容');
  }
  
  // 提取JSON字符串并使用jsonrepair修复格式问题
  const jsonString = content.substring(startIndex, endIndex);
  
  // 使用jsonrepair修复JSON
  const repairedJsonString = jsonrepair(jsonString);
  
  // 解析修复后的JSON
  const parsedContent = JSON.parse(repairedJsonString) as PodcastContentItem[];
  
  // 验证解析后的数据
  if (!Array.isArray(parsedContent) || parsedContent.length === 0) {
    throw new Error('解析的内容不是有效的播客脚本数组');
  }
  
  return parsedContent;
}; 