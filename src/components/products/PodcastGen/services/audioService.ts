import { PodcastContentItem, API_ENDPOINTS, BATCH_SIZE } from '../types';

/**
 * 生成单个音频片段
 * @param index 当前项的索引
 * @param item 播客内容项
 * @returns 返回带有音频URL的内容项
 */
export const generateSingleAudio = async (item: PodcastContentItem): Promise<string> => {
  try {
    // 构建请求内容
    const requestData = {
      content: item.content,
      role: item.role,
      name: item.name
    };
    
    // 发送请求
    const response = await fetch(API_ENDPOINTS.SYNTHESIZE_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });
    
    if (!response.ok) {
      let errorMessage = `服务器响应错误: ${response.status}`;
      try {
        const errorText = await response.text();
        console.error(`音频错误详情:`, errorText);
        errorMessage += ` - ${errorText}`;
      } catch (e) {
        console.error('无法获取错误详情');
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();
    
    if (data && data.url) {
      return data.url;
    } else {
      throw new Error('返回数据格式不正确');
    }
  } catch (error: any) {
    console.error(`音频生成失败:`, error);
    throw error;
  }
};

/**
 * 处理音频生成队列，同时处理最多BATCH_SIZE个项目，但总体保持顺序
 * @param content 播客内容数组
 * @param callbacks 回调函数对象，用于更新UI状态
 */
export const processAudioQueue = async (
  content: PodcastContentItem[],
  callbacks: {
    onStart: () => void,
    onItemStart: (index: number) => void,
    onItemComplete: (index: number, url: string) => void,
    onItemError: (index: number, error: Error) => void,
    onProgress: (completedCount: number, totalCount: number) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  }
): Promise<void> => {
  try {
    callbacks.onStart();
    
    // 创建音频处理队列
    const totalItems = content.length;
    let completedCount = 0; // 已完成的数量
    let nextIndex = 0; // 下一个要处理的索引
    
    // 跟踪活动任务
    const activeTasks = new Set<Promise<void>>();
    const activeIndexes = new Set<number>();
    
    // 处理单个项目的函数
    const processItem = async (index: number) => {
      if (index >= totalItems) return;
      
      // 标记为正在处理
      activeIndexes.add(index);
      callbacks.onItemStart(index);
      
      try {
        // 生成音频
        const url = await generateSingleAudio(content[index]);
        
        // 更新完成数量和进度
        completedCount++;
        callbacks.onItemComplete(index, url);
        callbacks.onProgress(completedCount, totalItems);
      } catch (error: any) {
        console.error(`处理索引 ${index} 失败:`, error);
        callbacks.onItemError(index, error);
      } finally {
        // 无论成功失败，都移除当前任务
        activeIndexes.delete(index);
        
        // 如果还有下一个项目，继续处理
        if (nextIndex < totalItems) {
          const newTask = processItem(nextIndex++);
          activeTasks.add(newTask);
          newTask.finally(() => {
            activeTasks.delete(newTask);
          });
        }
        
        // 如果所有任务都已处理且没有活动任务，则完成
        if (nextIndex >= totalItems && activeIndexes.size === 0) {
          callbacks.onComplete();
        }
      }
    };
    
    // 初始启动最多BATCH_SIZE个任务
    const initialCount = Math.min(BATCH_SIZE, totalItems);
    for (let i = 0; i < initialCount; i++) {
      if (nextIndex < totalItems) {
        const task = processItem(nextIndex++);
        activeTasks.add(task);
        task.finally(() => {
          activeTasks.delete(task);
        });
      }
    }
    
    // 等待所有任务完成
    while (activeTasks.size > 0) {
      await Promise.race(activeTasks);
    }
    
  } catch (error: any) {
    console.error('音频处理队列失败:', error);
    callbacks.onError(error);
  }
};

/**
 * 合并多个音频片段为一个完整音频
 * @param audioUrls 音频URL数组
 * @returns 合并后的音频URL
 */
export const mergeAudioFiles = async (audioUrls: string[]): Promise<string> => {
  try {
    // 构建请求内容
    const requestData = {
      file_paths: audioUrls
    };
    
    // 发送请求
    const response = await fetch(API_ENDPOINTS.MERGE_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });
    console.log('合并音频请求响应:', response);
    if (!response.ok) {
      let errorMessage = `服务器响应错误: ${response.status}`;
      try {
        const errorText = await response.text();
        console.error('合并错误详情:', errorText);
        errorMessage += ` - ${errorText}`;
      } catch (e) {
        console.error('无法获取错误详情');
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();
    
    if (data && data.url) {
      return data.url;
    } else {
      throw new Error('返回数据格式不正确');
    }
  } catch (error: any) {
    console.error('合并失败:', error);
    throw error;
  }
}; 