import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';
import { PodcastContentItem } from '../types';
import { BATCH_SIZE } from '../types';

// 后端API端点
const CLONE_API_URL = `${MANAGER_API_BASE_URL}/api/v1/audio/tts/proxy_synthesize`;
const getToken = (): string | null => localStorage.getItem('access_token');
const token = getToken();

// 利用声音样本和提示文本克隆生成目标文本的语音
export const cloneVoice = async (
  promptAudio: Blob,
  promptText: string,
  text: string
): Promise<string> => {
  try {
    // 创建表单数据
    const formData = new FormData();
    formData.append('prompt_audio', promptAudio, 'prompt_audio.wav');
    formData.append('prompt_text', promptText);
    formData.append('text', text);

    console.log('克隆语音请求参数:', {
      prompt_text: promptText,
      text: text,
      prompt_audio_size: promptAudio.size,
      prompt_audio_type: promptAudio.type
    });

    // 发送请求
    const response = await fetch(CLONE_API_URL, {
      method: 'POST',
      headers: {
          'Authorization': `Bearer ${token}`
        },
      body: formData,
    });

    if (!response.ok) {
      let errorMsg = `服务器返回错误: ${response.status}`;
      try {
        const errorText = await response.text();
        console.error('服务器错误详情:', errorText);
        errorMsg += ` - ${errorText}`;
      } catch (e) {
        console.error('无法获取错误详情');
      }
      throw new Error(errorMsg);
    }

    // 解析响应
    const data = await response.json();
    
    if (data && data.url) {
      return data.url;
    } else {
      throw new Error('返回数据格式不正确');
    }
  } catch (error: any) {
    console.error('克隆语音生成失败:', error);
    throw error;
  }
};

// 批量生成克隆语音，使用滑动窗口方式确保同时处理BATCH_SIZE个任务
export const generateClonedVoices = async (
  recordingsMap: Map<string, Blob>,
  promptText: string,
  content: PodcastContentItem[],
  callbacks: {
    onStart: () => void,
    onItemStart: (index: number) => void,
    onItemComplete: (index: number, url: string) => void,
    onItemError: (index: number, error: Error) => void,
    onProgress: (completedCount: number, totalCount: number) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  }
): Promise<void> => {
  try {
    // 过滤出要生成的项目 - 只处理有内容且对应角色有录音的项目
    const itemsToGenerate = content.filter(item => 
      !!item.content.trim() && recordingsMap.has(item.role)
    );
    
    // 对于没有录音的角色，在控制台显示跳过信息
    content.forEach((item, index) => {
      if (!!item.content.trim() && !recordingsMap.has(item.role)) {
        console.log(`跳过生成第 ${index + 1} 个语音片段（角色 ${item.role}），因为没有对应的录音`);
        // 标记为已完成，防止进度计算错误
        callbacks.onItemComplete(index, "");
      }
    });
    
    const totalItems = itemsToGenerate.length;
    
    if (totalItems === 0) {
      callbacks.onComplete(); // 直接完成，没有要处理的内容
      return;
    }
    
    // 回调：开始生成
    callbacks.onStart();
    
    // 使用动态并行处理
    let nextIndex = 0;  // 下一个要处理的索引
    let completedCount = 0;  // 已完成的数量
    let activeCount = 0;  // 当前活跃的任务数量
    let hasError = false;  // 是否有错误发生
    
    // 创建一个函数来启动单个任务
    const startTask = async () => {
      if (nextIndex >= itemsToGenerate.length) {
        return; // 没有更多任务可启动
      }
      
      const item = itemsToGenerate[nextIndex];
      // 找到原始索引
      const originalIndex = content.findIndex(c => 
        c.role === item.role && c.content === item.content
      );
      nextIndex++;
      activeCount++;
      
      try {
        // 回调：开始处理单个项目
        callbacks.onItemStart(originalIndex);
        
        // 获取对应角色的录音
        const promptAudio = recordingsMap.get(item.role);
        if (!promptAudio) {
          throw new Error(`未找到角色"${item.role}"的声音样本`);
        }
        
        // 克隆生成语音
        const audioUrl = await cloneVoice(
          promptAudio,
          promptText,
          item.content
        );
        
        // 成功完成
        completedCount++;
        callbacks.onProgress(completedCount, totalItems);
        callbacks.onItemComplete(originalIndex, audioUrl);
      } catch (error: any) {
        callbacks.onItemError(originalIndex, error);
        hasError = true;
      } finally {
        activeCount--;
        
        // 启动下一个任务以保持并行度
        startTask();
        
        // 检查是否所有任务都已完成
        if (activeCount === 0 && nextIndex >= itemsToGenerate.length) {
          if (hasError) {
            callbacks.onError(new Error('部分语音生成失败'));
          } else {
            callbacks.onComplete();
          }
        }
      }
    };
    
    // 启动初始的批次（最多BATCH_SIZE个并行任务）
    const initialBatchSize = Math.min(BATCH_SIZE, itemsToGenerate.length);
    for (let i = 0; i < initialBatchSize; i++) {
      startTask();
    }
    
    // 注意：函数会在所有任务完成后通过回调返回结果
    // 使用递归启动任务的方式确保始终保持BATCH_SIZE个并行任务
    
  } catch (error: any) {
    callbacks.onError(error);
    throw error;
  }
}; 