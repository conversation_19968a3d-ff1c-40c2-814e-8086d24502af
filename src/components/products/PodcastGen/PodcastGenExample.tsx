import React, { useState } from 'react';
import { message } from 'antd';
import styles from './PodcastGen.module.css';
import CollapsibleInput from './components/CollapsibleInput';

// 此示例组件展示如何使用CollapsibleInput
const PodcastGenExample: React.FC = () => {
  // 输入文本
  const [inputText, setInputText] = useState('');
  // 是否正在生成内容
  const [isGenerating, setIsGenerating] = useState(false);
  // 生成的内容
  const [generatedContent, setGeneratedContent] = useState('');

  // 处理生成内容
  const handleGenerate = async () => {
    if (!inputText.trim()) {
      message.warning('请输入内容');
      return;
    }

    setIsGenerating(true);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 设置生成的内容
      setGeneratedContent(`您输入的内容是：${inputText}\n\n这是基于您输入生成的示例内容。在实际应用中，这里会显示AI生成的播客脚本。`);
      
      message.success('内容生成成功');
    } catch (error) {
      console.error('内容生成失败:', error);
      message.error('内容生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>播客生成示例</h1>
        <p className={styles.description}>
          输入主题，AI将生成播客脚本并转换为逼真的语音
        </p>
      </div>
      
      <div className={styles.content}>
        {/* 使用CollapsibleInput组件 */}
        <CollapsibleInput
          inputValue={inputText}
          onInputChange={setInputText}
          onGenerateClick={handleGenerate}
          isLoading={isGenerating}
          initialCollapsed={false}
        />
        
        {/* 显示生成的内容 */}
        {generatedContent && (
          <div className={styles.streamContent}>
            <pre>{generatedContent}</pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default PodcastGenExample; 