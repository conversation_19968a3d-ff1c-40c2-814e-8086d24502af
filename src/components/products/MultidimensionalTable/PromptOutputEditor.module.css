.container {
  width: 100%;
}

.headerTitle {
  margin-bottom: 16px;
}

.headerTitle h4 {
  margin-bottom: 8px;
}

.promptCard {
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.promptDescription {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.45);
}

.dividerSection {
  margin: 16px 0;
}

.emptyState {
  text-align: center;
  padding: 20px 0;
  color: rgba(0, 0, 0, 0.45);
}

.addButton {
  text-align: center;
  margin-top: 16px;
}

.paramForm {
  width: 100%;
}

.formItem {
  margin-bottom: 24px;
}

.formInput {
  height: 50px;
}

.formTextArea {
  font-size: 14px;
  resize: none;
}

.formRow {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.formRowItem {
  width: 50%;
  margin-bottom: 0;
}

.formSelect {
  width: 100%;
  height: 50px;
}

.requiredMark {
  color: #ff4d4f;
}

.actionColumn {
  display: flex;
  gap: 8px;
} 