import React from 'react';
import { List, Button, Progress, Typography, Popconfirm } from 'antd';
import { CheckCircleFilled, SyncOutlined, DeleteOutlined } from '@ant-design/icons';
import styles from './TaskList.module.css';

const { Text } = Typography;

interface Task {
  id: string;
  filename: string;
  progress: number;
  created_at: string;
}

interface TaskListProps {
  tasks: Task[];
  loading: boolean;
  onRefresh: () => void;
  onDownload: (taskId: string) => void;
  onDelete: (taskId: string, e: React.MouseEvent) => void;
}

/**
 * TaskList component to display and manage multi-dimensional table tasks
 */
const TaskList: React.FC<TaskListProps> = ({ 
  tasks, 
  loading, 
  onRefresh, 
  onDownload, 
  onDelete 
}) => {
  // Handle delete action with event propagation control
  const handleDelete = (taskId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the item click (download)
    onDelete(taskId, e);
  };

  return (
    <div className={styles.taskListContainer}>
      <div className={styles.taskListHeader}>
        <Button 
          onClick={onRefresh} 
          icon={<SyncOutlined />} 
          loading={loading}
          className={styles.refreshButton}
        >
          刷新列表
        </Button>
      </div>

      <List
        loading={loading}
        dataSource={tasks}
        renderItem={(item: Task) => (
          <List.Item
            key={item.id}
            className={styles.taskItem}
            onClick={() => onDownload(item.id)}
            actions={[
              <Popconfirm
                key="delete"
                title="确定要删除这个任务吗？"
                onConfirm={(e) => handleDelete(item.id, e as React.MouseEvent)}
                okText="是"
                cancelText="否"
                onCancel={(e) => e?.stopPropagation()}
              >
                <DeleteOutlined 
                  onClick={(e) => e.stopPropagation()}
                  className={styles.deleteButton}
                />
              </Popconfirm>
            ]}
          >
            <List.Item.Meta
              title={
                <div className={styles.taskTitle}>
                  {item.filename || '未命名任务'}
                  <span className={styles.taskDate}>
                    {new Date(item.created_at).toLocaleString()}
                  </span>
                </div>
              }
              description={
                <div className={styles.taskProgress}>
                  <Progress 
                    percent={item.progress} 
                    size="small"
                    status={item.progress >= 100 ? 'success' : 'active'}
                  />
                  <Text type={item.progress >= 100 ? 'success' : 'secondary'}>
                    {item.progress >= 100 ? (
                      <><CheckCircleFilled /> 已完成</>
                    ) : (
                      <><SyncOutlined spin /> 处理中</>
                    )}
                  </Text>
                </div>
              }
            />
          </List.Item>
        )}
        locale={{ emptyText: '暂无任务数据' }}
      />
    </div>
  );
};

export default TaskList;