.container {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 4px;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.pageTitle {
  text-align: center;
  padding: 2px 16px 2px 56px;
  margin: 0 !important;
  color: #262626;
  background-color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  font-weight: 500 !important;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 2px !important;
  position: relative;
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  padding-left: 4px;
  top: 25px;
}

.titleWrapper {
  width: 100%;
  max-width: 1200px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.titleContent {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 500;
  margin: 0 auto;
}

.titleContent :global(.anticon) {
  font-size: 20px;
}

.stepsContainer {
  background: #fff;
  padding: 2px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 2px;
}

.stepsContainer :global(.ant-steps-item-description) {
  max-width: none !important;
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

.contentRow {
  flex: 1;
  height: calc(100vh - 45px - 16px - 16px - 48px);
  padding: 2px 2%;
  background: #f0f2f5;
  display: flex;
  gap: 24px;
  width: 96%;
  margin: 0 auto;
  overflow: auto;
  padding-right: 12px;
}

.contentCol {
  width: 48%;
  height: 100%;
  position: relative;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.card {
  height: 100%;
  border: none;
  background: #fff;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: visible;
  flex: 1;
}

.uploadToggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.uploadToggle:hover {
  background: #f4f8ff;
}

.uploadToggle .icon {
  transition: transform 0.3s;
}

.uploadToggle .icon.collapsed {
  transform: rotate(-90deg);
}

.uploadSection {
  padding: 32px;
  text-align: center;
  background: #fafafa;
  border: 2px dashed #e8e8e8;
  border-radius: 4px;
  transition: all 0.3s;
  margin-bottom: 24px;
  overflow: hidden;
}

.uploadSection.collapsed {
  padding: 0;
  height: 0;
  margin: 0;
  border: none;
}

.uploadSection:not(.collapsed):hover {
  border-color: #1890ff;
  background: #f4f8ff;
}

.uploadSection :global(.ant-upload-drag-icon) {
  color: #1890ff;
  font-size: 48px;
  margin-bottom: 16px;
}

.uploadSection :global(.ant-upload-text) {
  font-size: 16px;
  color: #262626;
  margin: 8px 0;
}

.uploadSection :global(.ant-upload-hint) {
  color: #8c8c8c;
}

.previewTable {
  margin-bottom: 24px;
}

.previewTable h4 {
  margin-bottom: 16px !important;
  color: #262626;
  font-weight: 500;
}

.previewTable :global(.ant-table-wrapper) {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.previewTable :global(.ant-table) {
  height: 100%;
}

.previewTable :global(.ant-table-container) {
  max-height: calc(100vh - 400px);
  overflow: auto;
}

.previewTable :global(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 500;
  position: sticky;
  top: 0;
  z-index: 2;
}

.columnSelection {
  padding: 16px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.columnSelection h4 {
  margin-bottom: 16px !important;
  color: #262626;
  font-weight: 500;
}

.columnSelection :global(.ant-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.columnSelection :global(.ant-checkbox-wrapper) {
  margin-right: 0 !important;
  padding: 4px 8px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  transition: all 0.3s;
  flex-shrink: 0;
}

.columnSelection :global(.ant-checkbox-wrapper:hover) {
  border-color: #1890ff;
}

.processingSection {
  margin: 24px 0;
}

.processingSection h4 {
  margin-bottom: 16px !important;
  color: #262626;
  font-weight: 500;
}

.progressBar {
  margin: 16px 0;
}

.progressBar :global(.ant-progress-inner) {
  background-color: #f5f5f5;
}

.resultSection {
  margin: 24px 0;
  overflow-y: auto;
  flex: 1;
}

.resultSection h4 {
  margin-bottom: 16px !important;
  color: #262626;
  font-weight: 500;
}

.resultSection :global(.ant-table-wrapper) {
  overflow: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.resultSection :global(.ant-table-body) {
  overflow-y: auto !important;
  max-height: 400px;
}

.resultSection :global(.ant-table-thead > tr > th) {
  position: sticky;
  top: 0;
  z-index: 2;
  background: #f0f2f5;
  font-weight: 500;
}

.fieldConfig {
  margin-bottom: 24px;
  overflow-y: auto;
}

/* 自定义滚动条样式 */
:global(*::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:global(*::-webkit-scrollbar-thumb) {
  background: #d9d9d9;
  border-radius: 3px;
}

:global(*::-webkit-scrollbar-track) {
  background: #f5f5f5;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .contentRow {
    flex-direction: column;
    padding: 16px;
    gap: 16px;
    height: auto;
  }
  
  .contentCol {
    width: 100%;
    height: auto;
  }
  
  .card {
    height: auto;
  }
  
  .columnSelection :global(.ant-checkbox-group) {
    gap: 8px;
  }
  
  .previewTable :global(.ant-table-container),
  .resultSection :global(.ant-table-container) {
    max-height: 400px;
  }
} 

.headerSettings {
  position: absolute;
  right: 20px;
  top: 15px;
}

/* 修改侧边栏切换按钮样式 */
.sidebarToggle {
  position: absolute;
  left: 16px;
  font-size: 18px;
  z-index: 1;
}

.taskListSidebar {
  font-size: 14px;
}

.taskListHeader {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.taskListItem {
  cursor: pointer;
  transition: background-color 0.3s;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.taskListItem:hover {
  background-color: #f0f0f0;
}

.taskItemContent {
  width: 100%;
}

.taskItemHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.taskItemDate {
  font-size: 12px;
}

.taskItemProgress {
  margin-bottom: 4px;
}


/* 删除按钮样式 */
.deleteButton {
  opacity: 0.7;
  transition: opacity 0.3s;
}

.taskListItem:hover .deleteButton {
  opacity: 1;
}

.deleteButton:hover {
  background-color: rgba(255, 77, 79, 0.1);
}

.workflowDescription {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.workflowName {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

.workflowDesc {
  color: #666;
}

.promptEditorSection {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
  margin-top: 20px;
}