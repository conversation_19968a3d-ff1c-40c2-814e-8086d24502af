/* TaskList.module.css */
.taskListContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .taskListHeader {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }
  
  .taskListItem {
    cursor: pointer;
    transition: all 0.3s;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    margin-bottom: 8px;
  }
  
  .taskListItem:hover {
    background-color: #f5f5f5;
  }
  
  .taskItemContent {
    width: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .taskItemHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .taskItemDate {
    font-size: 12px;
  }
  
  .taskItemProgress {
    position: relative;
  }
  
  .taskItemStatus {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .deleteButton {
    height: 24px;
    width: 24px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }