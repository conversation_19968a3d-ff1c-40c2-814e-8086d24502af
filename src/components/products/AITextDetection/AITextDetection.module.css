.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100vh;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 32px;
  position: relative;
}

.headerSettings {
  position: absolute;
  right: 8px;
  top: 16px;
  transform: translateY(-50%);
  z-index: 10;
}

.description {
  display: block;
  margin-top: 8px;
  font-size: 16px;
  color: #666;
}

.content {
  flex: 1;
  display: flex;
  gap: 24px;
}

@media (max-width: 768px) {
  .content {
    flex-direction: column;
  }
}

.inputSection {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.resultSection {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card {
  width: 100%;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  transition: all 0.3s;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cardTitle {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.cardContent {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.textInput {
  width: 100%;
  height: 100%;
  min-height: 400px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  font-size: 16px;
  line-height: 1.6;
  resize: none;
}

.buttonGroup {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.analyzeButton {
  height: 40px;
  font-size: 16px;
  padding: 0 24px;
}

.resultItem {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.resultText {
  font-size: 14px;
  line-height: 1.6;
}

.resultMeta {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 24px;
}

.errorMessage {
  color: #ff4d4f;
  margin-top: 8px;
}

/* Probability color classes */
.probLow {
  background-color: rgba(82, 196, 26, 0.1);
  border-left: 3px solid #52c41a;
}

.probMedium {
  background-color: rgba(250, 173, 20, 0.1);
  border-left: 3px solid #faad14;
}

.probHigh {
  background-color: rgba(245, 34, 45, 0.1);
  border-left: 3px solid #f5222d;
}

.probValue {
  font-weight: bold;
}

.lowProb {
  color: #52c41a;
}

.mediumProb {
  color: #faad14;
}

.highProb {
  color: #f5222d;
}
