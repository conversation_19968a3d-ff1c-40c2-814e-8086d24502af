import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Input, Button, Spin, Alert, Toolt<PERSON> } from 'antd';
import { RobotOutlined, SendOutlined, LoadingOutlined } from '@ant-design/icons';
import { MANAGER_API_BASE_URL } from '../../../Constant/ServerConstant';
import styles from './AITextDetection.module.css';
import HeaderSettings from '../../common/HeaderSettings';

const { Title, Paragraph } = Typography;
const { TextArea } = Input;

// Define the interface for the detection result item
interface DetectionResultItem {
  text: string;
  prob: number;
  cirt: number;
  index: number;
}

const AITextDetection: React.FC = () => {
  const [inputText, setInputText] = useState<string>('');
  const [results, setResults] = useState<DetectionResultItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Function to handle text input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputText(e.target.value);
  };

  // Function to determine the probability class
  const getProbabilityClass = (prob: number): string => {
    if (prob < 0.4) return styles.probLow;
    if (prob < 0.7) return styles.probMedium;
    return styles.probHigh;
  };

  // Function to determine the probability text class
  const getProbabilityTextClass = (prob: number): string => {
    if (prob < 0.4) return styles.lowProb;
    if (prob < 0.7) return styles.mediumProb;
    return styles.highProb;
  };

  // Function to handle the detection request
  const handleDetect = async () => {
    if (!inputText.trim()) {
      setError('Please enter some text to analyze.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${MANAGER_API_BASE_URL}/detect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: inputText }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setResults(data);
    } catch (err) {
      console.error('Error detecting text:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while analyzing the text.');
    } finally {
      setLoading(false);
    }
  };

  // Function to format probability as percentage
  const formatProbability = (prob: number): string => {
    return `${(prob * 100).toFixed(1)}%`;
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Title level={2}>
          <RobotOutlined /> AI Text Detection
        </Title>
        <Paragraph className={styles.description}>
          Analyze text to detect if it was written by AI. Enter your text in the input box and click "Analyze".
        </Paragraph>
        <div className={styles.headerSettings}>
          <HeaderSettings />
        </div>
      </div>

      <div className={styles.content}>
        {/* Input Section */}
        <div className={styles.inputSection}>
          <div className={styles.card}>
            <div className={styles.cardTitle}>Input Text</div>
            <div className={styles.cardContent}>
              <TextArea
                className={styles.textInput}
                value={inputText}
                onChange={handleInputChange}
                placeholder="Enter text to analyze..."
                disabled={loading}
                autoSize={{ minRows: 15, maxRows: 30 }}
                allowClear
              />
              <div className={styles.buttonGroup}>
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleDetect}
                  loading={loading}
                  disabled={!inputText.trim()}
                  className={styles.analyzeButton}
                  size="large"
                >
                  Analyze
                </Button>
              </div>
              {error && <div className={styles.errorMessage}>{error}</div>}
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className={styles.resultSection}>
          <div className={styles.card}>
            <div className={styles.cardTitle}>Analysis Results</div>
            <div className={styles.cardContent}>
              {loading ? (
                <div className={styles.loadingContainer}>
                  <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
                  <p>Analyzing text...</p>
                </div>
              ) : results.length > 0 ? (
                results.map((item) => (
                  <div
                    key={item.index}
                    className={`${styles.resultItem} ${getProbabilityClass(item.prob)}`}
                  >
                    <div className={styles.resultText}>{item.text}</div>
                    <div className={styles.resultMeta}>
                      <Tooltip title="AI probability score">
                        <span>
                          AI Probability:{' '}
                          <span className={`${styles.probValue} ${getProbabilityTextClass(item.prob)}`}>
                            {formatProbability(item.prob)}
                          </span>
                        </span>
                      </Tooltip>
                      <Tooltip title="Criterion score">
                        <span>Criterion: {item.cirt.toFixed(2)}</span>
                      </Tooltip>
                    </div>
                  </div>
                ))
              ) : (
                <Alert
                  message="No results yet"
                  description="Enter text and click 'Analyze' to see AI detection results."
                  type="info"
                  showIcon
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AITextDetection;
