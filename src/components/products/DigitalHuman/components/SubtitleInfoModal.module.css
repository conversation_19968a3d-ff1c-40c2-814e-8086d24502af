.formContainer {
  width: 100%;
}

.formContainer :global(.ant-form-item-label) {
  font-weight: 500;
}

.formContainer :global(.ant-input-prefix) {
  color: #1890ff;
  margin-right: 8px;
}

.formContainer :global(.ant-select-selector) {
  border-radius: 6px;
}

.formContainer :global(.ant-input) {
  border-radius: 6px;
}

.imageUploadSection {
  margin-bottom: 24px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.imageUploadSection:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.imageUploadSection h4 {
  margin-bottom: 12px;
  color: #333;
  font-weight: 500;
}

.imagePreview {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.uploadDragger {
  margin-bottom: 16px;
}

.uploadDragger :global(.ant-upload-drag) {
  border-radius: 8px;
  border: 1px dashed #1890ff;
  background-color: rgba(24, 144, 255, 0.04);
  transition: all 0.3s;
}

.uploadDragger :global(.ant-upload-drag:hover) {
  border-color: #40a9ff;
  background-color: rgba(24, 144, 255, 0.08);
}

.uploadDragger :global(.ant-upload-text) {
  color: #333;
  font-size: 16px;
  margin: 8px 0;
}

.uploadDragger :global(.ant-upload-hint) {
  color: #666;
  font-size: 14px;
}

.uploadDragger :global(.ant-upload-drag-icon) {
  color: #1890ff;
  font-size: 48px;
  margin-bottom: 8px;
}

/* 折叠面板样式 */
.uploadCollapse {
  margin-top: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.uploadCollapse :global(.ant-collapse-header) {
  background-color: #f0f8ff;
  border-radius: 8px !important;
  font-weight: 500;
  color: #1890ff !important;
}

.uploadCollapse :global(.ant-collapse-content) {
  border-top: 1px solid #d6e4ff;
}

.uploadCollapse :global(.ant-collapse-item) {
  border-radius: 8px !important;
  border: 1px solid #d6e4ff !important;
  margin-bottom: 8px;
}
