.subtitleCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.formSection {
  margin-bottom: 20px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #1890ff;
}

.formContainer {
  width: 100%;
}

.formContainer :global(.ant-form-item-label) {
  font-weight: 500;
}

.formContainer :global(.ant-input-prefix) {
  color: #1890ff;
  margin-right: 8px;
}

.formContainer :global(.ant-select-selector) {
  border-radius: 6px;
}

.formContainer :global(.ant-input) {
  border-radius: 6px;
}

.imageUploadSection {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.imageUploadSection h4 {
  margin-bottom: 12px;
  color: #333;
  font-weight: 500;
}

.imagePreview {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.generateSection {
  margin-top: 24px;
  padding: 16px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #d6e4ff;
}

.progressContainer {
  margin-top: 16px;
}

.progressText {
  margin-top: 8px;
  color: #1890ff;
  text-align: center;
}

.errorContainer {
  margin-top: 16px;
  padding: 12px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
}

.errorText {
  color: #ff4d4f;
  margin: 0;
}

.resultContainer {
  margin-top: 20px;
  padding: 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.resultContainer h4 {
  align-self: flex-start;
  margin-bottom: 12px;
  color: #52c41a;
}

.successText {
  margin-top: 12px;
  color: #52c41a;
  font-weight: 500;
}

.videoPlayer {
  width: 100%;
  max-height: 300px;
  border-radius: 4px;
}
