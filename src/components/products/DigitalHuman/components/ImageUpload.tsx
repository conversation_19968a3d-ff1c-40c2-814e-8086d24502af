import React, { useState } from 'react';
import { Card, Upload, Button, message, Space, Alert, Image } from 'antd';
import { UploadOutlined, PictureOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import styles from './ImageUpload.module.css';

interface ImageUploadProps {
  isGenerating: boolean;
  onCoverImageChange: (url: string) => void;
  onEndImageChange: (url: string) => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  isGenerating,
  onCoverImageChange,
  onEndImageChange
}) => {
  const [coverImageUrl, setCoverImageUrl] = useState<string>('');
  const [endImageUrl, setEndImageUrl] = useState<string>('');
  // const [coverImageFile, setCoverImageFile] = useState<UploadFile | null>(null);
  // const [endImageFile, setEndImageFile] = useState<UploadFile | null>(null);

  // 封面图片上传配置
  const coverUploadProps: UploadProps = {
    accept: 'image/*',
    beforeUpload: (file) => {
      // 检查文件类型
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('请上传图片文件！');
        return Upload.LIST_IGNORE;
      }

      // 检查文件大小，限制为5MB
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('图片文件大小不能超过5MB！');
        return Upload.LIST_IGNORE;
      }

      // 创建临时URL用于预览
      const url = URL.createObjectURL(file);
      setCoverImageUrl(url);
      onCoverImageChange(url);
      // setCoverImageFile(file);

      message.success('封面图片上传成功！');

      // 阻止默认上传行为
      return false;
    },
    showUploadList: false,
    disabled: isGenerating,
    maxCount: 1,
  };

  // 结束图片上传配置
  const endUploadProps: UploadProps = {
    accept: 'image/*',
    beforeUpload: (file) => {
      // 检查文件类型
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('请上传图片文件！');
        return Upload.LIST_IGNORE;
      }

      // 检查文件大小，限制为5MB
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('图片文件大小不能超过5MB！');
        return Upload.LIST_IGNORE;
      }

      // 创建临时URL用于预览
      const url = URL.createObjectURL(file);
      setEndImageUrl(url);
      onEndImageChange(url);
      // setEndImageFile(file);

      message.success('结束图片上传成功！');

      // 阻止默认上传行为
      return false;
    },
    showUploadList: false,
    disabled: isGenerating,
    maxCount: 1,
  };

  // 清除封面图片
  const clearCoverImage = () => {
    if (coverImageUrl) {
      URL.revokeObjectURL(coverImageUrl);
    }
    setCoverImageUrl('');
    // setCoverImageFile(null);
    onCoverImageChange('');
  };

  // 清除结束图片
  const clearEndImage = () => {
    if (endImageUrl) {
      URL.revokeObjectURL(endImageUrl);
    }
    setEndImageUrl('');
    // setEndImageFile(null);
    onEndImageChange('');
  };

  return (
    <Card title="步骤4: 上传图片" className={styles.imageUploadCard}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div className={styles.imageUploadSection}>
          <h4>封面图片</h4>
          <Upload {...coverUploadProps}>
            <Button icon={<UploadOutlined />} disabled={isGenerating}>
              上传封面图片
            </Button>
          </Upload>

          {coverImageUrl && (
            <div className={styles.imagePreview}>
              <Image
                src={coverImageUrl}
                alt="封面图片预览"
                style={{ maxWidth: '100%', maxHeight: '200px' }}
              />
              <Button
                size="small"
                danger
                onClick={clearCoverImage}
                style={{ marginTop: '8px' }}
              >
                删除图片
              </Button>
            </div>
          )}

          {!coverImageUrl && (
            <Alert
              message="请上传封面图片"
              description="封面图片将显示在视频开始部分"
              type="info"
              showIcon
              icon={<PictureOutlined />}
            />
          )}
        </div>

        <div className={styles.imageUploadSection}>
          <h4>结束图片</h4>
          <Upload {...endUploadProps}>
            <Button icon={<UploadOutlined />} disabled={isGenerating}>
              上传结束图片
            </Button>
          </Upload>

          {endImageUrl && (
            <div className={styles.imagePreview}>
              <Image
                src={endImageUrl}
                alt="结束图片预览"
                style={{ maxWidth: '100%', maxHeight: '200px' }}
              />
              <Button
                size="small"
                danger
                onClick={clearEndImage}
                style={{ marginTop: '8px' }}
              >
                删除图片
              </Button>
            </div>
          )}

          {!endImageUrl && (
            <Alert
              message="请上传结束图片"
              description="结束图片将显示在视频结束部分"
              type="info"
              showIcon
              icon={<PictureOutlined />}
            />
          )}
        </div>
      </Space>
    </Card>
  );
};

export default ImageUpload;
