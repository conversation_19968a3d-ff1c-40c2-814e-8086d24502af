import React from 'react';
import { Card, Button, Space, Typography, Progress, Alert } from 'antd';
import { CloudUploadOutlined } from '@ant-design/icons';
import styles from '../DigitalHuman.module.css';

const { Text } = Typography;

interface GenerationControlProps {
  videoUrl: string;
  audioUrl: string;
  coverImageUrl: string;
  endImageUrl: string;
  title: string;
  isGenerating: boolean;
  progress: number;
  remainingTime: number;
  error: string | null;
  hasRecordedVideo?: boolean;
  hasRecordedAudio?: boolean;
  hasVoiceClone?: boolean;
  isDoctorInfoComplete?: boolean;
  onGenerate: () => void;
}

const GenerationControl: React.FC<GenerationControlProps> = ({
  videoUrl,
  audioUrl,
  coverImageUrl,
  endImageUrl,
  title,
  isGenerating,
  progress,
  remainingTime,
  error,
  hasRecordedVideo = false,
  hasRecordedAudio = false,
  hasVoiceClone = false,
  isDoctorInfoComplete = false,
  onGenerate
}) => {
  // 格式化剩余时间
  const formatTime = (seconds: number): string => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hrs}小时${mins}分钟${secs}秒`;
  };

  // 检查是否缺少必要的信息
  const isVideoMissing = !videoUrl || ((videoUrl === "已使用录制的视频" || videoUrl === "已上传视频文件") && !hasRecordedVideo);
  const isAudioMissing = !hasVoiceClone && (!audioUrl || ((audioUrl === "已使用录制的音频" || audioUrl === "已上传音频文件") && !hasRecordedAudio));
  const isImageMissing = !coverImageUrl || !endImageUrl;
  const isTitleMissing = !title || title.trim() === '';

  // 按钮应该只在所有必要信息都提供且没有正在生成时启用
  const isButtonDisabled = isVideoMissing || isAudioMissing || isImageMissing || !isDoctorInfoComplete || isTitleMissing || isGenerating;

  return (
    <Card title="步骤5: 生成数字人视频" className={styles.card}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Button
          type="primary"
          onClick={onGenerate}
          icon={<CloudUploadOutlined />}
          disabled={isButtonDisabled}
          loading={isGenerating}
          block
          size="large"
          className={isGenerating ? styles['btn-generating'] : ''}
        >
          {isGenerating ? '正在生成数字人视频...' : '生成数字人视频'}
        </Button>

        {isGenerating && (
          <div className={styles.progressContainer}>
            <Progress percent={Math.round(progress)} status="active" />
            <Text type="secondary">
              预计剩余时间: {formatTime(remainingTime)}，请耐心等待...
            </Text>
            <Alert
              message="数字人视频生成可能需要较长时间"
              description="生成过程中请勿关闭页面，完成后您将收到通知。"
              type="info"
              showIcon
            />
          </div>
        )}

        {error && (
          <Alert
            message="生成失败"
            description={error}
            type="error"
            showIcon
          />
        )}
      </Space>
    </Card>
  );
};

export default GenerationControl;