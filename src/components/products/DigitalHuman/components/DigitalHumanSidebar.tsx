import React, { useState, useEffect, useCallback } from 'react';
import { List, Pagination, Spin, Empty, Button, Typography, message, Tooltip, Popconfirm, Tag } from 'antd';
import { MenuFoldOutlined, MenuUnfoldOutlined, RobotOutlined, LeftOutlined, DeleteOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';
import styles from './DigitalHumanSidebar.module.css';
import { fetchBulk, deleteData } from '../../../api/api';
import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';
const { Title, Text } = Typography;

// 数字人状态常量
export const DIGITAL_HUMAN_STATUS = {
  COMPLETED: '已完成',
  GENERATING: '生成中',
  NOT_GENERATED: '未生成'
};

interface DigitalHumanItem {
  id: string;
  name: string;
  copy_writing: string;
  status: string;
  video_url: string;
  audio_url: string;
  updated_at: string;
  reference_audio_url: string;
  reference_video_url: string;
  subtitle_video_url?: string; // 可选的字幕视频URL
}

interface DigitalHumanSidebarProps {
  onSelectDigitalHuman?: (item: DigitalHumanItem) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
  disabled?: boolean; // 添加禁用属性
}

const DigitalHumanSidebar: React.FC<DigitalHumanSidebarProps> = ({
  onSelectDigitalHuman,
  collapsed,
  onToggleCollapse,
  disabled = false // 默认不禁用
}) => {
  const [digitalHumans, setDigitalHumans] = useState<DigitalHumanItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const pageSize = 10;

  // 从Redux获取用户信息
  const { userInfo } = useSelector((state: any) => state.user);
  const username = userInfo?.username || '';

  // 排序设置
  const sortField = 'updated_at';
  const sortOrder = 'desc';

  // 获取数字人列表
  const fetchDigitalHumans = async () => {
    // 如果侧边栏折叠或用户未登录，不获取数据
    if (collapsed) {
      console.log('侧边栏已折叠，跳过获取数字人数据');
      return;
    }

    if (!username) {
      message.error('用户未登录，无法获取数字人列表');
      return;
    }

    setLoading(true);
    try {
      const skip = (currentPage - 1) * pageSize;
      const limit = pageSize;
      const endpoint = `${MANAGER_API_BASE_URL}/api/v1/product/digital-human/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${username}`;

      console.log('获取数字人数据，页码:', currentPage);
      const result = await fetchBulk(endpoint);
      setDigitalHumans(result.data || []);
      setTotal(result.count || 0);
      console.log('数字人数据获取成功:', result.data?.length || 0, '条记录');
    } catch (error) {
      console.error('获取数字人数据失败:', error);
      message.error('获取数字人列表失败，请稍后再试');
      setDigitalHumans([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 删除数字人
  const handleDeleteDigitalHuman = async (id: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发项目点击

    // 如果组件被禁用，不执行任何操作
    if (disabled) {
      message.warning('正在生成中，请等待当前操作完成后再删除数字人记录');
      return;
    }

    try {
      setLoading(true);
      await deleteData(`${MANAGER_API_BASE_URL}/api/v1/product/digital-human/${id}`);
      message.success('数字人记录已删除');

      // 刷新数字人列表
      fetchDigitalHumans();
    } catch (error) {
      console.error('删除数字人记录失败:', error);
      message.error('删除数字人记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 页码变化处理
  const handlePageChange = (page: number) => {
    // 如果组件被禁用，不执行任何操作
    if (disabled) {
      message.warning('正在生成中，请等待当前操作完成后再切换页面');
      return;
    }

    setCurrentPage(page);
  };

  // 选择数字人
  const handleSelectDigitalHuman = (item: DigitalHumanItem) => {
    // 如果组件被禁用，不执行任何操作
    if (disabled) {
      message.warning('正在生成中，请等待当前操作完成后再切换数字人记录');
      return;
    }

    if (onSelectDigitalHuman) {
      onSelectDigitalHuman(item);
    }
  };

  // 获取状态标签样式
  const getStatusTagClass = (status: string) => {
    switch (status) {
      case DIGITAL_HUMAN_STATUS.COMPLETED:
        return styles.statusCompleted;
      case DIGITAL_HUMAN_STATUS.GENERATING:
        return styles.statusGenerating;
      default:
        return styles.statusNotGenerated;
    }
  };

  // 获取状态对应的项目背景样式
  const getStatusItemClass = (status: string) => {
    switch (status) {
      case DIGITAL_HUMAN_STATUS.COMPLETED:
        return styles.itemCompleted;
      case DIGITAL_HUMAN_STATUS.GENERATING:
        return styles.itemGenerating;
      default:
        return styles.itemNotGenerated;
    }
  };

  // 使用useCallback包装fetchDigitalHumans，避免无限循环
  const memoizedFetchDigitalHumans = useCallback(fetchDigitalHumans, [
    collapsed, username, currentPage, pageSize, sortField, sortOrder
  ]);

  // 监听页码变化，重新获取数据
  useEffect(() => {
    memoizedFetchDigitalHumans();
  }, [memoizedFetchDigitalHumans]);

  // 组件挂载时获取数据
  useEffect(() => {
    if (!collapsed) {
      console.log('组件挂载或侧边栏展开，自动获取最新数字人数据');
      memoizedFetchDigitalHumans();
    }
  }, [collapsed, memoizedFetchDigitalHumans]);

  // 如果侧边栏折叠，只显示折叠按钮
  if (collapsed) {
    return (
      <div className={styles.collapsedSidebar}>
        <Button
          type="text"
          icon={<MenuUnfoldOutlined />}
          onClick={onToggleCollapse}
          className={styles.collapseButton}
          disabled={disabled}
          title={disabled ? "正在生成中，请等待操作完成" : "展开侧边栏"}
        />
      </div>
    );
  }

  return (
    <div className={`${styles.sidebar} ${disabled ? styles.disabled : ''}`}>
      <div className={styles.sidebarHeader}>
        <Title level={5} style={{ margin: 0 }}>
          <RobotOutlined style={{ marginRight: 8 }} />
          数字人记录 {disabled && <Tag color="warning">生成中</Tag>}
        </Title>
        <Button
          type="text"
          icon={<MenuFoldOutlined />}
          onClick={onToggleCollapse}
          className={styles.collapseButton}
          disabled={disabled}
        />
      </div>

      <div className={styles.sidebarContent}>
        <Spin spinning={loading}>
          {digitalHumans.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="没有找到数字人记录"
            />
          ) : (
            <List
              dataSource={digitalHumans}
              renderItem={(item) => (
                <List.Item
                  className={`${styles.digitalHumanItem} ${getStatusItemClass(item.status)}`}
                  onClick={() => handleSelectDigitalHuman(item)}
                >
                  <div className={styles.digitalHumanInfo}>
                    <Tooltip title={item.copy_writing || '无文案'}>
                      <Text ellipsis style={{ maxWidth: '80%' }}>
                        {item.name}
                        <span className={`${styles.statusTag} ${getStatusTagClass(item.status)}`}>
                          {item.status}
                        </span>
                      </Text>
                    </Tooltip>
                    <Text type="secondary" className={styles.digitalHumanDate}>
                      {new Date(item.updated_at).toLocaleDateString()}
                    </Text>
                  </div>
                  <div className={styles.digitalHumanActions}>
                    <Popconfirm
                      title="确定要删除这个数字人记录吗？"
                      description="删除后将无法恢复！"
                      onConfirm={(e) => handleDeleteDigitalHuman(item.id, e as React.MouseEvent)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        type="text"
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        className={styles.deleteButton}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </Popconfirm>
                    <LeftOutlined className={styles.digitalHumanArrow} />
                  </div>
                </List.Item>
              )}
            />
          )}
        </Spin>
      </div>

      {total > pageSize && (
        <div className={styles.paginationContainer}>
          <Pagination
            current={currentPage}
            total={total}
            pageSize={pageSize}
            size="small"
            onChange={handlePageChange}
            showSizeChanger={false}
            simple
            disabled={disabled}
          />
        </div>
      )}
    </div>
  );
};

export default DigitalHumanSidebar;
