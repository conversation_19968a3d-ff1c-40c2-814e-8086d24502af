import React, { useRef } from 'react';
import { Card, Button, Space, Alert } from 'antd';
import { PlayCircleOutlined, CloudDownloadOutlined } from '@ant-design/icons';
import styles from '../DigitalHuman.module.css';

interface ResultPreviewProps {
  outputUrl: string;
}

const ResultPreview: React.FC<ResultPreviewProps> = ({ outputUrl }) => {
  const resultVideoRef = useRef<HTMLVideoElement>(null);

  // 重新加载结果视频
  const reloadResultVideo = () => {
    if (resultVideoRef.current) {
      resultVideoRef.current.load();
    }
  };

  // 处理结果视频错误
  const handleResultVideoError = () => {
    console.error('结果视频加载失败:', outputUrl);
  };

  if (!outputUrl) return null;

  return (
    <Card title="步骤6: 预览生成结果" className={styles.card}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Alert
          message="生成成功"
          description="数字人视频已成功生成，可以预览或直接下载。"
          type="success"
          showIcon
        />

        <div className={styles.previewContainer}>
          <video
            ref={resultVideoRef}
            controls
            src={outputUrl}
            className={styles.videoPreview}
            onError={handleResultVideoError}
            playsInline
          />
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            type="primary"
            onClick={reloadResultVideo}
            icon={<PlayCircleOutlined />}
          >
            重新加载视频
          </Button>

          <Button
            type="primary"
            href={outputUrl}
            target="_blank"
            icon={<CloudDownloadOutlined />}
          >
            下载生成的视频
          </Button>
        </div>
      </Space>
    </Card>
  );
};

export default ResultPreview;