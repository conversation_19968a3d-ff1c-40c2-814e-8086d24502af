.voiceCloneSimpleContainer {
  width: 100%;
}

.voiceCloneCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.voiceCloneCard :global(.ant-card-head) {
  background-color: #f0f8ff;
  border-bottom: 1px solid #d6e4ff;
}

.voiceCloneCard :global(.ant-card-head-title) {
  font-size: 18px;
  font-weight: 500;
  color: #1890ff;
}

.voiceCloneSection {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 8px;
}

.sectionHeader {
  margin-bottom: 8px;
  font-size: 16px;
  color: #1890ff;
}

.recordingControls {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.recordingControls h3 {
  margin-bottom: 8px;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.audioPreviewContainer {
  margin-top: 16px;
  padding: 16px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.audioPreviewContainer strong {
  font-size: 15px;
  color: #1890ff;
}

.audioSourceInfo {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f8ff;
  border-radius: 4px;
  border: 1px dashed #91d5ff;
}

.textInputSection {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.textInputSection h3 {
  margin-bottom: 8px;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.textInput {
  border-radius: 6px;
  resize: none;
  min-height: 120px;
  font-size: 14px;
  line-height: 1.6;
}

.generateButtonContainer {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.generateButtonContainer button {
  max-width: 200px;
  height: 40px;
  font-size: 16px;
  border-radius: 20px;
}

.audioPlayer {
  width: 100%;
  margin-top: 8px;
}

.generatedAudioContainer {
  margin-top: 16px;
  padding: 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.generatedAudioContainer h3 {
  margin-bottom: 4px;
  color: #52c41a;
  font-size: 16px;
  font-weight: 500;
}

/* 录音指示器样式 */
.recordingIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff4d4f;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
