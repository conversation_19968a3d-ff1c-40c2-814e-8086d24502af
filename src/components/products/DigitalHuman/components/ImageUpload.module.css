.imageUploadCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.imageUploadSection {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
}

.imageUploadSection h4 {
  margin-bottom: 12px;
  color: #333;
  font-weight: 500;
}

.imagePreview {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.uploadButton {
  width: 100%;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.uploadButton:hover {
  border-color: #1890ff;
}

.uploadIcon {
  font-size: 32px;
  color: #999;
  margin-bottom: 8px;
}

.uploadText {
  color: #666;
}

.imageContainer {
  margin-top: 16px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.imageActions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}
