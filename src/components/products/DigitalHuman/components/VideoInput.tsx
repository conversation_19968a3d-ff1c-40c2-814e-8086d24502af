import React, { useState, useRef, useEffect } from 'react';
import { Card, Input, Button, Space, message, Alert, Modal, Upload, Tooltip } from 'antd';
import { LinkOutlined, VideoCameraOutlined, CloseCircleOutlined, CheckCircleOutlined, UploadOutlined } from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import styles from '../DigitalHuman.module.css';

// 最小录制时间（秒）
const MIN_RECORDING_TIME = 20;

interface VideoInputProps {
  videoUrl: string;
  onVideoUrlChange: (url: string) => void;
  defaultVideoUrl: string;
  isGenerating: boolean;
  onRecordedVideoChange?: (blob: Blob | null) => void;
}

const VideoInput: React.FC<VideoInputProps> = ({
  videoUrl,
  onVideoUrlChange,
  defaultVideoUrl,
  isGenerating,
  onRecordedVideoChange
}) => {
  const [isPreviewingVideo, setIsPreviewingVideo] = useState<boolean>(true);
  const [videoPreviewError, setVideoPreviewError] = useState<boolean>(false);

  // 摄像头录制相关状态
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const [recordingTime, setRecordingTime] = useState<number>(0);
  const [showRecorder, setShowRecorder] = useState<boolean>(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [recordingTooShort, setRecordingTooShort] = useState<boolean>(false);

  // 文件上传相关状态
  const [uploadedFile, setUploadedFile] = useState<UploadFile | null>(null);

  // Refs
  const videoElementRef = useRef<HTMLVideoElement>(null);
  const videoTimerRef = useRef<NodeJS.Timeout | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);
  const videoPreviewRef = useRef<HTMLVideoElement>(null);

  // 检查URL是否有效
  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  // 处理视频URL输入
  const handleVideoUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    onVideoUrlChange(url);

    // 当输入内容变化时，重置预览状态
    setIsPreviewingVideo(false);
    setVideoPreviewError(false);

    // 使用防抖处理，避免用户输入过程中频繁触发预览
    if (videoTimerRef.current) {
      clearTimeout(videoTimerRef.current);
    }

    // 只有当输入不为空时才预览
    if (url && url.trim() !== '') {
      // 设置新的定时器，延迟500ms自动预览
      videoTimerRef.current = setTimeout(() => {
        if (isValidUrl(url)) {
          setIsPreviewingVideo(true);
        } else {
          message.warning('视频链接格式无效，请输入正确的URL');
        }
      }, 500);
    }
  };

  // 文件上传属性配置
  const uploadProps: UploadProps = {
    accept: 'video/*',
    beforeUpload: (file) => {
      // 检查文件类型
      const isVideo = file.type.startsWith('video/');
      if (!isVideo) {
        message.error('请上传视频文件！');
        return Upload.LIST_IGNORE;
      }

      // 检查文件大小，限制为100MB
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('视频文件大小不能超过100MB！');
        return Upload.LIST_IGNORE;
      }

      // 读取文件并转换为Blob
      const reader = new FileReader();
      reader.onload = () => {
        const blob = new Blob([reader.result as ArrayBuffer], { type: file.type });
        // 设置新的录制Blob
        setRecordedBlob(blob);
        if (onRecordedVideoChange) {
          onRecordedVideoChange(blob);
        }

        // 更新视频URL为提示文本
        onVideoUrlChange("已上传视频文件");
        setIsPreviewingVideo(true);

        message.success('视频文件上传成功！');
      };

      reader.onerror = () => {
        message.error('读取文件失败，请重试！');
      };

      reader.readAsArrayBuffer(file);

      // 保存上传的文件信息
      setUploadedFile(file);

      // 阻止默认上传行为
      return false;
    },
    showUploadList: false,
    disabled: isGenerating,
    maxCount: 1,
  };

  // 处理视频预览错误
  const handleVideoError = () => {
    setVideoPreviewError(true);
    message.error('视频无法加载，请检查链接或尝试其他视频');
  };

  // 手动重试预览视频
  const retryVideoPreview = () => {
    setVideoPreviewError(false);
    setIsPreviewingVideo(true);
    if (videoElementRef.current) {
      videoElementRef.current.load(); // 重新加载视频
    }
  };

  // 重置为默认视频
  const resetToDefault = () => {
    onVideoUrlChange(defaultVideoUrl);
    setIsPreviewingVideo(true);
    setVideoPreviewError(false);
    // 清除录制的视频Blob
    if (onRecordedVideoChange) {
      onRecordedVideoChange(null);
    }
    // 清除上传文件
    setUploadedFile(null);
  };

  // 清除上传的文件
  const clearUploadedFile = () => {
    setUploadedFile(null);
    if (onRecordedVideoChange) {
      onRecordedVideoChange(null);
    }
    onVideoUrlChange('');
    setIsPreviewingVideo(false);
  };

  // 打开录制摄像头Modal
  const openCameraRecorder = async () => {
    setShowRecorder(true);
    setCameraError(null);
    setRecordedBlob(null);
    setRecordingTime(0);

    try {
      // 获取摄像头权限
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: true
      });

      streamRef.current = stream;

      // 显示预览
      if (videoPreviewRef.current) {
        videoPreviewRef.current.srcObject = stream;
        videoPreviewRef.current.muted = true; // 避免回声
      }
    } catch (err) {
      console.error('获取摄像头权限失败:', err);
      setCameraError('无法访问摄像头，请确保您已授予权限并且摄像头未被其他应用程序占用。');
    }
  };

  // 开始录制
  const startRecording = () => {
    if (!streamRef.current) return;

    // 重置状态
    recordedChunksRef.current = [];
    setRecordingTime(0);
    setIsRecording(true);

    // 创建MediaRecorder实例
    const mediaRecorder = new MediaRecorder(streamRef.current, { mimeType: 'video/webm' });

    // 保存数据
    mediaRecorder.ondataavailable = (event) => {
      if (event.data && event.data.size > 0) {
        recordedChunksRef.current.push(event.data);
      }
    };

    // 录制完成处理
    mediaRecorder.onstop = () => {
      const blob = new Blob(recordedChunksRef.current, { type: 'video/webm' });
      setRecordedBlob(blob);
    };

    // 启动录制
    mediaRecorder.start(1000); // 每1秒生成一个数据块
    mediaRecorderRef.current = mediaRecorder;

    // 开始计时
    recordingTimerRef.current = setInterval(() => {
      setRecordingTime(prev => prev + 1);
    }, 1000);
  };

  // 停止录制
  const stopRecording = () => {
    // 检查是否满足最小录制时间
    if (recordingTime < MIN_RECORDING_TIME) {
      message.warning(`录制时间太短，视频需要至少${MIN_RECORDING_TIME}秒`);
      setRecordingTooShort(true);
      return;
    }

    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }

    // 清理计时器
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }

    setIsRecording(false);
    setRecordingTooShort(false);
  };

  // 使用录制的视频
  const useRecordedVideo = () => {
    if (recordedBlob) {
      // 检查视频长度是否符合要求
      if (recordingTime < MIN_RECORDING_TIME) {
        message.warning(`视频时长需要至少${MIN_RECORDING_TIME}秒，请重新录制`);
        return;
      }

      // 创建临时URL供预览使用
      // const url = URL.createObjectURL(recordedBlob);
      // 清空输入框中的URL（显示一个提示文本）
      onVideoUrlChange("已使用录制的视频");
      // 调用回调函数传递录制的视频Blob
      if (onRecordedVideoChange) {
        onRecordedVideoChange(recordedBlob);
      }
      setIsPreviewingVideo(true);
      message.success('已成功设置为录制的视频');
      closeRecorder();
    }
  };

  // 关闭录制器
  const closeRecorder = () => {
    // 停止录制
    if (isRecording) {
      stopRecording();
    }

    // 停止摄像头
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    setShowRecorder(false);
  };

  // 格式化时间
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      // 清理视频预览定时器
      if (videoTimerRef.current) {
        clearTimeout(videoTimerRef.current);
      }

      // 清理录制定时器和摄像头
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }

      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  return (
    <>
      <Card title="步骤2: 输入视频链接" className={styles.card}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
            <Input
              placeholder="输入视频链接URL"
              value={videoUrl}
              onChange={handleVideoUrlChange}
              prefix={<LinkOutlined />}
              disabled={isGenerating || uploadedFile !== null}
              allowClear
              style={{ flex: 1, marginRight: '8px' }}
            />
            <Button
              onClick={resetToDefault}
              title="恢复默认视频链接"
              style={{ marginRight: '8px' }}
            >
              恢复默认
            </Button>
            <Space>
              <Upload {...uploadProps}>
                <Tooltip title="上传视频文件">
                  <Button
                    icon={<UploadOutlined />}
                    disabled={isGenerating}
                  >
                    上传视频
                  </Button>
                </Tooltip>
              </Upload>
              <Button
                type="primary"
                icon={<VideoCameraOutlined />}
                onClick={openCameraRecorder}
                disabled={isGenerating}
                title="使用摄像头录制视频"
              >
                录制视频
              </Button>
            </Space>
          </div>

          {/* 显示上传的文件信息 */}
          {uploadedFile && videoUrl === "已上传视频文件" && (
            <div style={{ marginBottom: '10px' }}>
              <Alert
                message={`已上传视频: ${uploadedFile.name}`}
                type="success"
                action={
                  <Button size="small" danger onClick={clearUploadedFile}>
                    删除
                  </Button>
                }
              />
            </div>
          )}

          {isPreviewingVideo && videoUrl && !videoPreviewError && videoUrl !== "已使用录制的视频" && videoUrl !== "已上传视频文件" && (
            <div className={styles.previewContainer}>
              <video
                ref={videoElementRef}
                controls
                src={videoUrl}
                className={styles.videoPreview}
                onError={handleVideoError}
                playsInline
              />
            </div>
          )}

          {recordedBlob && videoUrl === "已使用录制的视频" && (
            <div className={styles.previewContainer}>
              <video
                controls
                src={URL.createObjectURL(recordedBlob)}
                className={styles.videoPreview}
                playsInline
              />
              <div style={{ marginTop: '8px', color: '#1890ff' }}>
                使用的是录制的视频
              </div>
            </div>
          )}

          {recordedBlob && videoUrl === "已上传视频文件" && (
            <div className={styles.previewContainer}>
              <video
                controls
                src={URL.createObjectURL(recordedBlob)}
                className={styles.videoPreview}
                playsInline
              />
              <div style={{ marginTop: '8px', color: '#1890ff' }}>
                使用的是上传的视频
              </div>
            </div>
          )}

          {videoPreviewError && (
            <Alert
              message="视频预览失败"
              description="无法加载视频，请检查链接是否有效或尝试其他视频链接。"
              type="error"
              showIcon
              action={
                <Button size="small" type="primary" onClick={retryVideoPreview}>
                  重试
                </Button>
              }
            />
          )}
        </Space>
      </Card>

      {/* 摄像头录制Modal */}
      <Modal
        title="摄像头录制"
        open={showRecorder}
        onCancel={closeRecorder}
        width={800}
        footer={null}
        destroyOnHidden
      >
        <div className={styles.recorderContainer}>
          {cameraError ? (
            <Alert
              message="摄像头错误"
              description={cameraError}
              type="error"
              showIcon
            />
          ) : (
            <>
              <div className={styles.videoRecorderPreview}>
                {!recordedBlob ? (
                  <video
                    ref={videoPreviewRef}
                    autoPlay
                    playsInline
                    muted
                    className={styles.cameraPreview}
                  />
                ) : (
                  <video
                    src={URL.createObjectURL(recordedBlob)}
                    controls
                    playsInline
                    className={styles.recordedPreview}
                  />
                )}
              </div>

              <div className={styles.recorderControls}>
                {!recordedBlob ? (
                  <>
                    <div className={styles.recordingTime}>
                      {isRecording && (
                        <span className={styles.recordingIndicator}>
                          ● 录制中: {formatTime(recordingTime)}
                          {recordingTime < MIN_RECORDING_TIME && (
                            <span style={{ marginLeft: '8px', color: '#ff4d4f' }}>
                              (需要至少录制 {MIN_RECORDING_TIME} 秒) (可以有适当的动作)
                            </span>
                          )}
                        </span>
                      )}
                      {!isRecording && !recordedBlob && (
                        <span>准备就绪，点击"开始录制"按钮开始录制，视频需要至少 {MIN_RECORDING_TIME} 秒</span>
                      )}
                    </div>

                    <Space>
                      {!isRecording ? (
                        <Button
                          type="primary"
                          icon={<VideoCameraOutlined />}
                          onClick={startRecording}
                        >
                          开始录制
                        </Button>
                      ) : (
                        <Button
                          danger
                          icon={<CloseCircleOutlined />}
                          onClick={stopRecording}
                          disabled={recordingTime < MIN_RECORDING_TIME}
                        >
                          {recordingTime < MIN_RECORDING_TIME
                            ? `还需录制 ${MIN_RECORDING_TIME - recordingTime} 秒`
                            : '停止录制'}
                        </Button>
                      )}
                      <Button onClick={closeRecorder}>取消</Button>
                    </Space>
                  </>
                ) : (
                  <>
                    {recordingTooShort && (
                      <Alert
                        message="录制时间不足"
                        description={`视频录制时间需要至少${MIN_RECORDING_TIME}秒，当前录制时间为${recordingTime}秒，请重新录制。`}
                        type="warning"
                        showIcon
                        style={{ marginBottom: '10px' }}
                      />
                    )}
                    <Space>
                      <Button
                        type="primary"
                        icon={<CheckCircleOutlined />}
                        onClick={useRecordedVideo}
                        disabled={recordingTime < MIN_RECORDING_TIME}
                      >
                        使用此视频
                      </Button>
                      <Button onClick={() => {
                        setRecordedBlob(null);
                        setRecordingTime(0);
                        setRecordingTooShort(false);
                      }}>重新录制</Button>
                      <Button onClick={closeRecorder}>取消</Button>
                    </Space>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </Modal>
    </>
  );
};

export default VideoInput;