import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, Input, Upload, Button, message, Progress } from 'antd';
import { 
  UploadOutlined, VideoCameraOutlined, RobotOutlined, LoadingOutlined, ReloadOutlined
 } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import styles from './DigitalHumanInput.module.css';
import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';
import { DIGITAL_HUMAN_STATUS } from './DigitalHumanSidebar';

interface DigitalHumanInputProps {
  isGenerating: boolean;
  videoUrl: string;
  onVideoUrlChange: (url: string) => void;
  onTitleChange: (title: string) => void;
  onRecordedVideoChange: (blob: Blob | null) => void;
  onDigitalHumanGenerated: (url: string) => void;
  hasVoiceClone: boolean;
  digitalHumanId?: string;
  digitalHumanName?: string;
  clonedAudioUrl?: string;
}
const getToken = (): string | null => localStorage.getItem('access_token');

const token = getToken();
const DigitalHumanInput: React.FC<DigitalHumanInputProps> = ({
  isGenerating,
  videoUrl,
  onVideoUrlChange,
  onTitleChange,
  onRecordedVideoChange,
  onDigitalHumanGenerated,
  hasVoiceClone,
  digitalHumanId = '',
  digitalHumanName = '',
  clonedAudioUrl = ''
}) => {
  const [form] = Form.useForm();
  const [videoPreviewUrl, setVideoPreviewUrl] = useState<string>('');
  const [isVideoUploaded, setIsVideoUploaded] = useState<boolean>(false);
  const [isVideoUrlValid, setIsVideoUrlValid] = useState<boolean>(true);
  const [isGeneratingDigitalHuman, setIsGeneratingDigitalHuman] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [digitalHumanUrl, setDigitalHumanUrl] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // 处理视频URL输入
  const handleVideoUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    onVideoUrlChange(url);

    // 简单验证URL格式
    const isValid = !url || /^https?:\/\/.+/.test(url);
    setIsVideoUrlValid(isValid);
  };

  // 处理标题输入
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onTitleChange(e.target.value);
  };

  // 处理视频文件上传
  const handleVideoUpload: UploadProps['beforeUpload'] = (file) => {
    // 检查文件类型
    const isVideo = file.type.startsWith('video/');
    if (!isVideo) {
      message.error('请上传视频文件！');
      return Upload.LIST_IGNORE;
    }

    // 检查文件大小（限制为100MB）
    const isLt100M = file.size / 1024 / 1024 < 100;
    if (!isLt100M) {
      message.error('视频文件大小不能超过100MB！');
      return Upload.LIST_IGNORE;
    }

    // 创建临时URL用于预览
    const url = URL.createObjectURL(file);
    setVideoPreviewUrl(url);
    setIsVideoUploaded(true);
    onVideoUrlChange('已上传视频文件');
    onRecordedVideoChange(file);

    message.success('视频上传成功！');

    // 阻止默认上传行为
    return false;
  };

  // 清除上传的视频
  const clearVideo = () => {
    if (videoPreviewUrl) {
      URL.revokeObjectURL(videoPreviewUrl);
    }
    setVideoPreviewUrl('');
    setIsVideoUploaded(false);
    onVideoUrlChange('');
    onRecordedVideoChange(null);
  };

  // 添加状态变量用于轮询任务状态
  const [taskCode, setTaskCode] = useState<string>('');
  const [isPolling, setIsPolling] = useState<boolean>(false);
  const [statusCheckCount, setStatusCheckCount] = useState<number>(0);
  const statusCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 检查任务状态
  const checkTaskStatus = async (code: string) => {
    if (!code) return;

    try {
      // 创建FormData用于请求
      const formData = new FormData();
      formData.append('code', code);

      // 如果有数字人ID，也添加到请求中
      if (digitalHumanId && digitalHumanId !== 'new') {
        formData.append('digital_human_id', digitalHumanId);
      }

      // 发送请求
      const STATUS_API = `${MANAGER_API_BASE_URL}/api/v1/video/digital_human/status`;
      const response = await fetch(STATUS_API, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`服务器返回错误 (${response.status}: ${response.statusText})`);
      }

      const result = await response.json();
      console.log('任务状态检查结果:', result);

      // 更新进度
      if (result.progress) {
        setProgress(result.progress);
      }

      // 如果任务完成并有URL
      if (result.url) {
        // 停止轮询
        if (statusCheckIntervalRef.current) {
          clearInterval(statusCheckIntervalRef.current);
          statusCheckIntervalRef.current = null;
        }
        setIsPolling(false);

        // 更新状态
        setDigitalHumanUrl(result.url);
        setProgress(100);
        message.success('数字人视频生成成功！');

        // 通知父组件
        onDigitalHumanGenerated(result.url);
      } else {
        // 增加检查次数
        setStatusCheckCount(prev => prev + 1);

        // 如果检查次数过多（例如超过60次，即10分钟），停止轮询
        if (statusCheckCount > 60) {
          if (statusCheckIntervalRef.current) {
            clearInterval(statusCheckIntervalRef.current);
            statusCheckIntervalRef.current = null;
          }
          setIsPolling(false);
          throw new Error('任务处理时间过长，请稍后在侧边栏查看结果');
        }
      }
    } catch (err: any) {
      console.error('检查任务状态错误:', err.message);
      // 不要在每次检查失败时都显示错误消息，避免过多提示
      if (!isPolling) {
        message.error(err.message || '检查任务状态失败');
      }
    }
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (statusCheckIntervalRef.current) {
        clearInterval(statusCheckIntervalRef.current);
      }
    };
  }, []);

  // 生成数字人视频
  const generateDigitalHuman = async () => {
    if (!videoUrl && !isVideoUploaded) {
      message.error('请先上传视频或提供视频链接');
      return;
    }

    if (!hasVoiceClone) {
      message.error('请先完成声音克隆');
      return;
    }

    // 重置状态
    setIsGeneratingDigitalHuman(true);
    setProgress(0);
    setError(null);
    setTaskCode('');
    setStatusCheckCount(0);

    // 停止之前的轮询（如果有）
    if (statusCheckIntervalRef.current) {
      clearInterval(statusCheckIntervalRef.current);
      statusCheckIntervalRef.current = null;
    }

    try {
      // 创建FormData用于上传文件
      const formData = new FormData();

      // 处理视频参数
      if (isVideoUploaded && videoPreviewUrl) {
        // 从视频预览URL获取Blob
        const response = await fetch(videoPreviewUrl);
        const blob = await response.blob();
        formData.append('video_file', blob, 'video.mp4');
      } else if (videoUrl) {
        // 使用视频URL
        formData.append('video_path', videoUrl);
      } else {
        throw new Error('必须提供视频文件或URL');
      }

      // 添加音频参数 - 使用声音克隆生成的音频URL
      if (clonedAudioUrl) {
        formData.append('audio_path', clonedAudioUrl);
      } else {
        throw new Error('必须提供音频URL');
      }

      // 添加code参数，优先使用保存的数字人名称
      if (digitalHumanName) {
        formData.append('code', digitalHumanName);
        setTaskCode(digitalHumanName);
      }

      // 添加数字人ID（如果有）
      if (digitalHumanId && digitalHumanId !== 'new') {
        formData.append('digital_human_id', digitalHumanId);
      }

      // 发送请求
      const DIGITAL_HUMAN_API = `${MANAGER_API_BASE_URL}/api/v1/video/digital_human/generate`;
      console.log('发送数字人生成请求到:');
      console.log('请求参数:', {
        视频: formData.get('video_file') ? '已上传视频文件' : formData.get('video_path'),
        音频: formData.get('audio_path'),
        code: formData.get('code'),
        digital_human_id: formData.get('digital_human_id')
      });

      const response = await fetch(DIGITAL_HUMAN_API, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });

      // 处理API响应
      if (!response.ok) {
        let errorMsg = '';
        try {
          const errorData = await response.json();
          errorMsg = errorData.detail || `服务器返回错误 (${response.status}: ${response.statusText})`;
        } catch (jsonError) {
          // 如果返回的不是JSON格式，直接使用状态文本
          errorMsg = `服务器返回错误 (${response.status}: ${response.statusText})`;
        }

        console.error('API错误详情:', errorMsg);
        throw new Error(errorMsg);
      }

      const result = await response.json();
      console.log('数字人生成任务提交结果:', result);

      // 检查响应状态
      if (result.status === DIGITAL_HUMAN_STATUS.GENERATING) {
        message.info('数字人生成任务已提交，正在后台处理中...');

        // 开始轮询任务状态
        setIsPolling(true);
        statusCheckIntervalRef.current = setInterval(() => {
          checkTaskStatus(digitalHumanName);
        }, 10000); // 每10秒检查一次

        // 立即进行第一次检查
        setTimeout(() => {
          checkTaskStatus(digitalHumanName);
        }, 3000);
      } else {
        throw new Error('任务提交失败: ' + (result.detail || '未知错误'));
      }
    } catch (err: any) {
      const errorMessage = err.message || '生成过程中发生错误';
      console.error('数字人生成错误:', errorMessage);
      setError(errorMessage);
      message.error('生成数字人视频失败，请稍后重试');

      // 停止轮询
      if (statusCheckIntervalRef.current) {
        clearInterval(statusCheckIntervalRef.current);
        statusCheckIntervalRef.current = null;
      }
      setIsPolling(false);
    } finally {
      if (!isPolling) {
        setIsGeneratingDigitalHuman(false);
      }
    }
  };

  return (
    <Card title="步骤2: 生成数字人" className={styles.digitalHumanCard}>
      <Form
        form={form}
        layout="vertical"
        initialValues={{ videoUrl: videoUrl }}
      >
        <div className={styles.formSection}>
          <h3 className={styles.sectionTitle}>
            <VideoCameraOutlined /> 视频信息
          </h3>

          <Form.Item
            name="title"
            label="视频标题"
            rules={[{ required: true, message: '请输入视频标题' }]}
          >
            <Input
              placeholder="请输入视频标题"
              onChange={handleTitleChange}
              disabled={isGenerating || isGeneratingDigitalHuman}
            />
          </Form.Item>

          <Form.Item
            name="videoUrl"
            label="视频链接"
            validateStatus={isVideoUrlValid ? 'success' : 'error'}
            help={isVideoUrlValid ? undefined : '请输入有效的URL地址，以http://或https://开头'}
            rules={[{ required: !isVideoUploaded, message: '请输入视频链接或上传视频文件' }]}
          >
            <Input
              placeholder="请输入视频链接，或上传视频文件"
              onChange={handleVideoUrlChange}
              disabled={isGenerating || isGeneratingDigitalHuman || isVideoUploaded}
              value={videoUrl}
            />
          </Form.Item>

          <div className={styles.uploadSection}>
            <Upload
              beforeUpload={handleVideoUpload}
              showUploadList={false}
              accept="video/*"
              disabled={isGenerating || isGeneratingDigitalHuman}
            >
              <Button
                icon={<UploadOutlined />}
                disabled={isGenerating || isGeneratingDigitalHuman}
              >
                上传视频文件
              </Button>
            </Upload>

            <p className={styles.uploadTip}>
              支持MP4、WebM等常见视频格式，文件大小不超过100MB
            </p>
          </div>

          {videoPreviewUrl && (
            <div className={styles.videoPreview}>
              <h4>视频预览</h4>
              <video
                controls
                src={videoPreviewUrl}
                className={styles.videoPlayer}
              ></video>
              <Button
                danger
                onClick={clearVideo}
                disabled={isGenerating || isGeneratingDigitalHuman}
              >
                删除视频
              </Button>
            </div>
          )}

          {videoUrl && !videoPreviewUrl && !isVideoUploaded && (
            <div className={styles.videoPreview}>
              <h4>视频链接预览</h4>
              <p className={styles.videoUrlPreview}>{videoUrl}</p>
            </div>
          )}
        </div>

        <div className={styles.generateSection}>
          <Button
            type="primary"
            icon={<RobotOutlined />}
            onClick={generateDigitalHuman}
            loading={isGeneratingDigitalHuman}
            disabled={isGenerating || isGeneratingDigitalHuman || (!videoUrl && !isVideoUploaded) || !hasVoiceClone}
            block
            size="large"
          >
            {isGeneratingDigitalHuman ? '生成数字人中...' : '生成数字人'}
          </Button>

          {isGeneratingDigitalHuman && (
            <div className={styles.progressContainer}>
              <Progress percent={progress} status="active" />
              <p className={styles.progressText}>
                <LoadingOutlined /> 正在生成数字人视频，请耐心等待...
              </p>
              {isPolling && taskCode && (
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={() => checkTaskStatus(taskCode)}
                  style={{ marginTop: 10 }}
                >
                  手动检查状态
                </Button>
              )}
            </div>
          )}

          {error && (
            <div className={styles.errorContainer}>
              <p className={styles.errorText}>{error}</p>
            </div>
          )}

          {digitalHumanUrl && (
            <div className={styles.resultContainer}>
              <h4>数字人视频预览</h4>
              <video
                controls
                src={digitalHumanUrl}
                className={styles.videoPlayer}
              ></video>
              <p className={styles.successText}>数字人视频生成成功！现在您可以继续下一步添加字幕。</p>
            </div>
          )}
        </div>
      </Form>
    </Card>
  );
};

export default DigitalHumanInput;
