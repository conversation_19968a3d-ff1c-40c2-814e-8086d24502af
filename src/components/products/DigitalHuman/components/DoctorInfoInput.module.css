.doctorInfoCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.formContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.formItem {
  margin-bottom: 16px;
}

.formItem :global(.ant-form-item-label) {
  font-weight: 500;
}

.formItem :global(.ant-input-prefix) {
  color: #1890ff;
  margin-right: 8px;
}

.formItem :global(.ant-select-selector) {
  border-radius: 6px;
}

.formItem :global(.ant-input) {
  border-radius: 6px;
}

.formSection {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 16px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #1890ff;
  display: flex;
  align-items: center;
}

.sectionTitle :global(.anticon) {
  margin-right: 8px;
}
