import React from 'react';
import { Card, Form, Input, Select, Space } from 'antd';
import { UserOutlined, MedicineBoxOutlined, BankOutlined } from '@ant-design/icons';
import styles from './DoctorInfoInput.module.css';

const { Option } = Select;

interface DoctorInfoInputProps {
  isGenerating: boolean;
  onDoctorInfoChange: (values: DoctorInfo) => void;
}

export interface DoctorInfo {
  doctor_name: string;
  doctor_title: string;
  doctor_department: string;
  doctor_hospital: string;
  background_position: string;
  theme_text: string;
}

const DoctorInfoInput: React.FC<DoctorInfoInputProps> = ({
  isGenerating,
  onDoctorInfoChange
}) => {
  const [form] = Form.useForm();

  const handleValuesChange = () => {
    const values = form.getFieldsValue();
    onDoctorInfoChange(values);
  };

  return (
    <Card title="步骤3: 输入医生信息" className={styles.doctorInfoCard}>
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
        initialValues={{
          background_position: 'right',
          theme_text: '健康科普'
        }}
      >
        <Space direction="vertical" className={styles.formContainer}>
          <Form.Item
            name="doctor_name"
            label="医生姓名"
            rules={[{ required: true, message: '请输入医生姓名' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入医生姓名"
              disabled={isGenerating}
            />
          </Form.Item>

          <Form.Item
            name="doctor_title"
            label="医生职称"
            rules={[{ required: true, message: '请输入医生职称' }]}
          >
            <Input
              prefix={<MedicineBoxOutlined />}
              placeholder="请输入医生职称，如：主任医师、副主任医师等"
              disabled={isGenerating}
            />
          </Form.Item>

          <Form.Item
            name="doctor_department"
            label="所属科室"
            rules={[{ required: true, message: '请输入所属科室' }]}
          >
            <Input
              prefix={<MedicineBoxOutlined />}
              placeholder="请输入所属科室，如：内科、外科等"
              disabled={isGenerating}
            />
          </Form.Item>

          <Form.Item
            name="doctor_hospital"
            label="所属医院"
            rules={[{ required: true, message: '请输入所属医院' }]}
          >
            <Input
              prefix={<BankOutlined />}
              placeholder="请输入所属医院"
              disabled={isGenerating}
            />
          </Form.Item>

          <Form.Item
            name="theme_text"
            label="主题文字"
            rules={[{ required: true, message: '请输入主题文字' }]}
          >
            <Input
              placeholder="请输入主题文字，如：健康科普、疾病预防等"
              disabled={isGenerating}
            />
          </Form.Item>

          <Form.Item
            name="background_position"
            label="背景位置"
            rules={[{ required: true, message: '请选择背景位置' }]}
          >
            <Select disabled={isGenerating}>
              <Option value="right">右侧</Option>
              <Option value="left">左侧</Option>
              <Option value="center">居中</Option>
            </Select>
          </Form.Item>
        </Space>
      </Form>
    </Card>
  );
};

export default DoctorInfoInput;
