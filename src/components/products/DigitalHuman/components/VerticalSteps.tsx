import React from 'react';
import { Steps } from 'antd';
import { SoundOutlined, VideoCameraOutlined, FileTextOutlined } from '@ant-design/icons';
import styles from './VerticalSteps.module.css';

const { Step } = Steps;

interface VerticalStepsProps {
  current: number;
  onChange: (current: number) => void;
}

const VerticalSteps: React.FC<VerticalStepsProps> = ({ current, onChange }) => {
  return (
    <div className={styles.verticalStepsContainer}>
      <Steps
        direction="vertical"
        current={current}
        onChange={onChange}
        className={styles.verticalSteps}
      >
        <Step
          title="声音克隆"
          icon={<SoundOutlined />}
          description="录制或上传声音"
          className={current === 0 ? styles.activeStep : (current > 0 ? styles.completedStep : '')}
        />
        <Step
          title="生成数字人"
          icon={<VideoCameraOutlined />}
          description="上传视频和基本信息"
          className={current === 1 ? styles.activeStep : (current > 1 ? styles.completedStep : '')}
        />
        <Step
          title="添加字幕"
          icon={<FileTextOutlined />}
          description="添加医生信息和图片"
          className={current === 2 ? styles.activeStep : (current > 2 ? styles.completedStep : '')}
        />
      </Steps>
    </div>
  );
};

export default VerticalSteps;
