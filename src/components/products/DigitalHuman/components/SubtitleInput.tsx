import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Select, Upload, Button, Space, message, Image, Divider, Progress } from 'antd';
import { UserOutlined, MedicineBoxOutlined, BankOutlined, UploadOutlined, FileTextOutlined, LoadingOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import styles from './SubtitleInput.module.css';
import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';
import { DIGITAL_HUMAN_STATUS } from './DigitalHumanSidebar';

const { Option } = Select;

// 默认图片URL
const DEFAULT_COVER_IMAGE_URL = "https://minio.aimed.cn/johnson/llm-agent/2025-5-15/cover_image.png";
const DEFAULT_END_IMAGE_URL = "https://minio.aimed.cn/johnson/llm-agent/2025-5-15/end_image.png";

interface SubtitleInputProps {
  isGenerating: boolean;
  onDoctorInfoChange: (values: DoctorInfo) => void;
  onCoverImageChange: (url: string) => void;
  onEndImageChange: (url: string) => void;
  onSubtitleGenerated: (url: string) => void;
  digitalHumanUrl: string;
  title: string;
  voiceCloneText: string;
}

export interface DoctorInfo {
  doctor_name: string;
  doctor_title: string;
  doctor_department: string;
  doctor_hospital: string;
  background_position: string;
  theme_text: string;
}

const SubtitleInput: React.FC<SubtitleInputProps> = ({
  isGenerating,
  onDoctorInfoChange,
  onCoverImageChange,
  onEndImageChange,
  onSubtitleGenerated,
  digitalHumanUrl,
  title,
  voiceCloneText
}) => {
  const [form] = Form.useForm();
  const [coverImageUrl, setCoverImageUrl] = useState<string>(DEFAULT_COVER_IMAGE_URL);
  const [endImageUrl, setEndImageUrl] = useState<string>(DEFAULT_END_IMAGE_URL);
  const [isGeneratingSubtitle, setIsGeneratingSubtitle] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const getToken = (): string | null => localStorage.getItem('access_token');
  // 处理表单值变化
  const handleValuesChange = () => {
    const values = form.getFieldsValue();
    onDoctorInfoChange(values);
  };

  // 初始化时通知父组件默认图片URL
  useEffect(() => {
    onCoverImageChange(DEFAULT_COVER_IMAGE_URL);
    onEndImageChange(DEFAULT_END_IMAGE_URL);
  }, [onCoverImageChange, onEndImageChange]);

  // 封面图片上传配置
  const coverUploadProps: UploadProps = {
    accept: 'image/*',
    beforeUpload: (file) => {
      // 检查文件类型
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('请上传图片文件！');
        return Upload.LIST_IGNORE;
      }

      // 检查文件大小，限制为5MB
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('图片文件大小不能超过5MB！');
        return Upload.LIST_IGNORE;
      }

      // 创建临时URL用于预览
      const url = URL.createObjectURL(file);
      setCoverImageUrl(url);
      onCoverImageChange(url);

      message.success('封面图片上传成功！');

      // 阻止默认上传行为
      return false;
    },
    showUploadList: false,
    disabled: isGenerating,
    maxCount: 1,
  };

  // 结束图片上传配置
  const endUploadProps: UploadProps = {
    accept: 'image/*',
    beforeUpload: (file) => {
      // 检查文件类型
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('请上传图片文件！');
        return Upload.LIST_IGNORE;
      }

      // 检查文件大小，限制为5MB
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('图片文件大小不能超过5MB！');
        return Upload.LIST_IGNORE;
      }

      // 创建临时URL用于预览
      const url = URL.createObjectURL(file);
      setEndImageUrl(url);
      onEndImageChange(url);

      message.success('结束图片上传成功！');

      // 阻止默认上传行为
      return false;
    },
    showUploadList: false,
    disabled: isGenerating,
    maxCount: 1,
  };

  // 清除封面图片
  const clearCoverImage = () => {
    if (coverImageUrl) {
      URL.revokeObjectURL(coverImageUrl);
    }
    setCoverImageUrl('');
    onCoverImageChange('');
  };

  // 清除结束图片
  const clearEndImage = () => {
    if (endImageUrl) {
      URL.revokeObjectURL(endImageUrl);
    }
    setEndImageUrl('');
    onEndImageChange('');
  };

  // 生成字幕
  const generateSubtitle = async () => {
    if (!digitalHumanUrl) {
      message.error('请先生成数字人视频');
      return;
    }

    if (!coverImageUrl || !endImageUrl) {
      message.error('请上传封面图片和结束图片');
      return;
    }

    const values = form.getFieldsValue();
    if (!values.doctor_name || !values.doctor_title || !values.doctor_department || !values.doctor_hospital) {
      message.error('请填写完整的医生信息');
      return;
    }

    // 重置状态
    setIsGeneratingSubtitle(true);
    setProgress(0);
    setError(null);

    try {
      // 创建请求数据
      const requestData = {
        title: title,
        video_url: digitalHumanUrl,
        theme_text: values.theme_text,
        cover_image_url: coverImageUrl,
        end_image_url: endImageUrl,
        doctor_name: values.doctor_name,
        doctor_title: values.doctor_title,
        doctor_department: values.doctor_department,
        doctor_hospital: values.doctor_hospital,
        background_position: values.background_position,
        origin_text: voiceCloneText // 用来生成语音克隆的文本
      };

      console.log('提交数据:', requestData);

      // 设置进度更新定时器
      const startTime = Date.now();
      const timer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const totalEstimatedTime = 120; // 预计2分钟
        const progressPercent = Math.min(99, (elapsed / totalEstimatedTime) * 100);
        setProgress(progressPercent);

        if (elapsed >= totalEstimatedTime) {
          clearInterval(timer);
        }
      }, 1000);
            // 获取认证 token
      const token = getToken();
      // 发送请求到字幕API
      const SUBTITLE_API = `${MANAGER_API_BASE_URL}/api/v1/video/proxy/subtitle`;
      const response = await fetch(SUBTITLE_API, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestData),
      });

      // 清除定时器
      clearInterval(timer);

      // 处理API响应
      if (!response.ok) {
        let errorMsg = '';
        try {
          const errorData = await response.json();
          errorMsg = errorData.detail || `服务器返回错误 (${response.status}: ${response.statusText})`;
        } catch (jsonError) {
          // 如果返回的不是JSON格式，直接使用状态文本
          errorMsg = `服务器返回错误 (${response.status}: ${response.statusText})`;
        }

        console.error('API错误详情:', errorMsg);
        throw new Error(errorMsg);
      }

      const result = await response.json();
      console.log('字幕API响应:', result);

      // API现在只返回状态，不再直接返回URL
      if (result.status === DIGITAL_HUMAN_STATUS.GENERATING) {
        // 设置进度为99%，表示请求已成功提交，等待后台处理
        setProgress(99);

        message.info('字幕添加请求已提交，正在后台处理中，请稍后刷新页面查看结果');

        // 通知父组件请求已提交（传递空字符串表示处理中）
        onSubtitleGenerated('');

        // 添加一个定时器，提示用户可以刷新页面查看结果
        setTimeout(() => {
          message.info('您可以稍后刷新页面查看处理结果');
        }, 5000);
      } else {
        throw new Error('字幕API返回了意外的状态: ' + result.status);
      }

    } catch (err: any) {
      const errorMessage = err.message || '生成过程中发生错误';
      console.error('字幕添加错误:', errorMessage);
      setError(errorMessage);
      message.error('添加字幕失败，请稍后重试');
    } finally {
      setIsGeneratingSubtitle(false);
    }
  };

  return (
    <Card title="步骤3: 添加字幕" className={styles.subtitleCard}>
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
        initialValues={{
          background_position: 'left',
          theme_text: '健康科普'
        }}
      >
        <div className={styles.formSection}>
          <h3 className={styles.sectionTitle}>医生信息</h3>

          <Space direction="vertical" className={styles.formContainer}>
            <Form.Item
              name="doctor_name"
              label="医生姓名"
              rules={[{ required: true, message: '请输入医生姓名' }]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入医生姓名"
                disabled={isGenerating || isGeneratingSubtitle}
              />
            </Form.Item>

            <Form.Item
              name="doctor_title"
              label="医生职称"
              rules={[{ required: true, message: '请输入医生职称' }]}
            >
              <Input
                prefix={<MedicineBoxOutlined />}
                placeholder="请输入医生职称，如：主任医师、副主任医师等"
                disabled={isGenerating || isGeneratingSubtitle}
              />
            </Form.Item>

            <Form.Item
              name="doctor_department"
              label="所属科室"
              rules={[{ required: true, message: '请输入所属科室' }]}
            >
              <Input
                prefix={<MedicineBoxOutlined />}
                placeholder="请输入所属科室，如：内科、外科等"
                disabled={isGenerating || isGeneratingSubtitle}
              />
            </Form.Item>

            <Form.Item
              name="doctor_hospital"
              label="所属医院"
              rules={[{ required: true, message: '请输入所属医院' }]}
            >
              <Input
                prefix={<BankOutlined />}
                placeholder="请输入所属医院"
                disabled={isGenerating || isGeneratingSubtitle}
              />
            </Form.Item>

            <Form.Item
              name="theme_text"
              label="主题文字"
              rules={[{ required: true, message: '请输入主题文字' }]}
            >
              <Input
                placeholder="请输入主题文字，如：健康科普、疾病预防等"
                disabled={isGenerating || isGeneratingSubtitle}
              />
            </Form.Item>

            <Form.Item
              name="background_position"
              label="背景位置"
              rules={[{ required: true, message: '请选择背景位置' }]}
            >
              <Select disabled={isGenerating || isGeneratingSubtitle}>
                <Option value="right">右侧</Option>
                <Option value="left">左侧</Option>
                <Option value="center">居中</Option>
              </Select>
            </Form.Item>
          </Space>
        </div>

        <Divider />

        <div className={styles.formSection}>
          <h3 className={styles.sectionTitle}>图片上传</h3>

          <div className={styles.imageUploadSection}>
            <h4>封面图片</h4>
            <Upload {...coverUploadProps}>
              <Button icon={<UploadOutlined />} disabled={isGenerating || isGeneratingSubtitle}>
                上传封面图片
              </Button>
            </Upload>

            {coverImageUrl && (
              <div className={styles.imagePreview}>
                <Image
                  src={coverImageUrl}
                  alt="封面图片预览"
                  style={{ maxWidth: '100%', maxHeight: '200px' }}
                />
                <Button
                  size="small"
                  danger
                  onClick={clearCoverImage}
                  style={{ marginTop: '8px' }}
                  disabled={isGenerating || isGeneratingSubtitle}
                >
                  删除图片
                </Button>
              </div>
            )}
          </div>

          <div className={styles.imageUploadSection}>
            <h4>结束图片</h4>
            <Upload {...endUploadProps}>
              <Button icon={<UploadOutlined />} disabled={isGenerating || isGeneratingSubtitle}>
                上传结束图片
              </Button>
            </Upload>

            {endImageUrl && (
              <div className={styles.imagePreview}>
                <Image
                  src={endImageUrl}
                  alt="结束图片预览"
                  style={{ maxWidth: '100%', maxHeight: '200px' }}
                />
                <Button
                  size="small"
                  danger
                  onClick={clearEndImage}
                  style={{ marginTop: '8px' }}
                  disabled={isGenerating || isGeneratingSubtitle}
                >
                  删除图片
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className={styles.generateSection}>
          <Button
            type="primary"
            icon={<FileTextOutlined />}
            onClick={generateSubtitle}
            loading={isGeneratingSubtitle}
            disabled={isGenerating || isGeneratingSubtitle || !digitalHumanUrl || !coverImageUrl || !endImageUrl}
            block
            size="large"
          >
            {isGeneratingSubtitle ? '添加字幕中...' : '添加字幕'}
          </Button>

          {isGeneratingSubtitle && (
            <div className={styles.progressContainer}>
              <Progress percent={progress} status="active" />
              <p className={styles.progressText}>
                <LoadingOutlined /> 正在添加字幕，请耐心等待...
              </p>
            </div>
          )}

          {error && (
            <div className={styles.errorContainer}>
              <p className={styles.errorText}>{error}</p>
            </div>
          )}

          {/* 字幕处理现在是异步的，不再直接显示结果 */}
          {progress === 99 && (
            <div className={styles.resultContainer}>
              <h4>字幕添加请求已提交</h4>
              <p className={styles.successText}>字幕正在后台处理中，请稍后刷新页面查看结果</p>
            </div>
          )}
        </div>
      </Form>
    </Card>
  );
};

export default SubtitleInput;
