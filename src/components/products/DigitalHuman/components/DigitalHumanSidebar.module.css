.sidebar {
  width: 240px;
  height: 100%;
  border-left: 1px solid #e8e8e8;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
}

.collapsedSidebar {
  width: 48px;
  height: 100%;
  border-left: 1px solid #e8e8e8;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 16px;
  transition: all 0.3s;
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
}

.sidebarHeader {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.collapseButton {
  height: 30px;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.sidebarContent {
  flex: 1;
  overflow-y: auto;
  padding: 12px 0;
}

.digitalHumanItem {
  padding: 8px 16px 8px 13px !important;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.digitalHumanItem:hover {
  background-color: #eaeaea !important;
}

/* 不同状态的项目背景色 */
.itemCompleted {
  background-color: rgba(24, 144, 255, 0.1);
  border-left: 3px solid rgba(24, 144, 255, 0.6);
}

.itemGenerating {
  background-color: rgba(250, 140, 22, 0.1);
  border-left: 3px solid rgba(250, 140, 22, 0.6);
}

.itemNotGenerated {
  background-color: rgba(114, 46, 209, 0.1);
  border-left: 3px solid rgba(114, 46, 209, 0.6);
}

/* 禁用状态的样式 */
.disabled .digitalHumanItem {
  cursor: not-allowed;
  opacity: 0.6;
}

.disabled .digitalHumanItem:hover {
  background-color: transparent;
}

.digitalHumanInfo {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.digitalHumanDate {
  font-size: 12px;
  margin-top: 4px;
}

.digitalHumanArrow {
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s, transform 0.3s;
}

.digitalHumanItem:hover .digitalHumanArrow {
  opacity: 1;
  transform: translateX(3px);
}

.paginationContainer {
  padding: 12px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e8e8e8;
}

.digitalHumanActions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.deleteButton {
  opacity: 0;
  transition: opacity 0.3s;
}

.digitalHumanItem:hover .deleteButton {
  opacity: 1;
}

.statusTag {
  margin-left: 8px;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
}

.statusCompleted {
  background-color: #e6f7ff;
  color: #1890ff;
}

.statusGenerating {
  background-color: #fff7e6;
  color: #fa8c16;
}

.statusNotGenerated {
  background-color: #f9f0ff;
  color: #722ed1;
}
