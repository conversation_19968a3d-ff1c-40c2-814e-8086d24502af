.verticalStepsContainer {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  width: 200px;
  z-index: 100;
  border: 1px solid #e8e8e8;
}

.verticalSteps {
  height: 100%;
}

.verticalSteps :global(.ant-steps-item) {
  padding-bottom: 16px;
}

.verticalSteps :global(.ant-steps-item-container) {
  display: flex;
  align-items: flex-start;
}

.verticalSteps :global(.ant-steps-item-icon) {
  background-color: #f0f0f0;
  border-color: #d9d9d9;
}

.verticalSteps :global(.ant-steps-item-active .ant-steps-item-icon) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.verticalSteps :global(.ant-steps-item-finish .ant-steps-item-icon) {
  background-color: #52c41a;
  border-color: #52c41a;
}

.verticalSteps :global(.ant-steps-item-title) {
  font-weight: 500;
  font-size: 14px;
}

.verticalSteps :global(.ant-steps-item-description) {
  font-size: 12px;
  color: #888;
}

.verticalSteps :global(.ant-steps-item-active .ant-steps-item-title) {
  color: #1890ff;
  font-weight: 600;
}

.verticalSteps :global(.ant-steps-item-active .ant-steps-item-description) {
  color: #1890ff;
}

.verticalSteps :global(.ant-steps-item-finish .ant-steps-item-title) {
  color: #52c41a;
}

.activeStep {
  transform: scale(1.05);
  transition: all 0.3s ease;
}

.completedStep :global(.ant-steps-item-icon) {
  background-color: #52c41a !important;
  border-color: #52c41a !important;
}

.completedStep :global(.ant-steps-item-icon .ant-steps-icon) {
  color: #fff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .verticalStepsContainer {
    position: static;
    transform: none;
    width: 100%;
    margin-bottom: 20px;
  }
}
