import React, { useState, useRef, useEffect } from 'react';
import { Card, Input, Button, Space, message, Alert, Modal, Upload, Tooltip } from 'antd';
import { LinkOutlined, AudioOutlined, CloseCircleOutlined, CheckCircleOutlined, UploadOutlined } from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import styles from '../DigitalHuman.module.css';

// 最小录音时间（秒）
const MIN_RECORDING_TIME = 10;

interface AudioInputProps {
  audioUrl: string;
  onAudioUrlChange: (url: string) => void;
  defaultAudioUrl: string;
  isGenerating: boolean;
  onRecordedAudioChange?: (blob: Blob | null) => void; // 添加录音的回调
}

const AudioInput: React.FC<AudioInputProps> = ({
  audioUrl,
  onAudioUrlChange,
  defaultAudioUrl,
  isGenerating,
  onRecordedAudioChange
}) => {
  const [isPreviewingAudio, setIsPreviewingAudio] = useState<boolean>(true);
  const [audioPreviewError, setAudioPreviewError] = useState<boolean>(false);

  // 录音相关状态
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const [recordingTime, setRecordingTime] = useState<number>(0);
  const [showRecorder, setShowRecorder] = useState<boolean>(false);
  const [micError, setMicError] = useState<string | null>(null);
  const [recordingTooShort, setRecordingTooShort] = useState<boolean>(false);

  // 文件上传相关状态
  const [uploadedFile, setUploadedFile] = useState<UploadFile | null>(null);

  // Refs
  const audioElementRef = useRef<HTMLAudioElement>(null);
  const audioTimerRef = useRef<NodeJS.Timeout | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);
  const audioPreviewRef = useRef<HTMLAudioElement>(null);

  // 检查URL是否有效
  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  // 处理音频URL输入
  const handleAudioUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    onAudioUrlChange(url);

    // 当输入内容变化时，重置预览状态
    setIsPreviewingAudio(false);
    setAudioPreviewError(false);

    // 使用防抖处理
    if (audioTimerRef.current) {
      clearTimeout(audioTimerRef.current);
    }

    // 只有当输入不为空时才预览
    if (url && url.trim() !== '') {
      // 设置新的定时器，延迟500ms自动预览
      audioTimerRef.current = setTimeout(() => {
        if (isValidUrl(url)) {
          setIsPreviewingAudio(true);
        } else {
          message.warning('音频链接格式无效，请输入正确的URL');
        }
      }, 500);
    }
  };

  // 文件上传属性配置
  const uploadProps: UploadProps = {
    accept: 'audio/*',
    beforeUpload: (file) => {
      // 检查文件类型
      const isAudio = file.type.startsWith('audio/');
      if (!isAudio) {
        message.error('请上传音频文件！');
        return Upload.LIST_IGNORE;
      }

      // 检查文件大小，限制为20MB
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        message.error('音频文件大小不能超过20MB！');
        return Upload.LIST_IGNORE;
      }

      // 读取文件并转换为Blob
      const reader = new FileReader();
      reader.onload = () => {
        const blob = new Blob([reader.result as ArrayBuffer], { type: file.type });
        // 设置新的录制Blob
        setRecordedBlob(blob);
        if (onRecordedAudioChange) {
          onRecordedAudioChange(blob);
        }

        // 创建临时URL用于预览

        // 更新音频URL为提示文本
        onAudioUrlChange("已上传音频文件");
        setIsPreviewingAudio(true);

        message.success('音频文件上传成功！');
      };

      reader.onerror = () => {
        message.error('读取文件失败，请重试！');
      };

      reader.readAsArrayBuffer(file);

      // 保存上传的文件信息
      setUploadedFile(file);

      // 阻止默认上传行为
      return false;
    },
    showUploadList: false,
    disabled: isGenerating,
    maxCount: 1,
  };

  // 处理音频预览错误
  const handleAudioError = () => {
    setAudioPreviewError(true);
    message.error('音频无法加载，请检查链接或尝试其他音频');
  };

  // 手动重试预览音频
  const retryAudioPreview = () => {
    setAudioPreviewError(false);
    setIsPreviewingAudio(true);
    if (audioElementRef.current) {
      audioElementRef.current.load(); // 重新加载音频
    }
  };

  // 重置为默认音频
  const resetToDefault = () => {
    onAudioUrlChange(defaultAudioUrl);
    setIsPreviewingAudio(true);
    setAudioPreviewError(false);
    // 清除录制的音频Blob
    if (onRecordedAudioChange) {
      onRecordedAudioChange(null);
    }
    // 清除上传文件
    setUploadedFile(null);
  };

  // 清除上传的文件
  const clearUploadedFile = () => {
    setUploadedFile(null);
    if (onRecordedAudioChange) {
      onRecordedAudioChange(null);
    }
    onAudioUrlChange('');
    setIsPreviewingAudio(false);
  };

  // 打开录音Modal
  const openAudioRecorder = async () => {
    setShowRecorder(true);
    setMicError(null);
    setRecordedBlob(null);
    setRecordingTime(0);

    try {
      // 获取麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: true
      });

      streamRef.current = stream;

      // 显示音频可视化（可选）
      if (audioPreviewRef.current) {
        audioPreviewRef.current.muted = true; // 避免回声
      }
    } catch (err) {
      console.error('获取麦克风权限失败:', err);
      setMicError('无法访问麦克风，请确保您已授予权限并且麦克风未被其他应用程序占用。');
    }
  };

  // 开始录音
  const startRecording = () => {
    if (!streamRef.current) return;

    // 重置状态
    recordedChunksRef.current = [];
    setRecordingTime(0);
    setIsRecording(true);

    // 创建MediaRecorder实例
    const mediaRecorder = new MediaRecorder(streamRef.current, { mimeType: 'audio/webm' });

    // 保存数据
    mediaRecorder.ondataavailable = (event) => {
      if (event.data && event.data.size > 0) {
        recordedChunksRef.current.push(event.data);
      }
    };

    // 录制完成处理
    mediaRecorder.onstop = () => {
      const blob = new Blob(recordedChunksRef.current, { type: 'audio/webm' });
      setRecordedBlob(blob);
    };

    // 启动录制
    mediaRecorder.start(1000); // 每1秒生成一个数据块
    mediaRecorderRef.current = mediaRecorder;

    // 开始计时
    recordingTimerRef.current = setInterval(() => {
      setRecordingTime(prev => prev + 1);
    }, 1000);
  };

  // 停止录音
  const stopRecording = () => {
    // 检查是否满足最小录音时间
    if (recordingTime < MIN_RECORDING_TIME) {
      message.warning(`录音时间太短，音频需要至少${MIN_RECORDING_TIME}秒`);
      setRecordingTooShort(true);
      return;
    }

    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }

    // 清理计时器
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }

    setIsRecording(false);
    setRecordingTooShort(false);
  };

  // 使用录制的音频
  const useRecordedAudio = () => {
    if (recordedBlob) {
      // 检查音频长度是否符合要求
      if (recordingTime < MIN_RECORDING_TIME) {
        message.warning(`音频时长需要至少${MIN_RECORDING_TIME}秒，请重新录制`);
        return;
      }

      // 创建临时URL供预览使用
      // const url = URL.createObjectURL(recordedBlob);
      // 清空输入框中的URL（显示一个提示文本）
      onAudioUrlChange("已使用录制的音频");
      // 调用回调函数传递录制的音频Blob
      if (onRecordedAudioChange) {
        onRecordedAudioChange(recordedBlob);
      }
      setIsPreviewingAudio(true);
      message.success('已成功设置为录制的音频');
      closeRecorder();
    }
  };

  // 关闭录音器
  const closeRecorder = () => {
    // 停止录音
    if (isRecording) {
      stopRecording();
    }

    // 停止麦克风
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    setShowRecorder(false);
  };

  // 格式化时间
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (audioTimerRef.current) {
        clearTimeout(audioTimerRef.current);
      }

      // 清理录音定时器和麦克风
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }

      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  return (
    <>
      <Card title="步骤2 (可选): 输入音频链接" className={styles.card}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
            <Input
              placeholder="输入音频链接URL"
              value={audioUrl}
              onChange={handleAudioUrlChange}
              prefix={<LinkOutlined />}
              disabled={isGenerating || uploadedFile !== null}
              allowClear
              style={{ flex: 1, marginRight: '8px' }}
            />
            <Button
              onClick={resetToDefault}
              title="恢复默认音频链接"
              style={{ marginRight: '8px' }}
            >
              恢复默认
            </Button>
            <Space>
              <Upload {...uploadProps}>
                <Tooltip title="上传音频文件">
                  <Button
                    icon={<UploadOutlined />}
                    disabled={isGenerating}
                  >
                    上传音频
                  </Button>
                </Tooltip>
              </Upload>
              <Button
                type="primary"
                icon={<AudioOutlined />}
                onClick={openAudioRecorder}
                disabled={isGenerating}
                title="使用麦克风录制音频"
              >
                录制音频
              </Button>
            </Space>
          </div>

          {/* 显示上传的文件信息 */}
          {uploadedFile && audioUrl === "已上传音频文件" && (
            <div style={{ marginBottom: '10px' }}>
              <Alert
                message={`已上传音频: ${uploadedFile.name}`}
                type="success"
                action={
                  <Button size="small" danger onClick={clearUploadedFile}>
                    删除
                  </Button>
                }
              />
            </div>
          )}

          {isPreviewingAudio && audioUrl && !audioPreviewError && audioUrl !== "已使用录制的音频" && audioUrl !== "已上传音频文件" && (
            <div className={styles.previewContainer}>
              <audio
                ref={audioElementRef}
                controls
                src={audioUrl}
                className={styles.audioPreview}
                onError={handleAudioError}
              />
            </div>
          )}

          {recordedBlob && audioUrl === "已使用录制的音频" && (
            <div className={styles.previewContainer}>
              <audio
                controls
                src={URL.createObjectURL(recordedBlob)}
                className={styles.audioPreview}
              />
              <div style={{ marginTop: '8px', color: '#1890ff' }}>
                使用的是录制的音频
              </div>
            </div>
          )}

          {recordedBlob && audioUrl === "已上传音频文件" && (
            <div className={styles.previewContainer}>
              <audio
                controls
                src={URL.createObjectURL(recordedBlob)}
                className={styles.audioPreview}
              />
              <div style={{ marginTop: '8px', color: '#1890ff' }}>
                使用的是上传的音频
              </div>
            </div>
          )}

          {audioPreviewError && (
            <Alert
              message="音频预览失败"
              description="无法加载音频，请检查链接是否有效或尝试其他音频链接。"
              type="error"
              showIcon
              action={
                <Button size="small" type="primary" onClick={retryAudioPreview}>
                  重试
                </Button>
              }
            />
          )}
        </Space>
      </Card>

      {/* 麦克风录音Modal */}
      <Modal
        title="麦克风录音"
        open={showRecorder}
        onCancel={closeRecorder}
        width={600}
        footer={null}
        destroyOnHidden
      >
        <div className={styles.recorderContainer}>
          {micError ? (
            <Alert
              message="麦克风错误"
              description={micError}
              type="error"
              showIcon
            />
          ) : (
            <>
              <div className={styles.recorderControls}>
                {!recordedBlob ? (
                  <>
                    <div className={styles.recordingTime}>
                      {isRecording && (
                        <span className={styles.recordingIndicator}>
                          ● 录音中: {formatTime(recordingTime)}
                          {recordingTime < MIN_RECORDING_TIME && (
                            <span style={{ marginLeft: '8px', color: '#ff4d4f' }}>
                              (需要至少录制 {MIN_RECORDING_TIME} 秒)
                            </span>
                          )}
                        </span>
                      )}
                      {!isRecording && !recordedBlob && (
                        <span>准备就绪，点击"开始录音"按钮开始录制，音频需要至少 {MIN_RECORDING_TIME} 秒</span>
                      )}
                    </div>

                    <Space>
                      {!isRecording ? (
                        <Button
                          type="primary"
                          icon={<AudioOutlined />}
                          onClick={startRecording}
                        >
                          开始录音
                        </Button>
                      ) : (
                        <Button
                          danger
                          icon={<CloseCircleOutlined />}
                          onClick={stopRecording}
                          disabled={recordingTime < MIN_RECORDING_TIME}
                        >
                          {recordingTime < MIN_RECORDING_TIME
                            ? `还需录制 ${MIN_RECORDING_TIME - recordingTime} 秒`
                            : '停止录音'}
                        </Button>
                      )}
                      <Button onClick={closeRecorder}>取消</Button>
                    </Space>
                  </>
                ) : (
                  <>
                    {recordingTooShort && (
                      <Alert
                        message="录音时间不足"
                        description={`音频录制时间需要至少${MIN_RECORDING_TIME}秒，当前录制时间为${recordingTime}秒，请重新录制。`}
                        type="warning"
                        showIcon
                        style={{ marginBottom: '10px' }}
                      />
                    )}
                    <div className={styles.previewContainer}>
                      <audio
                        controls
                        src={URL.createObjectURL(recordedBlob)}
                        className={styles.audioPreview}
                      />
                    </div>
                    <Space>
                      <Button
                        type="primary"
                        icon={<CheckCircleOutlined />}
                        onClick={useRecordedAudio}
                        disabled={recordingTime < MIN_RECORDING_TIME}
                      >
                        使用此音频
                      </Button>
                      <Button onClick={() => {
                        setRecordedBlob(null);
                        setRecordingTime(0);
                        setRecordingTooShort(false);
                      }}>重新录音</Button>
                      <Button onClick={closeRecorder}>取消</Button>
                    </Space>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </Modal>
    </>
  );
};

export default AudioInput;