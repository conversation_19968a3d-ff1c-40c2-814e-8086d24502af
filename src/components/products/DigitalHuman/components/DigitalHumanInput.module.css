.digitalHumanCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.formSection {
  margin-bottom: 20px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #1890ff;
  display: flex;
  align-items: center;
}

.sectionTitle :global(.anticon) {
  margin-right: 8px;
}

.uploadSection {
  margin-top: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.uploadTip {
  margin-top: 8px;
  color: #888;
  font-size: 12px;
}

.videoPreview {
  margin-top: 20px;
  padding: 16px;
  background-color: #f0f8ff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.videoPreview h4 {
  align-self: flex-start;
  margin-bottom: 8px;
  color: #1890ff;
}

.videoPlayer {
  width: 100%;
  max-height: 300px;
  border-radius: 4px;
}

.videoUrlPreview {
  width: 100%;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  word-break: break-all;
  color: #333;
}

.generateSection {
  margin-top: 24px;
  padding: 16px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #d6e4ff;
}

.progressContainer {
  margin-top: 16px;
}

.progressText {
  margin-top: 8px;
  color: #1890ff;
  text-align: center;
}

.errorContainer {
  margin-top: 16px;
  padding: 12px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
}

.errorText {
  color: #ff4d4f;
  margin: 0;
}

.resultContainer {
  margin-top: 20px;
  padding: 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.resultContainer h4 {
  align-self: flex-start;
  margin-bottom: 12px;
  color: #52c41a;
}

.successText {
  margin-top: 12px;
  color: #52c41a;
  font-weight: 500;
}
