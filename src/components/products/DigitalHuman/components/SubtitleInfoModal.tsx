import React, { useState } from 'react';
import { Modal, Form, Input, Select, Upload, Button, Space, message, Image, Divider, Tabs, Collapse } from 'antd';
import { UserOutlined, MedicineBoxOutlined, BankOutlined, InboxOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import styles from './SubtitleInfoModal.module.css';

const { Option } = Select;
const { TabPane } = Tabs;

export interface DoctorInfo {
  doctor_name: string;
  doctor_title: string;
  doctor_department: string;
  doctor_hospital: string;
  background_position: string;
  theme_text: string;
  article_title?: string; // 添加文章标题字段
}

interface SubtitleInfoModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (doctorInfo: DoctorInfo, coverImageUrl: string, endImageUrl: string) => void;
  initialDoctorInfo: DoctorInfo;
  initialCoverImageUrl: string;
  initialEndImageUrl: string;
  confirmLoading?: boolean;
}

const SubtitleInfoModal: React.FC<SubtitleInfoModalProps> = ({
  visible,
  onCancel,
  onOk,
  initialDoctorInfo,
  initialCoverImageUrl,
  initialEndImageUrl,
  confirmLoading = false
}) => {
  const [form] = Form.useForm();
  const [coverImageUrl, setCoverImageUrl] = useState<string>(initialCoverImageUrl);
  const [endImageUrl, setEndImageUrl] = useState<string>(initialEndImageUrl);
  const [activeTab, setActiveTab] = useState<string>('1');
  const [coverImageCollapsed, setCoverImageCollapsed] = useState<boolean>(false);
  const [endImageCollapsed, setEndImageCollapsed] = useState<boolean>(false);

  // 封面图片上传配置
  const coverUploadProps: UploadProps = {
    accept: 'image/*',
    beforeUpload: (file) => {
      // 检查文件类型
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('请上传图片文件！');
        return Upload.LIST_IGNORE;
      }

      // 检查文件大小，限制为5MB
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('图片文件大小不能超过5MB！');
        return Upload.LIST_IGNORE;
      }

      // 创建临时URL用于预览
      const url = URL.createObjectURL(file);
      setCoverImageUrl(url);

      // 上传成功后折叠上传区域
      setCoverImageCollapsed(true);

      message.success('封面图片选择成功，点击确定后将上传');

      // 阻止默认上传行为
      return false;
    },
    showUploadList: false,
    maxCount: 1,
  };

  // 结束图片上传配置
  const endUploadProps: UploadProps = {
    accept: 'image/*',
    beforeUpload: (file) => {
      // 检查文件类型
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('请上传图片文件！');
        return Upload.LIST_IGNORE;
      }

      // 检查文件大小，限制为5MB
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('图片文件大小不能超过5MB！');
        return Upload.LIST_IGNORE;
      }

      // 创建临时URL用于预览
      const url = URL.createObjectURL(file);
      setEndImageUrl(url);

      // 上传成功后折叠上传区域
      setEndImageCollapsed(true);

      message.success('结束图片选择成功，点击确定后将上传');

      // 阻止默认上传行为
      return false;
    },
    showUploadList: false,
    maxCount: 1,
  };

  // 清除封面图片
  const clearCoverImage = () => {
    if (coverImageUrl) {
      URL.revokeObjectURL(coverImageUrl);
    }
    setCoverImageUrl('');
    // 清除图片后展开上传区域
    setCoverImageCollapsed(false);
  };

  // 清除结束图片
  const clearEndImage = () => {
    if (endImageUrl) {
      URL.revokeObjectURL(endImageUrl);
    }
    setEndImageUrl('');
    // 清除图片后展开上传区域
    setEndImageCollapsed(false);
  };

  // 处理表单提交
  const handleOk = () => {
    form.validateFields().then(values => {
      if (!coverImageUrl) {
        message.error('请上传封面图片');
        setActiveTab('2');
        return;
      }

      if (!endImageUrl) {
        message.error('请上传结束图片');
        setActiveTab('2');
        return;
      }

      // 如果图片是本地上传的（Blob URL），提示用户将上传到服务器
      if (!coverImageUrl.startsWith('http') || !endImageUrl.startsWith('http')) {
        message.info('图片将上传到服务器，请稍候...');
      }

      onOk(values as DoctorInfo, coverImageUrl, endImageUrl);
    }).catch(info => {
      console.log('验证失败:', info);
      message.error('请填写完整的医生信息');
    });
  };

  return (
    <Modal
      title="添加字幕信息"
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      width={700}
      okText="保存信息并上传图片"
      cancelText="取消"
      confirmLoading={confirmLoading}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="医生信息" key="1">
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              ...initialDoctorInfo,
              background_position: initialDoctorInfo.background_position || 'left'
            }}
          >
            <Space direction="vertical" className={styles.formContainer}>
              <Form.Item
                name="doctor_name"
                label="医生姓名"
                rules={[{ required: true, message: '请输入医生姓名' }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入医生姓名"
                />
              </Form.Item>

              <Form.Item
                name="doctor_title"
                label="医生职称"
                rules={[{ required: true, message: '请输入医生职称' }]}
              >
                <Input
                  prefix={<MedicineBoxOutlined />}
                  placeholder="请输入医生职称，如：主任医师、副主任医师等"
                />
              </Form.Item>

              <Form.Item
                name="doctor_department"
                label="所属科室"
                rules={[{ required: true, message: '请输入所属科室' }]}
              >
                <Input
                  prefix={<MedicineBoxOutlined />}
                  placeholder="请输入所属科室，如：内科、外科等"
                />
              </Form.Item>

              <Form.Item
                name="doctor_hospital"
                label="所属医院"
                rules={[{ required: true, message: '请输入所属医院' }]}
              >
                <Input
                  prefix={<BankOutlined />}
                  placeholder="请输入所属医院"
                />
              </Form.Item>

              <Form.Item
                name="article_title"
                label="文章标题"
                rules={[{ required: true, message: '请输入文章标题' }]}
              >
                <Input
                  placeholder="请输入文章标题，将用于字幕生成"
                />
              </Form.Item>

              <Form.Item
                name="theme_text"
                label="主题文字"
                rules={[{ required: true, message: '请输入主题文字' }]}
              >
                <Input
                  placeholder="请输入主题文字，如：健康科普、疾病预防等"
                />
              </Form.Item>

              <Form.Item
                name="background_position"
                label="背景位置"
                rules={[{ required: true, message: '请选择背景位置' }]}
              >
                <Select>
                  <Option value="right">右侧</Option>
                  <Option value="left">左侧</Option>
                  <Option value="center">居中</Option>
                </Select>
              </Form.Item>
            </Space>
          </Form>
        </TabPane>

        <TabPane tab="图片上传" key="2">
          <div className={styles.imageUploadSection}>
            <h4>封面图片</h4>

            {coverImageUrl ? (
              <div className={styles.imagePreview}>
                <Image
                  src={coverImageUrl}
                  alt="封面图片预览"
                  style={{ maxWidth: '100%', maxHeight: '200px' }}
                />
                <Button
                  size="small"
                  danger
                  onClick={clearCoverImage}
                  style={{ marginTop: '8px' }}
                >
                  删除图片
                </Button>
              </div>
            ) : null}

            <Collapse
              activeKey={coverImageCollapsed ? [] : ['coverUpload']}
              onChange={(keys) => setCoverImageCollapsed(keys.length === 0)}
              className={styles.uploadCollapse}
            >
              <Collapse.Panel
                header={coverImageUrl ? "更换封面图片" : "上传封面图片"}
                key="coverUpload"
              >
                <Upload.Dragger {...coverUploadProps} className={styles.uploadDragger}>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽图片到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持单个图片文件上传，建议使用高清图片
                  </p>
                </Upload.Dragger>
              </Collapse.Panel>
            </Collapse>
          </div>

          <Divider />

          <div className={styles.imageUploadSection}>
            <h4>结束图片</h4>

            {endImageUrl ? (
              <div className={styles.imagePreview}>
                <Image
                  src={endImageUrl}
                  alt="结束图片预览"
                  style={{ maxWidth: '100%', maxHeight: '200px' }}
                />
                <Button
                  size="small"
                  danger
                  onClick={clearEndImage}
                  style={{ marginTop: '8px' }}
                >
                  删除图片
                </Button>
              </div>
            ) : null}

            <Collapse
              activeKey={endImageCollapsed ? [] : ['endUpload']}
              onChange={(keys) => setEndImageCollapsed(keys.length === 0)}
              className={styles.uploadCollapse}
            >
              <Collapse.Panel
                header={endImageUrl ? "更换结束图片" : "上传结束图片"}
                key="endUpload"
              >
                <Upload.Dragger {...endUploadProps} className={styles.uploadDragger}>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽图片到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持单个图片文件上传，建议使用高清图片
                  </p>
                </Upload.Dragger>
              </Collapse.Panel>
            </Collapse>
          </div>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default SubtitleInfoModal;
