import React, { useState, useRef, useEffect } from 'react';
import { Button, Input, message,  Typography, Upload, Space } from 'antd';
import { AudioOutlined, AudioMutedOutlined, UploadOutlined } from '@ant-design/icons';
import styles from './VoiceCloneSimple.module.css';
import { MANAGER_API_BASE_URL } from '../../../../Constant/ServerConstant';


const { TextArea } = Input;
const { Text } = Typography;
// 后端API地址
const API_URL = `${MANAGER_API_BASE_URL}/api/v1/audio/tts/proxy_synthesize`;
const ASR_API_URL = `${MANAGER_API_BASE_URL}/api/v1/audio/asr/transcribe`;
const getToken = (): string | null => localStorage.getItem('access_token');
const token = getToken();
// WAV格式转换函数 - 从VoiceClone组件复用
const convertToWav = async (audioBlob: Blob): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    // 创建音频上下文
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

    // 创建文件读取器
    const fileReader = new FileReader();

    fileReader.onload = async (event) => {
      try {
        // 解码音频数据
        const arrayBuffer = event.target?.result as ArrayBuffer;
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        // 准备WAV格式数据
        const wavBuffer = createWavBuffer(audioBuffer);

        // 创建WAV格式的Blob
        const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });
        resolve(wavBlob);
      } catch (error) {
        console.error('转换音频格式失败:', error);
        reject(error);
      }
    };

    fileReader.onerror = (error) => {
      console.error('读取音频文件失败:', error);
      reject(error);
    };

    // 开始读取Blob数据
    fileReader.readAsArrayBuffer(audioBlob);
  });
};

// 创建WAV格式的Buffer - 从VoiceClone组件复用
const createWavBuffer = (audioBuffer: AudioBuffer): ArrayBuffer => {
  const numOfChannels = audioBuffer.numberOfChannels;
  const length = audioBuffer.length * numOfChannels * 2; // 2字节每样本
  const sampleRate = audioBuffer.sampleRate;

  // WAV文件头 + 数据大小
  const buffer = new ArrayBuffer(44 + length);
  const view = new DataView(buffer);

  // WAV文件头
  // "RIFF"标识
  writeString(view, 0, 'RIFF');
  // 文件大小
  view.setUint32(4, 36 + length, true);
  // "WAVE"标识
  writeString(view, 8, 'WAVE');
  // "fmt "子块
  writeString(view, 12, 'fmt ');
  // 子块大小
  view.setUint32(16, 16, true);
  // 音频格式 (1 表示 PCM)
  view.setUint16(20, 1, true);
  // 通道数
  view.setUint16(22, numOfChannels, true);
  // 采样率
  view.setUint32(24, sampleRate, true);
  // 字节率
  view.setUint32(28, sampleRate * numOfChannels * 2, true);
  // 数据块对齐
  view.setUint16(32, numOfChannels * 2, true);
  // 每个样本位数
  view.setUint16(34, 16, true);
  // "data"子块
  writeString(view, 36, 'data');
  // 数据大小
  view.setUint32(40, length, true);

  // 写入实际音频数据
  const channelData = [];
  for (let i = 0; i < numOfChannels; i++) {
    channelData.push(audioBuffer.getChannelData(i));
  }

  let offset = 44;
  for (let i = 0; i < audioBuffer.length; i++) {
    for (let channel = 0; channel < numOfChannels; channel++) {
      // 将-1.0 - 1.0的浮点值转换为-32768 - 32767的整数
      const sample = Math.max(-1, Math.min(1, channelData[channel][i]));
      const value = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(offset, value, true);
      offset += 2;
    }
  }

  return buffer;
};

// 辅助函数：将字符串写入DataView - 从VoiceClone组件复用
const writeString = (view: DataView, offset: number, string: string): void => {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
};

// 格式化录音时间 - 从VoiceClone组件复用
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

interface VoiceCloneSimpleProps {
  onVoiceCloneComplete: (audioUrl: string, text: string) => Promise<void>;
  initialAudioUrl?: string;
  initialTargetText?: string; // 添加初始文案内容属性
  onCustomAudioUploaded?: () => void;
  isDefaultAudio?: boolean;
}

const VoiceCloneSimple: React.FC<VoiceCloneSimpleProps> = ({
  onVoiceCloneComplete,
  initialAudioUrl = '',
  initialTargetText = '', // 添加初始文案内容参数
  onCustomAudioUploaded,
  isDefaultAudio = false
}) => {
  // 状态管理
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const [promptText, setPromptText] = useState<string>('生活就像一杯茶，不会苦一辈子，但总会苦一阵子。重要的不是此刻的苦涩，而是你如何品味其中的回甘。保持微笑，继续前行，美好终将如期而至');
  const [targetText, setTargetText] = useState<string>(initialTargetText);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordingDuration, setRecordingDuration] = useState<number>(0);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [generatedAudioUrl, setGeneratedAudioUrl] = useState<string>('');
  const [audioUrl, setAudioUrl] = useState<string>('');
  // const [isTranscribing, setIsTranscribing] = useState<boolean>(false);
  const [isUsingInitialAudio, setIsUsingInitialAudio] = useState<boolean>(!!initialAudioUrl);

  // 录音相关引用
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 开始录音
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          echoCancellation: true,
          noiseSuppression: true
        }
      });

      // 尝试使用常见的音频格式
      let options;

      if (MediaRecorder.isTypeSupported('audio/webm')) {
        options = { mimeType: 'audio/webm' };
      } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
        options = { mimeType: 'audio/mp4' };
      } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
        options = { mimeType: 'audio/ogg; codecs=opus' };
      } else {
        options = {};
      }

      console.log('使用录音格式:', options.mimeType || '默认格式');
      const mediaRecorder = new MediaRecorder(stream, options);

      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        // 将录音数据组合成一个 Blob，使用媒体记录器的实际类型
        const mimeType = mediaRecorder.mimeType || 'audio/webm';
        const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
        setRecordedBlob(audioBlob);

        // 创建音频URL
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);

        // 停止所有轨道以释放麦克风
        stream.getTracks().forEach(track => track.stop());
      };

      // 每 1 秒获取一次录音数据
      mediaRecorder.start(1000);
      setIsRecording(true);
      setRecordingDuration(0);

      // 设置计时器
      timerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

    } catch (error) {
      console.error('录音失败:', error);
      message.error('无法访问麦克风，请确保已授予麦克风使用权限');
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      // 清除计时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    // 检查文件类型
    const acceptedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/x-m4a', 'audio/flac'];
    const fileType = file.type;

    if (!acceptedTypes.includes(fileType) &&
        !file.name.endsWith('.mp3') &&
        !file.name.endsWith('.wav') &&
        !file.name.endsWith('.m4a') &&
        !file.name.endsWith('.flac')) {
      message.error('请上传MP3、WAV、M4A或FLAC格式的音频文件');
      return false;
    }

    // 检查文件大小（限制为20MB）
    if (file.size > 20 * 1024 * 1024) {
      message.error('文件大小不能超过20MB');
      return false;
    }

    try {
      // 释放旧的URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }

      // 创建音频URL
      const newUrl = URL.createObjectURL(file);
      setAudioUrl(newUrl);
      setRecordedBlob(file);

      // 标记为不使用初始音频
      setIsUsingInitialAudio(false);

      // 通知父组件已上传自定义音频
      if (onCustomAudioUploaded) {
        onCustomAudioUploaded();
      }

      // 自动识别音频内容
      await transcribeAudio(file);

      return false; // 不上传到默认上传列表
    } catch (error) {
      console.error('处理上传失败:', error);
      message.error('文件处理失败，请重试');
      return false;
    }
  };

  // 识别音频内容
  const transcribeAudio = async (audioFile: Blob) => {
    // setIsTranscribing(true);

    try {
      // 创建FormData
      const formData = new FormData();
      formData.append('input_audio_file', new File([audioFile], 'audio.mp3', { type: audioFile.type }));

      // 发送请求
      const response = await fetch(ASR_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`服务器响应错误: ${response.status}`);
      }

      const result = await response.json();

      if (result && result.transcription) {
        message.success('音频识别成功');
        setPromptText(result.transcription);
      } else {
        throw new Error('识别结果格式不正确');
      }
    } catch (error) {
      console.error('音频识别失败:', error);
      message.error(`音频识别失败: ${error instanceof Error ? error.message : '请重试'}`);
    } finally {
      // setIsTranscribing(false);
    }
  };

  // 生成语音
  const generateVoice = async () => {
    if (!recordedBlob && !isUsingInitialAudio) {
      message.error('请先录制或上传声音');
      return;
    }
    if (!promptText.trim()) {
      message.error('请输入录音文字');
      return;
    }
    if (!targetText.trim()) {
      message.error('请输入需要生成的文字');
      return;
    }

    setIsGenerating(true);
    try {
      console.log('开始转换音频格式为WAV...');

      // 转换音频为WAV格式
      let wavBlob;

      // 如果使用初始音频，需要先获取音频文件
      if (isUsingInitialAudio && initialAudioUrl) {
        try {
          const response = await fetch(initialAudioUrl);
          if (!response.ok) {
            throw new Error(`获取音频失败: ${response.status}`);
          }
          const blob = await response.blob();
          wavBlob = await convertToWav(blob);
        } catch (error) {
          console.error('获取或转换初始音频失败:', error);
          message.error('初始音频处理失败，请上传自己的音频');
          setIsGenerating(false);
          return;
        }
      } else if (recordedBlob) {
        try {
          wavBlob = await convertToWav(recordedBlob);
          console.log('转换成功，WAV文件大小:', wavBlob.size);
        } catch (error) {
          console.error('转换WAV格式失败:', error);
          message.error('音频格式转换失败，请重试');
          setIsGenerating(false);
          return;
        }
      } else {
        message.error('没有可用的音频');
        setIsGenerating(false);
        return;
      }

      // 创建FormData对象
      const formData = new FormData();

      // 使用转换后的WAV文件
      formData.append('prompt_audio', wavBlob, 'output.wav');
      formData.append('prompt_text', promptText);
      formData.append('text', targetText);

      // 发送请求
      const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });

      if (!response.ok) {
        let errorMessage = `服务器响应错误: ${response.status}`;
        try {
          const errorText = await response.text();
          console.error('服务器错误详情:', errorText);
          errorMessage += ` - ${errorText}`;
        } catch (e) {
          console.error('无法获取错误详情');
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      if (data && data.url) {
        setGeneratedAudioUrl(data.url);
        message.success('语音生成成功');
        // 回调通知父组件
        await onVoiceCloneComplete(data.url, targetText);
      } else {
        throw new Error('返回数据格式不正确');
      }
    } catch (error: any) {
      console.error('生成失败:', error);
      message.error(`语音生成失败: ${error.message || '请重试'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  // 初始化和清理资源
  useEffect(() => {
    // 如果有初始音频URL，设置为使用初始音频
    if (initialAudioUrl) {
      setIsUsingInitialAudio(true);

      // 如果是从视频提取的音频，自动设置提示文本
      if (!isDefaultAudio) {
        setPromptText('这是从视频中提取的音频内容，您可以根据需要修改。');
      }
    }

    return () => {
      // 清除URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }

      // 清理录音定时器和麦克风
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [initialAudioUrl, isDefaultAudio]);

  // 监听 initialTargetText 变化
  useEffect(() => {
    if (initialTargetText) {
      setTargetText(initialTargetText);
    }
  }, [initialTargetText]);

  return (
    <div className={styles.voiceCloneSimpleContainer}>
      {/* <Card className={styles.voiceCloneCard} title="声音克隆" bordered={false}> */}
      <div className={styles.voiceCloneSection}>
        <div className={styles.recordingControls}>
          <h3>{isUsingInitialAudio ? '使用提取的音频或录制/上传新音频' : '录制或上传声音'}</h3>
          <Space size="middle">
            <Button
              type="primary"
              icon={isRecording ? <AudioMutedOutlined /> : <AudioOutlined />}
              onClick={isRecording ? stopRecording : startRecording}
              danger={isRecording}
              size="large"
            >
              {isRecording ? `停止录音 (${formatDuration(recordingDuration)})` : '开始录音'}
            </Button>

            <Upload
              beforeUpload={handleFileUpload}
              showUploadList={false}
              accept=".mp3,.wav,.m4a,.flac"
            >
              <Button icon={<UploadOutlined />} size="large">上传音频</Button>
            </Upload>
          </Space>

          {recordedBlob ? (
            <div className={styles.audioPreviewContainer}>
              <Text strong>录音预览</Text>
              <audio controls src={audioUrl} className={styles.audioPlayer}></audio>
            </div>
          ) : isUsingInitialAudio && initialAudioUrl ? (
            <div className={styles.audioPreviewContainer}>
              <Text strong>{isDefaultAudio ? '使用默认音频' : '使用视频提取的音频'}</Text>
              <audio controls src={initialAudioUrl} className={styles.audioPlayer}></audio>
              <div className={styles.audioSourceInfo}>
                <Text type="secondary">
                  {isDefaultAudio
                    ? '当前使用默认音频，您也可以录制或上传自己的声音'
                    : '当前使用从视频中提取的音频，您也可以录制或上传自己的声音'}
                </Text>
              </div>
            </div>
          ) : null}
        </div>

        <div className={styles.textInputSection}>
          <h3>数字人文案</h3>
          <TextArea
            placeholder="请输入需要用您的声音生成的文案内容，这将保存为数字人的copy_writing字段"
            value={targetText}
            onChange={(e) => setTargetText(e.target.value)}
            className={styles.textInput}
            autoSize={{ minRows: 4, maxRows: 8 }}
          />
        </div>

        <div className={styles.generateButtonContainer}>
          <Button
            type="primary"
            onClick={generateVoice}
            loading={isGenerating}
            disabled={(!recordedBlob && !isUsingInitialAudio) || !promptText.trim() || !targetText.trim() || isGenerating}
          >
            {isGenerating ? '生成中...' : '生成语音'}
          </Button>
        </div>

        {generatedAudioUrl && (
          <div className={styles.generatedAudioContainer}>
            <h3>生成结果</h3>
            <audio controls src={generatedAudioUrl} className={styles.audioPlayer}></audio>
          </div>
        )}
      </div>
      {/* </Card> */}
    </div>
  );
};

export default VoiceCloneSimple;
