.container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  height: auto;
  min-height: 100vh;
  overflow-y: auto;
  position: relative;
}

.headerContainer {
  margin-bottom: 24px;
  position: relative;
}

.title {
  margin-bottom: 8px !important;
  display: flex;
  align-items: center;
}

.title :global(.anticon) {
  margin-right: 12px;
  color: #1890ff;
}

.description {
  color: #666;
  font-size: 16px;
  max-width: 800px;
}

.headerActions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
}

.headerSettings {
  display: flex;
  align-items: center;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  overflow: visible;
  padding-bottom: 50px;
}

.card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  width: 100%;
  overflow: visible;
  margin-bottom: 24px;
}

.card :global(.ant-card-head) {
  background-color: #f0f8ff;
  border-bottom: 1px solid #d6e4ff;
}

.card :global(.ant-card-head-title) {
  font-size: 18px;
  font-weight: 500;
  color: #1890ff;
}

.cardContent {
  padding: 16px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #1890ff;
  display: flex;
  align-items: center;
}

.sectionTitle :global(.anticon) {
  margin-right: 8px;
}

.videoUploadContainer {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.videoInputSection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.videoInputSection h3 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.videoSourceOptions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.videoUrlInput {
  width: 100%;
}

.videoUrlInput :global(.ant-form-item) {
  margin-bottom: 0;
}

.videoUrlInput :global(.ant-input-prefix) {
  color: #1890ff;
  margin-right: 8px;
}

.videoFileUpload {
  width: 100%;
}

.uploadDragger {
  padding: 16px;
  border-radius: 8px;
  border: 2px dashed #d9d9d9 !important;
  background-color: #fafafa;
  transition: all 0.3s;
}

.uploadDragger:hover {
  border-color: #1890ff !important;
}

.uploadIcon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 8px;
}

.uploadText {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.uploadHint {
  color: #888;
  font-size: 12px;
}

.videoPreviewSection {
  margin-top: 8px;
  padding: 20px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #d6e4ff;
}

.videoPreviewSection h3 {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
  margin-bottom: 16px;
}

.videoPreviewWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.videoPlayer {
  width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.deleteVideoBtn {
  align-self: flex-end;
}

.defaultVideoPreview {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.videoUrlInfo {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f0f8ff;
  border-radius: 20px;
  color: #1890ff;
  font-size: 14px;
  border: 1px solid #d6e4ff;
}

.videoUrlIcon {
  font-size: 16px;
  color: #1890ff;
}

.noVideoPreview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
}

.noVideoIcon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.actionButtons {
  margin-top: 24px;
  padding: 24px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #d6e4ff;
}

.buttonGroup {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.actionButton {
  flex: 1;
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
}

.progressContainer {
  margin-top: 16px;
}

.progressText {
  margin-top: 8px;
  color: #1890ff;
  text-align: center;
}

.errorContainer {
  margin-top: 16px;
  padding: 16px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.errorText {
  color: #ff4d4f;
  margin: 0;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid #d6e4ff;
}

.loadingIcon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
}

.loadingText {
  color: #1890ff;
  font-size: 16px;
  margin-top: 16px;
}

.loadingSubText {
  color: #999;
  font-size: 14px;
  margin-top: 8px;
  text-align: center;
}

.extractAudioSection {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 8px;
}

.extractAudioTip {
  margin-top: 8px;
  color: #d48806;
  text-align: center;
}

.noAudioSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
}

.noAudioIcon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.audioEditSection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audioEditSection h3 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.audioPreviewWrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #d6e4ff;
}

.audioPlayer {
  width: 100%;
  border-radius: 8px;
}

.audioEditControls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.audioEditTip {
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
  color: #666;
  margin: 0;
}

.audioStatusBadge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f6ffed;
  border-radius: 20px;
  color: #52c41a;
  font-weight: 500;
  border: 1px solid #b7eb8f;
}

.resultContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

/* 折叠面板样式 */
.uploadCollapse {
  margin-top: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.uploadCollapse :global(.ant-collapse-header) {
  background-color: #f0f8ff;
  border-radius: 8px !important;
  font-weight: 500;
  color: #1890ff !important;
}

.uploadCollapse :global(.ant-collapse-content) {
  border-top: 1px solid #d6e4ff;
}

.uploadCollapse :global(.ant-collapse-item) {
  border-radius: 8px !important;
  border: 1px solid #d6e4ff !important;
  margin-bottom: 8px;
}

/* 声音克隆结果样式 */
.voiceCloneResult {
  padding: 16px;
  background-color: #f6ffed;
  border-radius: 8px;
  border: 1px solid #b7eb8f;
  margin-bottom: 16px;
}

.voiceCloneInfo {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.voiceCloneIcon {
  font-size: 24px;
  color: #52c41a;
}
