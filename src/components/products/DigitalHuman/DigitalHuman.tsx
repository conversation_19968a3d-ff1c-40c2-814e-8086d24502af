import React, { useState, useEffect, useRef } from 'react';
import { Typography, message } from 'antd';
import { RobotOutlined, ArrowDownOutlined, SoundOutlined } from '@ant-design/icons';
import styles from './DigitalHuman.module.css';
import { VoiceCloneSimple, VerticalSteps, DigitalHumanInput, SubtitleInput, ResultPreview } from './components';
import HeaderSettings from '../../common/HeaderSettings';
import { DoctorInfo } from './components/SubtitleInput';

const { Title, Paragraph } = Typography;

// 预设链接
const DEFAULT_VIDEO_URL = "https://minio.aimed.cn/johnson/test/263_1729741652.mp4";


interface DigitalHumanProps {}

const DigitalHuman: React.FC<DigitalHumanProps> = () => {
  // 步骤状态
  const [currentStep, setCurrentStep] = useState<number>(0);

  // 视频和音频状态
  const [videoUrl, setVideoUrl] = useState<string>(DEFAULT_VIDEO_URL);
  const [isGenerating] = useState<boolean>(false);
  const [showScrollIndicator, setShowScrollIndicator] = useState<boolean>(true);

  // 录制的媒体Blob
  const [, setRecordedVideoBlob] = useState<Blob | null>(null);

  // 语音克隆状态
  const [hasVoiceClone, setHasVoiceClone] = useState<boolean>(false);
  const [voiceCloneText, setVoiceCloneText] = useState<string>('');

  // 医生信息状态
  const [, setDoctorInfo] = useState<DoctorInfo>({
    doctor_name: '',
    doctor_title: '',
    doctor_department: '',
    doctor_hospital: '',
    background_position: 'right',
    theme_text: '健康科普'
  });

  // 图片状态
  const [, setCoverImageUrl] = useState<string>('');
  const [, setEndImageUrl] = useState<string>('');

  // 标题状态
  const [title, setTitle] = useState<string>('');

  // 生成结果状态
  const [digitalHumanUrl, setDigitalHumanUrl] = useState<string>('');
  const [subtitleUrl, setSubtitleUrl] = useState<string>('');

  // 引用
  const containerRef = useRef<HTMLDivElement>(null);

  // 检查URL是否有效
  const checkUrlValidity = async (url: string): Promise<boolean> => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      console.error('检查URL有效性失败:', error);
      return false;
    }
  };

  // 在组件加载时验证默认链接
  useEffect(() => {
    const validateDefaultUrls = async () => {
      // 验证默认视频URL
      const isVideoValid = await checkUrlValidity(DEFAULT_VIDEO_URL);
      if (!isVideoValid) {
        console.warn('默认视频URL无效，可能导致生成失败');
        message.warning('默认视频URL可能无效，请上传自己的视频或使用有效的视频链接');
      }


    };

    validateDefaultUrls();
  }, []);

  // 不再需要监听生成进度，由各组件自行处理

  // 监听滚动，隐藏滚动指示器
  useEffect(() => {
    const handleScroll = () => {
      if (containerRef.current) {
        const { scrollTop, scrollHeight } = containerRef.current;
        // 当用户滚动到内容的20%以下时，隐藏滚动指示器
        if (scrollTop > scrollHeight * 0.2) {
          setShowScrollIndicator(false);
        } else {
          setShowScrollIndicator(true);
        }
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  // 手动滚动到指定位置
  const scrollToPosition = (position: number) => {
    if (containerRef.current) {
      containerRef.current.scrollTo({
        top: position,
        behavior: 'smooth'
      });
    }
  };



  // 处理数字人生成完成
  const handleDigitalHumanGenerated = (url: string) => {
    setDigitalHumanUrl(url);
    // 自动进入下一步
    setCurrentStep(2);
    message.success('数字人生成成功，请继续添加字幕');
  };

  // 处理字幕添加完成
  const handleSubtitleGenerated = (url: string) => {
    setSubtitleUrl(url);
    message.success('字幕添加成功！');
    // 滚动到结果部分
    setTimeout(() => {
      scrollToPosition(document.body.scrollHeight);
    }, 500);
  };

  return (
    <div className={styles.container} ref={containerRef}>
      <div className={styles.headerContainer}>
        <Title level={2} className={styles.title}>
          <RobotOutlined /> 数字人视频生成
        </Title>
        <Paragraph className={styles.description}>
          通过语音克隆和视频上传，生成带有字幕的数字人视频。
        </Paragraph>
        <div className={styles.headerSettings}>
          <HeaderSettings />
        </div>
      </div>

      {/* 垂直步骤导航 */}
      <VerticalSteps current={currentStep} onChange={setCurrentStep} />

      <div className={styles.content}>
        {/* 步骤1: 语音克隆 */}
        {currentStep === 0 && (
          <div className={styles.voiceCloneContainer}>
            <div className={styles.sectionTitle}>
              <SoundOutlined /> 步骤1: 声音克隆
            </div>
            <VoiceCloneSimple onVoiceCloneComplete={async (_, text) => {
              // 保存语音克隆结果
              setHasVoiceClone(true);
              setVoiceCloneText(text);
              // 自动进入下一步
              setCurrentStep(1);
              message.success('声音克隆完成，请继续下一步');
            }} />
          </div>
        )}

        {/* 步骤2: 生成数字人 */}
        {currentStep === 1 && (
          <DigitalHumanInput
            isGenerating={isGenerating}
            videoUrl={videoUrl}
            onVideoUrlChange={setVideoUrl}
            onTitleChange={setTitle}
            onRecordedVideoChange={setRecordedVideoBlob}
            onDigitalHumanGenerated={handleDigitalHumanGenerated}
            hasVoiceClone={hasVoiceClone}
          />
        )}

        {/* 步骤3: 添加字幕 */}
        {currentStep === 2 && (
          <SubtitleInput
            isGenerating={isGenerating}
            onDoctorInfoChange={setDoctorInfo}
            onCoverImageChange={setCoverImageUrl}
            onEndImageChange={setEndImageUrl}
            onSubtitleGenerated={handleSubtitleGenerated}
            digitalHumanUrl={digitalHumanUrl}
            title={title}
            voiceCloneText={voiceCloneText}
          />
        )}

        {/* 结果预览 */}
        {subtitleUrl && <ResultPreview outputUrl={subtitleUrl} />}
      </div>

      {/* 滚动指示器，只在初始状态显示 */}
      {showScrollIndicator && (
        <div
          className={styles.scrollIndicator}
          onClick={() => scrollToPosition(document.body.scrollHeight)}
        >
          <ArrowDownOutlined style={{ color: '#fff', fontSize: '20px' }} />
        </div>
      )}
    </div>
  );
};

export default DigitalHuman;