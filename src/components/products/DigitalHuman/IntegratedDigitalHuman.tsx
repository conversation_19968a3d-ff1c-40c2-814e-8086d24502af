import React, { useState, useRef, useEffect } from 'react';
import { Typography, message, Card, Button, Form, Input, Upload, Progress, Divider, Collapse, Spin } from 'antd';
import {
  RobotOutlined,
  SoundOutlined,
  VideoCameraOutlined,
  UploadOutlined,
  FileTextOutlined,
  LoadingOutlined,
  DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import styles from './IntegratedDigitalHuman.module.css';
import { MANAGER_API_BASE_URL } from '../../../Constant/ServerConstant';
import { VoiceCloneSimple, SubtitleInfoModal } from './components';
import DigitalHumanSidebar, { DIGITAL_HUMAN_STATUS } from './components/DigitalHumanSidebar';
import HeaderSettings from '../../common/HeaderSettings';
import { DoctorInfo } from './components/SubtitleInfoModal';
import type { UploadProps } from 'antd';
import { createData, updateData, fetchData, uploadImageToServer } from '../../../components/api/api';

const { Title, Paragraph } = Typography;

// 预设链接
const DEFAULT_VIDEO_URL = "https://minio.aimed.cn/johnson/test/263_1729741652.mp4";
const DEFAULT_AUDIO_URL = "https://minio.aimed.cn/johnson/test/audio.wav";
const DEFAULT_COVER_IMAGE_URL = "https://minio.aimed.cn/johnson/llm-agent/2025-5-15/cover_image.png";
const DEFAULT_END_IMAGE_URL = "https://minio.aimed.cn/johnson/llm-agent/2025-5-15/end_image.png";

const IntegratedDigitalHuman: React.FC = () => {
  // 获取路由参数和导航函数
  const { id = 'new' } = useParams<{ id: string }>();
  const navigate = useNavigate();
  // 视频状态
  const [videoUrl, setVideoUrl] = useState<string>(DEFAULT_VIDEO_URL);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState<string>('');
  const [isVideoUploaded, setIsVideoUploaded] = useState<boolean>(false);
  const [isVideoUrlValid, setIsVideoUrlValid] = useState<boolean>(true);
  const [videoUploadCollapsed, setVideoUploadCollapsed] = useState<boolean>(false);
  const [recordedVideoBlob, setRecordedVideoBlob] = useState<Blob | null>(null);

  // 音频状态
  const [audioUrl, setAudioUrl] = useState<string>(DEFAULT_AUDIO_URL);
  const [isExtractingAudio, setIsExtractingAudio] = useState<boolean>(false);
  const [audioExtractError, setAudioExtractError] = useState<string | null>(null);

  // 语音克隆状态
  const [hasVoiceClone, setHasVoiceClone] = useState<boolean>(false);
  const [voiceCloneText, setVoiceCloneText] = useState<string>('过去十年，软件的核心价值是"提升效率"：提高运营效率、自动化部分流程、辅助人类决策。企业为此购买 SaaS、堆积工具，预算划在"软件费用"一栏里。但现在，AI正在穿透这层逻辑。');
  const [clonedAudioUrl, setClonedAudioUrl] = useState<string>(''); // 存储声音克隆生成的音频URL
  const [voiceCloneCollapsed, setVoiceCloneCollapsed] = useState<boolean>(false);

  // 医生信息状态
  const [doctorInfo, setDoctorInfo] = useState<DoctorInfo>({
    doctor_name: '',
    doctor_title: '',
    doctor_department: '',
    doctor_hospital: '',
    background_position: 'left', // 默认背景位置为左侧
    theme_text: '健康科普',
    article_title: ''
  });

  // 图片状态
  const [coverImageUrl, setCoverImageUrl] = useState<string>(DEFAULT_COVER_IMAGE_URL);
  const [endImageUrl, setEndImageUrl] = useState<string>(DEFAULT_END_IMAGE_URL);

  // 标题状态
  const [title, setTitle] = useState<string>('数字人视频');

  // 生成结果状态
  const [digitalHumanUrl, setDigitalHumanUrl] = useState<string>('');
  const [subtitleVideoUrl, setSubtitleVideoUrl] = useState<string>('');
  const [digitalHumanId, setDigitalHumanId] = useState<string>('');
  const [digitalHumanName, setDigitalHumanName] = useState<string>(''); // 保存数字人名称

  // 加载状态
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 生成状态
  const [isGeneratingDigitalHuman, setIsGeneratingDigitalHuman] = useState<boolean>(false);
  const [isGeneratingSubtitle, setIsGeneratingSubtitle] = useState<boolean>(false);
  const [digitalHumanProgress, setDigitalHumanProgress] = useState<number>(0);
  const [subtitleProgress, setSubtitleProgress] = useState<number>(0);
  const [digitalHumanError, setDigitalHumanError] = useState<string | null>(null);
  const [subtitleError, setSubtitleError] = useState<string | null>(null);

  // 弹窗状态
  const [subtitleModalVisible, setSubtitleModalVisible] = useState<boolean>(false);

  // 侧边栏状态
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(true);

  // 引用
  const containerRef = useRef<HTMLDivElement>(null);

  // 初始化 - 确保默认视频使用默认音频和默认标题
  useEffect(() => {
    if (videoUrl === DEFAULT_VIDEO_URL) {
      setAudioUrl(DEFAULT_AUDIO_URL);
      setTitle('数字人视频'); // 确保默认视频有默认标题
    }
  }, [videoUrl]);

  // 重置状态为新建状态
  const resetToNewState = () => {
    // 重置ID和名称
    setDigitalHumanId('');
    setDigitalHumanName('');

    // 重置视频和音频状态
    setVideoUrl(DEFAULT_VIDEO_URL);
    setVideoPreviewUrl('');
    setIsVideoUploaded(false);
    setAudioUrl(DEFAULT_AUDIO_URL);

    // 重置语音克隆状态
    setHasVoiceClone(false);
    setClonedAudioUrl('');

    // 重置生成结果
    setDigitalHumanUrl('');
    setSubtitleVideoUrl('');

    // 重置标题
    setTitle('数字人视频');

    // 重置错误状态
    setDigitalHumanError(null);
    setSubtitleError(null);

    console.log('已重置为新建状态');
  };

  // 根据ID加载数据
  useEffect(() => {
    const loadDigitalHumanData = async () => {
      // 如果是新建，重置状态
      if (id === 'new') {
        console.log('创建新的数字人记录');
        resetToNewState();
        return;
      }

      try {
        setIsLoading(true);
        console.log(`加载数字人记录，ID: ${id}`);

        // 调用API获取数据
        const endpoint = `${MANAGER_API_BASE_URL}/api/v1/product/digital-human/${id}`;
        const result = await fetchData(endpoint);

        if (!result) {
          message.error('未找到数字人记录');
          navigate('/digital-human/new');
          return;
        }

        console.log('获取到数字人记录');

        // 使用获取到的数据更新状态
        setDigitalHumanId(result.id);
        setTitle(result.name || '数字人视频');

        // 保存数字人名称，用于后续生成数字人时作为code参数
        if (result.name) {
          setDigitalHumanName(result.name);
          console.log('加载数字人名称:', result.name);
        }

        // 设置视频URL
        if (result.reference_video_url) {
          setVideoUrl(result.reference_video_url);
        } else if (result.video_url) {
          setVideoUrl(result.video_url);
        }

        // 设置音频URL
        if (result.reference_audio_url) {
          setAudioUrl(result.reference_audio_url);
        } else if (result.audio_url) {
          setAudioUrl(result.audio_url);
        }

        // 设置文案
        if (result.copy_writing) {
          setVoiceCloneText(result.copy_writing);
          setHasVoiceClone(true);
          setVoiceCloneCollapsed(true);
        }

        // 设置生成的数字人视频URL
        if (result.video_url) {
          setDigitalHumanUrl(result.video_url);
          setVideoUploadCollapsed(true);
        }

        // 设置生成的字幕视频URL
        if (result.subtitle_video_url) {
          setSubtitleVideoUrl(result.subtitle_video_url);
        }

        // 设置克隆的音频URL
        if (result.audio_url) {
          setClonedAudioUrl(result.audio_url);
        }

        message.success(`已加载数字人记录: ${result.name}`);
      } catch (error) {
        console.error('加载数字人记录失败:', error);
        message.error('加载数字人记录失败，将创建新记录');
        navigate('/digital-human/new');
      } finally {
        setIsLoading(false);
      }
    };

    loadDigitalHumanData();
  }, [id, navigate]);

  // 处理视频URL输入
  const handleVideoUrlChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setVideoUrl(url);

    // 简单验证URL格式
    const isValid = !url || /^https?:\/\/.+/.test(url);
    setIsVideoUrlValid(isValid);

    // 如果输入了新的有效URL，自动设置标题并提取音频
    if (url && isValid && url !== DEFAULT_VIDEO_URL) {
      setTitle("数字人视频");

      // 如果是有效的视频URL，自动提取音频
      if (url && url !== DEFAULT_VIDEO_URL) {
        // 延迟一下，等待用户完成输入
        const timeoutId = setTimeout(async () => {
          await extractAudioFromVideo();
        }, 1000);

        return () => clearTimeout(timeoutId);
      }
    } else if (url === DEFAULT_VIDEO_URL) {
      // 如果使用默认视频，使用默认音频
      setAudioUrl(DEFAULT_AUDIO_URL);
    }
  };

  // 处理标题输入
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  // 从视频提取音频
  const extractAudioFromVideo = async (file?: File) => {
    setIsExtractingAudio(true);
    setAudioExtractError(null);

    try {
      // 创建FormData对象
      const formData = new FormData();

      // 如果提供了文件，使用文件；否则使用视频URL
      if (file) {
        formData.append('video', file);
      } else if (videoUrl) {
        // 如果没有提供文件但有视频URL，使用视频URL
        formData.append('video_url', videoUrl);
      } else {
        throw new Error('没有可用的视频文件或链接');
      }
            // 获取认证 token
      const token = getToken();
      // 调用提取音频API
      const response = await fetch(`${MANAGER_API_BASE_URL}/api/v1/video/extract-voice`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });

      if (!response.ok) {
        let errorMsg = '';
        try {
          const errorData = await response.json();
          errorMsg = errorData.detail || `服务器返回错误 (${response.status}: ${response.statusText})`;
        } catch (jsonError) {
          errorMsg = `服务器返回错误 (${response.status}: ${response.statusText})`;
        }

        throw new Error(errorMsg);
      }

      const result = await response.json();

      // 更新音频URL
      setAudioUrl(result.url);
      message.success('已从视频中提取音频，可以直接进行声音克隆');

      // 重置语音克隆状态，但保留文案内容
      setHasVoiceClone(false);

      // 音频已准备好

      return result.url;
    } catch (err: any) {
      const errorMessage = err.message || '提取音频过程中发生错误';
      console.error('提取音频错误:', errorMessage);
      setAudioExtractError(errorMessage);
      message.error('从视频提取音频失败，请稍后重试');
      return null;
    } finally {
      setIsExtractingAudio(false);
    }
  };

  // 处理视频文件上传
  const handleVideoUpload: UploadProps['beforeUpload'] = async (file) => {
    // 检查文件类型
    const isVideo = file.type.startsWith('video/');
    if (!isVideo) {
      message.error('请上传视频文件！');
      return Upload.LIST_IGNORE;
    }

    // 检查文件大小（限制为100MB）
    const isLt100M = file.size / 1024 / 1024 < 100;
    if (!isLt100M) {
      message.error('视频文件大小不能超过100MB！');
      return Upload.LIST_IGNORE;
    }

    // 创建临时URL用于预览
    const url = URL.createObjectURL(file);
    setVideoPreviewUrl(url);
    setIsVideoUploaded(true);
    setVideoUrl(''); // 清空视频URL，使用上传的文件
    setRecordedVideoBlob(file);

    // 自动设置标题（使用文件名或默认名称）
    const fileName = file.name.split('.')[0];
    if (fileName) {
      setTitle(fileName);
    } else {
      setTitle("数字人视频");
    }

    // 上传成功后折叠视频上传部分
    setVideoUploadCollapsed(true);

    message.success('视频上传成功！');

    // 自动提取音频
    message.success('视频上传成功，正在提取音频...');
    await extractAudioFromVideo(file);

    // 阻止默认上传行为
    return false;
  };

  // 清除上传的视频
  const clearVideo = () => {
    if (videoPreviewUrl) {
      URL.revokeObjectURL(videoPreviewUrl);
    }
    setVideoPreviewUrl('');
    setIsVideoUploaded(false);
    setVideoUrl(DEFAULT_VIDEO_URL); // 恢复默认视频链接
    setRecordedVideoBlob(null);
    setTitle("数字人视频"); // 重置标题

    // 重置音频状态为默认音频
    setAudioUrl(DEFAULT_AUDIO_URL);
    setAudioExtractError(null);

    // 重置语音克隆状态，但保留文案内容
    setHasVoiceClone(false);
    setClonedAudioUrl(''); // 清除克隆生成的音频URL

    message.info('已恢复使用默认视频和音频');
  };

  // 处理语音克隆完成
  const handleVoiceCloneComplete = async (audioUrl: string, text: string) => {
    setHasVoiceClone(true);
    setVoiceCloneText(text);
    // 保存克隆生成的音频URL到专门的状态变量中
    setClonedAudioUrl(audioUrl);
    // 语音克隆完成后折叠语音克隆部分
    setVoiceCloneCollapsed(true);
    // 确保有标题，如果没有则设置默认标题
    if (!title) {
      setTitle('数字人视频');
    }
    console.log('声音克隆生成的音频URL:', audioUrl);

    try {
      // 检查是否有现有的数字人ID（不是"new"）
      if (id !== 'new' && digitalHumanId) {
        console.log('更新现有数字人记录，ID:', digitalHumanId);

        // 构建更新数据
        const updateDataObj = {
          copy_writing: text,
          audio_url: audioUrl.startsWith('http') ? audioUrl : null,
          reference_audio_url: audioUrl.startsWith('http') ? audioUrl : null,
          reference_video_url: videoUrl !== '' ? videoUrl : null,
          status: DIGITAL_HUMAN_STATUS.GENERATING
        };

        // 调用更新API
        const updateEndpoint = `${MANAGER_API_BASE_URL}/api/v1/product/digital-human/${digitalHumanId}`;
        console.log('更新数字人记录:', updateEndpoint, updateDataObj);

        await updateData(updateEndpoint, updateDataObj);
        console.log('数字人记录更新成功');

        message.success('声音克隆完成并更新记录！');
      } else {
        // 创建新的数字人记录
        const timestamp = Math.floor(Date.now() / 1000);
        const newDigitalHumanName = `数字人_${timestamp}`;

        const digitalHumanData = {
          name: newDigitalHumanName,
          reference_audio_url: audioUrl.startsWith('http') ? audioUrl : null,
          copy_writing: text,
          audio_url: audioUrl.startsWith('http') ? audioUrl : null,
          reference_video_url: videoUrl !== '' ? videoUrl : null,
          status: DIGITAL_HUMAN_STATUS.GENERATING
        };

        console.log('创建新的数字人记录:', digitalHumanData);

        // 调用创建数字人API
        const endpoint = `${MANAGER_API_BASE_URL}/api/v1/product/digital-human/`;
        const response = await createData(endpoint, digitalHumanData);

        // 保存数字人记录ID和名称
        if (response && response.id) {
          setDigitalHumanId(response.id);
          console.log('新的数字人记录ID:', response.id);

          // 保存数字人名称，用于后续生成数字人时作为code参数
          if (response.name) {
            setDigitalHumanName(response.name);
            console.log('数字人名称:', response.name);
          }

          // 更新URL，但不重新加载页面
          navigate(`/digital-human/${response.id}`, { replace: true });
        }

        message.success('声音克隆完成并创建新记录！');
      }
    } catch (error) {
      console.error('处理数字人记录失败:', error);
      message.success('声音克隆完成，但记录处理失败！');
    }
  };

  // 获取认证 token
  const getToken = (): string | null => {
    const token = localStorage.getItem('access_token');
    return token;
  };

  // 处理侧边栏折叠/展开
  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // 处理选择数字人记录
  const handleSelectDigitalHuman = (item: any) => {
    console.log('选择数字人记录:', item);

    // 重置错误状态
    setDigitalHumanError(null);
    setSubtitleError(null);

    // 设置参考视频URL
    if (item.reference_video_url) {
      setVideoUrl(item.reference_video_url);
      setIsVideoUploaded(false);
      setVideoPreviewUrl('');
    } else if (item.video_url) {
      // 如果没有参考视频但有生成的视频，也设置为视频URL
      setVideoUrl(item.video_url);
      setIsVideoUploaded(false);
      setVideoPreviewUrl('');
    }

    // 设置参考音频URL
    if (item.reference_audio_url) {
      setAudioUrl(item.reference_audio_url);
    } else if (item.audio_url) {
      setAudioUrl(item.audio_url);
    }

    // 如果有文案，设置文案并标记为已完成语音克隆
    if (item.copy_writing) {
      setVoiceCloneText(item.copy_writing);
      setHasVoiceClone(true);
      // 折叠语音克隆部分
      setVoiceCloneCollapsed(true);
    } else {
      setHasVoiceClone(false);
      setVoiceCloneCollapsed(false);
    }

    // 设置标题
    setTitle(item.name || '数字人视频');

    // 设置数字人ID
    setDigitalHumanId(item.id);

    // 保存数字人名称，用于后续生成数字人时作为code参数
    if (item.name) {
      setDigitalHumanName(item.name);
      console.log('保存数字人名称:', item.name);
    }

    // 设置生成的数字人视频URL
    if (item.video_url) {
      setDigitalHumanUrl(item.video_url);
      // 如果有视频URL，折叠视频上传部分
      setVideoUploadCollapsed(true);
    } else {
      setDigitalHumanUrl('');
    }

    // 设置生成的字幕视频URL（如果有）
    if (item.subtitle_video_url) {
      setSubtitleVideoUrl(item.subtitle_video_url);
    } else {
      setSubtitleVideoUrl('');
    }

    // 设置克隆的音频URL
    if (item.audio_url) {
      setClonedAudioUrl(item.audio_url);
    }

    // 根据状态显示不同的提示
    if (item.status === DIGITAL_HUMAN_STATUS.COMPLETED) {
      message.success(`已选择数字人记录: ${item.name}（已完成）`);
    } else if (item.status === DIGITAL_HUMAN_STATUS.GENERATING) {
      message.warning(`已选择数字人记录: ${item.name}（生成中）`);
    } else {
      message.info(`已选择数字人记录: ${item.name}（未生成）`);
    }

    // 滚动到页面顶部
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    // 更新URL，但不重新加载页面
    navigate(`/digital-human/${item.id}`, { replace: true });
  };

  // 处理字幕信息提交并生成字幕
  const handleSubtitleInfoSubmit = async (info: DoctorInfo, coverUrl: string, endUrl: string) => {
    try {
      // 显示上传中提示
      message.loading('正在上传图片...', 0);

      // 上传封面图片和结束图片到服务器获取URL
      const serverCoverUrl = await uploadImageToServer(coverUrl, 'cover_image.png');
      const serverEndUrl = await uploadImageToServer(endUrl, 'end_image.png');

      // 关闭上传中提示
      message.destroy();

      // 保存信息
      setDoctorInfo(info);
      setCoverImageUrl(serverCoverUrl);
      setEndImageUrl(serverEndUrl);
      setSubtitleModalVisible(false);

      // 开始生成字幕
      await generateSubtitle(info, serverCoverUrl, serverEndUrl);
    } catch (error) {
      // 关闭上传中提示
      message.destroy();
      message.error('图片上传失败，请重试');
      console.error('处理字幕信息提交错误:', error);
    }
  };

  // 状态变量用于轮询任务状态
  const [isPolling, setIsPolling] = useState<boolean>(false);
  const [statusCheckCount, setStatusCheckCount] = useState<number>(0);
  const statusCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 检查任务状态
  const checkTaskStatus = async (code: string) => {
    if (!code) return;

    try {
      // 创建FormData用于请求
      const formData = new FormData();
      formData.append('code', code);

      // 如果有数字人ID，也添加到请求中
      if (digitalHumanId && digitalHumanId !== 'new') {
        formData.append('digital_human_id', digitalHumanId);
      }

      // 获取认证 token
      const token = getToken();
      // 发送请求
      const STATUS_API = `${MANAGER_API_BASE_URL}/api/v1/video/digital_human/status`;
      const response = await fetch(STATUS_API, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`服务器返回错误 (${response.status}: ${response.statusText})`);
      }

      const result = await response.json();
      console.log('任务状态检查结果:', result);

      // 更新进度
      if (result.progress) {
        setDigitalHumanProgress(result.progress);
      }

      // 如果任务完成并有URL
      if (result.url) {
        // 停止轮询
        if (statusCheckIntervalRef.current) {
          clearInterval(statusCheckIntervalRef.current);
          statusCheckIntervalRef.current = null;
        }
        setIsPolling(false);
        setIsGeneratingDigitalHuman(false);

        // 更新状态
        setDigitalHumanUrl(result.url);
        setDigitalHumanProgress(100);
        message.success('数字人视频生成成功！');

        // 滚动到结果部分
        setTimeout(() => {
          window.scrollTo({
            top: document.body.scrollHeight,
            behavior: 'smooth'
          });
        }, 500);
      } else {
        // 增加检查次数
        setStatusCheckCount(prev => prev + 1);

        // 如果检查次数过多（例如超过60次，即10分钟），停止轮询
        if (statusCheckCount > 60) {
          if (statusCheckIntervalRef.current) {
            clearInterval(statusCheckIntervalRef.current);
            statusCheckIntervalRef.current = null;
          }
          setIsPolling(false);
          setIsGeneratingDigitalHuman(false);
          throw new Error('任务处理时间过长，请稍后在侧边栏查看结果');
        }
      }
    } catch (err: any) {
      console.error('检查任务状态错误:', err.message);
      // 不要在每次检查失败时都显示错误消息，避免过多提示
      if (!isPolling) {
        message.error(err.message || '检查任务状态失败');
      }
    }
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (statusCheckIntervalRef.current) {
        clearInterval(statusCheckIntervalRef.current);
      }
    };
  }, []);

  // 生成数字人视频
  const generateDigitalHuman = async () => {
    if (!videoUrl && !isVideoUploaded) {
      message.error('请先上传视频或提供视频链接');
      return;
    }

    if (!hasVoiceClone) {
      message.error('请先完成声音克隆');
      return;
    }

    if (!title) {
      message.error('请输入视频标题');
      return;
    }

    // 重置状态
    setIsGeneratingDigitalHuman(true);
    setDigitalHumanProgress(0);
    setDigitalHumanError(null);
    setStatusCheckCount(0);

    // 停止之前的轮询（如果有）
    if (statusCheckIntervalRef.current) {
      clearInterval(statusCheckIntervalRef.current);
      statusCheckIntervalRef.current = null;
    }

    try {
      // 创建FormData用于上传文件
      const formData = new FormData();

      // 处理视频参数
      if (isVideoUploaded && videoPreviewUrl) {
        // 从视频预览URL获取Blob
        const response = await fetch(videoPreviewUrl);
        const blob = await response.blob();
        formData.append('video_file', blob, 'video.mp4');
      } else if (videoUrl) {
        // 使用视频URL
        formData.append('video_path', videoUrl);
      } else {
        throw new Error('必须提供视频文件或URL');
      }

      // 添加音频参数 - 使用声音克隆生成的音频
      if (hasVoiceClone && clonedAudioUrl) {
        console.log('使用声音克隆生成的音频');

        // 检查是否是URL形式
        if (clonedAudioUrl.startsWith('http')) {
          formData.append('audio_path', clonedAudioUrl);
        } else {
          // 如果是Blob URL，需要获取Blob并上传
          try {
            const audioResponse = await fetch(clonedAudioUrl);
            const audioBlob = await audioResponse.blob();
            formData.append('audio_file', audioBlob, 'audio.wav');
          } catch (error) {
            console.error('获取音频Blob失败:', error);
            formData.append('audio_path', clonedAudioUrl);
          }
        }
      } else {
        throw new Error('必须先完成声音克隆');
      }

      // 添加code参数，优先使用保存的数字人名称，如果没有则使用当前的title
      const codeValue = digitalHumanName || title;
      formData.append('code', codeValue);
      formData.append('digital_human_id', digitalHumanId === 'new' ? '' : digitalHumanId);
      console.log('使用的code参数:', codeValue);

      // 获取认证 token
      const token = getToken();


      // 发送请求
      const DIGITAL_HUMAN_API = `${MANAGER_API_BASE_URL}/api/v1/video/digital_human/generate`;
      console.log('发送数字人生成请求到:');
      console.log('请求参数:', {
        视频: formData.get('video_file') ? '已上传视频文件' : formData.get('video_path'),
        音频: formData.get('audio_file') ? '已上传声音克隆生成的音频文件' : formData.get('audio_path'),
        code: formData.get('code'),
        digital_human_id: formData.get('digital_human_id')
      });

      const response = await fetch(DIGITAL_HUMAN_API, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });

      // 处理API响应
      if (!response.ok) {
        let errorMsg = '';
        try {
          const errorData = await response.json();
          errorMsg = errorData.detail || `服务器返回错误 (${response.status}: ${response.statusText})`;
        } catch (jsonError) {
          // 如果返回的不是JSON格式，直接使用状态文本
          errorMsg = `服务器返回错误 (${response.status}: ${response.statusText})`;
        }

        console.error('API错误详情:', errorMsg);
        throw new Error(errorMsg);
      }

      const result = await response.json();
      console.log('数字人生成任务提交结果:', result);

      // 检查响应状态
      if (result.status === DIGITAL_HUMAN_STATUS.GENERATING) {
        message.info('数字人生成任务已提交，正在后台处理中...');

        // 开始轮询任务状态
        setIsPolling(true);
        statusCheckIntervalRef.current = setInterval(() => {
          checkTaskStatus(codeValue);
        }, 10000); // 每10秒检查一次

        // 立即进行第一次检查
        setTimeout(() => {
          checkTaskStatus(codeValue);
        }, 3000);
      } else {
        throw new Error('任务提交失败: ' + (result.detail || '未知错误'));
      }
    } catch (err: any) {
      const errorMessage = err.message || '生成过程中发生错误';
      console.error('数字人生成错误:', errorMessage);
      setDigitalHumanError(errorMessage);
      message.error('生成数字人视频失败，请稍后重试');

      // 停止轮询
      if (statusCheckIntervalRef.current) {
        clearInterval(statusCheckIntervalRef.current);
        statusCheckIntervalRef.current = null;
      }
      setIsPolling(false);
      setIsGeneratingDigitalHuman(false);
    }
  };

  // 生成字幕
  const generateSubtitle = async (
    info?: DoctorInfo,
    coverUrl?: string,
    endUrl?: string
  ) => {
    if (!digitalHumanUrl) {
      message.error('请先生成数字人视频');
      return;
    }

    // 使用传入的参数或已保存的状态
    const finalDoctorInfo = info || doctorInfo;
    const finalCoverImageUrl = coverUrl || coverImageUrl;
    const finalEndImageUrl = endUrl || endImageUrl;

    // 验证必要的信息是否存在
    if (!finalCoverImageUrl || !finalEndImageUrl) {
      message.error('请先填写字幕信息');
      setSubtitleModalVisible(true);
      return;
    }

    if (!finalDoctorInfo.doctor_name || !finalDoctorInfo.doctor_title ||
        !finalDoctorInfo.doctor_department || !finalDoctorInfo.doctor_hospital ||
        !finalDoctorInfo.article_title) {
      message.error('请先填写完整的医生信息和文章标题');
      setSubtitleModalVisible(true);
      return;
    }

    // 重置状态
    setIsGeneratingSubtitle(true);
    setSubtitleProgress(0);
    setSubtitleError(null);

    try {
      // 确保图片URL是服务器URL
      if (!finalCoverImageUrl.startsWith('http') || !finalEndImageUrl.startsWith('http')) {
        throw new Error('图片URL必须是服务器URL，请先上传图片');
      }

      // 创建请求数据
      const requestData = {
        title: finalDoctorInfo.article_title || title, // 优先使用文章标题，如果没有则使用视频标题
        video_url: digitalHumanUrl,
        theme_text: finalDoctorInfo.theme_text,
        cover_image_url: finalCoverImageUrl,
        end_image_url: finalEndImageUrl,
        doctor_name: finalDoctorInfo.doctor_name,
        doctor_title: finalDoctorInfo.doctor_title,
        doctor_department: finalDoctorInfo.doctor_department,
        doctor_hospital: finalDoctorInfo.doctor_hospital,
        background_position: finalDoctorInfo.background_position,
        origin_text: voiceCloneText, // 用来生成语音克隆的文本
        digital_human_id: digitalHumanId != 'new' ? digitalHumanId : null
      };

      console.log('使用的文章标题:', finalDoctorInfo.article_title || title);

      console.log('使用服务器图片URL:', {
        封面图片: finalCoverImageUrl,
        结束图片: finalEndImageUrl
      });

      console.log('提交数据:', requestData);

      // 设置进度更新定时器
      const startTime = Date.now();
      const timer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const totalEstimatedTime = 120; // 预计2分钟
        const progressPercent = Math.min(99, (elapsed / totalEstimatedTime) * 100);
        setSubtitleProgress(progressPercent);

        if (elapsed >= totalEstimatedTime) {
          clearInterval(timer);
        }
      }, 1000);

      // 获取认证 token
      const token = getToken();

      // 发送请求到字幕API
      const SUBTITLE_API = `${MANAGER_API_BASE_URL}/api/v1/video/proxy/subtitle`;
      console.log('发送字幕请求到:', SUBTITLE_API);

      const response = await fetch(SUBTITLE_API, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestData),
      });

      // 清除定时器
      clearInterval(timer);

      // 处理API响应
      if (!response.ok) {
        let errorMsg = '';
        try {
          const errorData = await response.json();
          errorMsg = errorData.detail || `服务器返回错误 (${response.status}: ${response.statusText})`;
        } catch (jsonError) {
          // 如果返回的不是JSON格式，直接使用状态文本
          errorMsg = `服务器返回错误 (${response.status}: ${response.statusText})`;
        }

        console.error('API错误详情:', errorMsg);
        throw new Error(errorMsg);
      }

      const result = await response.json();
      console.log('字幕API响应:', result);

      // API现在只返回状态，不再直接返回URL
      if (result.status === DIGITAL_HUMAN_STATUS.GENERATING) {
        // 设置进度为99%，表示请求已成功提交，等待后台处理
        setSubtitleProgress(99);

        // 不再设置subtitleVideoUrl，因为URL将通过回调更新到数据库
        // 用户需要等待处理完成后刷新页面或通过侧边栏查看结果

        message.info('字幕添加请求已提交，正在后台处理中，请稍后刷新页面查看结果');
      } else {
        throw new Error('字幕API返回了意外的状态: ' + result.status);
      }

      // 字幕处理已经提交到后台，不需要在这里更新记录
      // 数据库更新将通过回调完成

      // 提示用户刷新或查看侧边栏
      message.success('字幕添加请求已提交！处理完成后将自动更新');

      // 添加一个定时器，提示用户可以刷新页面查看结果
      setTimeout(() => {
        message.info('您可以稍后刷新页面或通过侧边栏查看处理结果');
      }, 5000);

      // 如果当前URL不匹配数字人ID，更新URL
      if (digitalHumanId && id !== digitalHumanId) {
        navigate(`/digital-human/${digitalHumanId}`, { replace: true });
      }
    } catch (err: any) {
      const errorMessage = err.message || '生成过程中发生错误';
      console.error('字幕添加错误:', errorMessage);
      setSubtitleError(errorMessage);
      message.error('添加字幕失败，请稍后重试');
    } finally {
      setIsGeneratingSubtitle(false);
    }
  };

  return (
    <div className={styles.container} ref={containerRef}>
      <div className={styles.headerContainer}>
        <Title level={2} className={styles.title}>
          <RobotOutlined /> 数字人视频生成
        </Title>
        <Paragraph className={styles.description}>
          通过语音克隆和视频上传，生成带有字幕的数字人视频。
        </Paragraph>
        <div className={styles.headerActions}>
          {id !== 'new' && (
            <Button
              type="primary"
              icon={<RobotOutlined />}
              onClick={() => navigate('/digital-human/new')}
              style={{ marginRight: 16 }}
            >
              新建数字人
            </Button>
          )}
          <div className={styles.headerSettings}>
            <HeaderSettings />
          </div>
        </div>
      </div>

      {isLoading && (
        <div className={styles.loadingContainer}>
          <Spin size="large" />
          <p className={styles.loadingText}>正在加载数字人数据...</p>
        </div>
      )}

      <div className={styles.content}>
        {/* 视频上传部分 - 第一步 */}
        <Card title="第一步：视频上传" className={styles.card} bordered={false}>
          <div className={styles.cardContent}>
            {/* <div className={styles.videoPreviewSection}> */}
              {/* <h3>视频预览</h3> */}
              <div className={styles.videoPreviewWrapper}>
                {videoPreviewUrl ? (
                  // 显示用户上传的视频预览
                  <>
                    <video
                      controls
                      src={videoPreviewUrl}
                      className={styles.videoPlayer}
                    ></video>
                    <Button
                      danger
                      onClick={clearVideo}
                      disabled={isGeneratingDigitalHuman || isGeneratingSubtitle}
                      icon={<DeleteOutlined />}
                      className={styles.deleteVideoBtn}
                    >
                      删除视频
                    </Button>
                  </>
                ) : videoUrl ? (
                  // 显示默认视频链接的预览
                  <div className={styles.defaultVideoPreview}>
                    <video
                      controls
                      src={videoUrl}
                      className={styles.videoPlayer}
                      onError={() => message.error('视频链接无效，请检查链接或上传视频')}
                    ></video>
                    <div className={styles.videoUrlInfo}>
                      <VideoCameraOutlined className={styles.videoUrlIcon} />
                      <span>当前使用默认视频</span>
                    </div>
                  </div>
                ) : (
                  // 没有视频时显示提示
                  <div className={styles.noVideoPreview}>
                    <div className={styles.noVideoIcon}>
                      <VideoCameraOutlined />
                    </div>
                    <p>请上传视频或输入视频链接</p>
                  </div>
                )}
              {/* </div> */}
            </div>

            <Collapse
              activeKey={videoUploadCollapsed ? [] : ['videoUpload']}
              onChange={(keys) => setVideoUploadCollapsed(keys.length === 0)}
              className={styles.uploadCollapse}
            >
              <Collapse.Panel
                header={videoPreviewUrl || (videoUrl && videoUrl !== DEFAULT_VIDEO_URL) ? "更换视频" : "选择视频来源"}
                key="videoUpload"
              >
                <div className={styles.videoUploadContainer}>
                  <div className={styles.videoInputSection}>
                    <div className={styles.videoSourceOptions}>
                      <div className={styles.videoFileUpload}>
                        <Upload.Dragger
                          beforeUpload={handleVideoUpload}
                          showUploadList={false}
                          accept="video/*"
                          disabled={isGeneratingDigitalHuman || isGeneratingSubtitle}
                          className={styles.uploadDragger}
                        >
                          <p className={styles.uploadIcon}>
                            <UploadOutlined />
                          </p>
                          <p className={styles.uploadText}>点击或拖拽文件到此区域上传视频</p>
                          <p className={styles.uploadHint}>
                            支持MP4、WebM等常见视频格式，文件大小不超过100MB
                          </p>
                        </Upload.Dragger>
                      </div>

                      <Divider>或</Divider>

                      <div className={styles.videoUrlInput}>
                        <Form.Item
                          label="使用视频链接"
                          validateStatus={isVideoUrlValid ? 'success' : 'error'}
                          help={isVideoUrlValid ? undefined : '请输入有效的URL地址，以http://或https://开头'}
                        >
                          <Input
                            placeholder="输入新的视频链接替换默认视频"
                            onChange={handleVideoUrlChange}
                            disabled={isGeneratingDigitalHuman || isGeneratingSubtitle || isVideoUploaded}
                            value={videoUrl}
                            prefix={<VideoCameraOutlined />}
                            size="large"
                            allowClear
                          />
                        </Form.Item>
                      </div>
                    </div>
                  </div>
                </div>
              </Collapse.Panel>
            </Collapse>

            {/* 隐藏的标题字段，自动设置默认值 */}
            <Input
              type="hidden"
              value={title || "数字人视频"}
              onChange={handleTitleChange}
            />
          </div>
        </Card>

        {/* 声音克隆部分 - 第二步 */}
        <Card title="第二步：声音克隆" className={styles.card}>
          <div className={styles.cardContent}>
            {isExtractingAudio ? (
              // 正在提取音频
              <div className={styles.loadingContainer}>
                <div className={styles.loadingIcon}><LoadingOutlined spin /></div>
                <p className={styles.loadingText}>正在从视频中提取音频，请稍候...</p>
                <p className={styles.loadingSubText}>提取完成后将自动进入声音克隆步骤</p>
              </div>
            ) : audioExtractError ? (
              // 提取音频出错
              <div className={styles.errorContainer}>
                <p className={styles.errorText}>{audioExtractError}</p>
                <Button
                  onClick={() => {
                    setAudioExtractError(null);
                    if (recordedVideoBlob) {
                      extractAudioFromVideo(recordedVideoBlob as File);
                    } else if (videoUrl && videoUrl !== DEFAULT_VIDEO_URL) {
                      extractAudioFromVideo();
                    }
                  }}
                >
                  重试提取音频
                </Button>
              </div>
            ) : hasVoiceClone ? (
              // 已完成声音克隆，显示结果和折叠面板
              <div>
                <div className={styles.voiceCloneResult}>
                  <div className={styles.voiceCloneInfo}>
                    <SoundOutlined className={styles.voiceCloneIcon} />
                    <span>声音克隆已完成</span>
                    {clonedAudioUrl && (
                      <audio controls src={clonedAudioUrl} className={styles.audioPlayer}></audio>
                    )}
                  </div>
                </div>

                <Collapse
                  activeKey={voiceCloneCollapsed ? [] : ['voiceClone']}
                  onChange={(keys) => setVoiceCloneCollapsed(keys.length === 0)}
                  className={styles.uploadCollapse}
                >
                  <Collapse.Panel
                    header="重新克隆声音"
                    key="voiceClone"
                  >
                    {videoUrl === DEFAULT_VIDEO_URL ? (
                      // 使用默认视频和默认音频
                      <VoiceCloneSimple
                        onVoiceCloneComplete={handleVoiceCloneComplete}
                        initialAudioUrl={DEFAULT_AUDIO_URL}
                        initialTargetText={voiceCloneText}
                        isDefaultAudio={true}
                      />
                    ) : audioUrl ? (
                      // 使用提取的音频
                      <VoiceCloneSimple
                        onVoiceCloneComplete={handleVoiceCloneComplete}
                        initialAudioUrl={audioUrl}
                        initialTargetText={voiceCloneText}
                        isDefaultAudio={false}
                      />
                    ) : (
                      // 其他情况（等待提取音频）
                      <div className={styles.noAudioSection}>
                        <div className={styles.noAudioIcon}>
                          <SoundOutlined />
                        </div>
                        <p>正在准备音频，请稍候...</p>
                      </div>
                    )}
                  </Collapse.Panel>
                </Collapse>
              </div>
            ) : videoUrl === DEFAULT_VIDEO_URL ? (
              // 使用默认视频和默认音频
              <VoiceCloneSimple
                onVoiceCloneComplete={handleVoiceCloneComplete}
                initialAudioUrl={DEFAULT_AUDIO_URL}
                initialTargetText={voiceCloneText}
                isDefaultAudio={true}
              />
            ) : audioUrl ? (
              // 使用提取的音频
              <VoiceCloneSimple
                onVoiceCloneComplete={handleVoiceCloneComplete}
                initialAudioUrl={audioUrl}
                initialTargetText={voiceCloneText}
                isDefaultAudio={false}
              />
            ) : (
              // 其他情况（等待提取音频）
              <div className={styles.noAudioSection}>
                <div className={styles.noAudioIcon}>
                  <SoundOutlined />
                </div>
                <p>正在准备音频，请稍候...</p>
              </div>
            )}
          </div>
        </Card>

        {/* 生成按钮部分 - 第三步 */}
        <Card title="第三步：生成数字人和字幕" className={styles.card}>
          <div className={styles.cardContent}>
            <div className={styles.actionButtons}>
              <div className={styles.buttonGroup}>
                <Button
                  type="primary"
                  icon={<RobotOutlined />}
                  onClick={() => {
                    console.log('生成数字人按钮状态:', {
                      isGeneratingDigitalHuman,
                      isGeneratingSubtitle,
                      videoUrl,
                      isVideoUploaded,
                      hasVoiceClone,
                      title
                    });
                    generateDigitalHuman();
                  }}
                  loading={isGeneratingDigitalHuman}
                  disabled={isGeneratingDigitalHuman || isGeneratingSubtitle || (!videoUrl && !isVideoUploaded) || !hasVoiceClone || !title}
                  size="large"
                  className={styles.actionButton}
                >
                  {isGeneratingDigitalHuman ? '生成数字人中...' : '生成数字人'}
                </Button>

                <Button
                  type="primary"
                  icon={<FileTextOutlined />}
                  onClick={() => setSubtitleModalVisible(true)}
                  loading={isGeneratingSubtitle}
                  disabled={isGeneratingDigitalHuman || isGeneratingSubtitle || !digitalHumanUrl}
                  size="large"
                  className={styles.actionButton}
                >
                  {isGeneratingSubtitle ? '添加字幕中...' : '添加字幕'}
                </Button>
              </div>

              {isGeneratingDigitalHuman && (
                <div className={styles.progressContainer}>
                  <Progress percent={digitalHumanProgress} status="active" />
                  <p className={styles.progressText}>
                    <LoadingOutlined /> 正在生成数字人视频，请耐心等待...
                  </p>
                  {isPolling && (
                    <Button
                      type="primary"
                      icon={<ReloadOutlined />}
                      onClick={() => checkTaskStatus(digitalHumanName || title)}
                      style={{ marginTop: 10 }}
                    >
                      手动检查状态
                    </Button>
                  )}
                </div>
              )}

              {digitalHumanError && (
                <div className={styles.errorContainer}>
                  <p className={styles.errorText}>{digitalHumanError}</p>
                </div>
              )}

              {isGeneratingSubtitle && (
                <div className={styles.progressContainer}>
                  <Progress percent={subtitleProgress} status="active" />
                  <p className={styles.progressText}>
                    <LoadingOutlined /> 正在添加字幕，请耐心等待...
                  </p>
                </div>
              )}

              {subtitleError && (
                <div className={styles.errorContainer}>
                  <p className={styles.errorText}>{subtitleError}</p>
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* 结果预览 */}
        {digitalHumanUrl && (
          <Card title="数字人视频预览" className={styles.card}>
            <div className={styles.resultContainer}>
              <video
                controls
                src={digitalHumanUrl}
                className={styles.videoPlayer}
              ></video>
            </div>
          </Card>
        )}

        {subtitleVideoUrl && (
          <Card title="添加字幕后的视频预览" className={styles.card}>
            <div className={styles.resultContainer}>
              <video
                controls
                src={subtitleVideoUrl}
                className={styles.videoPlayer}
              ></video>
            </div>
          </Card>
        )}
      </div>

      {/* 字幕信息弹窗 */}
      <SubtitleInfoModal
        visible={subtitleModalVisible}
        onCancel={() => setSubtitleModalVisible(false)}
        onOk={handleSubtitleInfoSubmit}
        initialDoctorInfo={doctorInfo}
        initialCoverImageUrl={coverImageUrl}
        initialEndImageUrl={endImageUrl}
        confirmLoading={isGeneratingSubtitle}
      />

      {/* 数字人侧边栏 */}
      <DigitalHumanSidebar
        collapsed={sidebarCollapsed}
        onToggleCollapse={handleToggleSidebar}
        onSelectDigitalHuman={handleSelectDigitalHuman}
        disabled={isGeneratingDigitalHuman || isGeneratingSubtitle} // 在生成过程中禁用侧边栏
      />
    </div>
  );
};

export default IntegratedDigitalHuman;
