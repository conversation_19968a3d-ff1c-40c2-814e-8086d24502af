.container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滑动体验 */
  height: calc(100vh - 64px); /* 适应屏幕高度减去头部导航栏高度 */
  scroll-behavior: smooth; /* 平滑滚动 */
}

.headerContainer {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  color: #1890ff;
  background-color: #fff;
  padding-top: 10px;
  padding-bottom: 10px;
}

.title :global(.anticon) {
  margin-right: 12px;
  font-size: 28px;
  background-color: #e6f7ff;
  padding: 8px;
  border-radius: 50%;
}

.description {
  margin-bottom: 16px;
  font-size: 16px;
  color: #666;
  max-width: 80%;
  line-height: 1.6;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  padding-bottom: 60px; /* 增加底部间距，确保滑动到底部有足够空间 */
}

.card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.previewContainer {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
  overflow-x: auto; /* 允许视频预览容器水平滚动 */
  -webkit-overflow-scrolling: touch;
}

.videoPreview, .audioPreview {
  width: 100%;
  max-height: 400px;
  display: block;
  margin: 0 auto;
  border-radius: 4px;
  touch-action: pan-y; /* 允许在视频上垂直滑动，但阻止水平滑动以避免干扰视频控制 */
}

.audioPreview {
  width: 100%;
  margin: 16px 0;
}

.progressContainer {
  margin-top: 16px;
  padding: 16px;
  background-color: #f6f8fa;
  border-radius: 8px;
  border: 1px solid #e8eaed;
}

.progressContainer :global(.ant-progress) {
  margin-bottom: 8px;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.btn-generating {
  animation: pulse 2s infinite;
}

/* 滚动指示器样式 */
.scrollIndicator {
  position: fixed;
  bottom: 16px;
  right: 16px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1890ff;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: pulse 1.5s infinite;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
    margin: 10px;
    height: calc(100vh - 40px); /* 移动设备高度调整 */
    overflow-y: auto;
  }

  .card {
    margin-bottom: 16px;
  }

  .videoPreview {
    max-height: 300px;
  }

  /* 增强移动设备上的滑动体验 */
  .content {
    gap: 16px;
    padding-bottom: 60px; /* 移动设备底部增加更多空间 */
  }
}

/* 摄像头录制相关样式 */
.recorderContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.videoRecorderPreview {
  width: 100%;
  height: 360px;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cameraPreview,
.recordedPreview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.recorderControls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 16px;
}

.recordingTime {
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: bold;
}

.recordingIndicator {
  color: #ff4d4f;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
.headerSettings {
  position: absolute;
  right: 16px;
  top: 20px;
}



/* 主内容区域样式 */
.content {
  margin-right: 220px; /* 为右侧垂直步骤导航留出空间 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content {
    margin-right: 0;
  }

  .headerContainer {
    padding: 10px;
  }

  .title {
    font-size: 20px;
  }

  .description {
    max-width: 100%;
  }
}

/* 语音克隆集成样式 */
.voiceCloneContainer {
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}



/* 标题样式 */
.sectionTitle {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #1890ff;
  display: flex;
  align-items: center;
}

.sectionTitle :global(.anticon) {
  margin-right: 8px;
}

/* 表单样式 */
.formSection {
  margin-bottom: 20px;
}