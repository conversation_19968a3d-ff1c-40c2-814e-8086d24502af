/* MeetingNotes.module.css */

/* 让 html, body 占满可视区 */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

.container {
  display: flex;
  /* 高度占满窗口的 95% */
  height: 95vh;
  width: 1200px; /* 如不需要固定宽度，可改成 100% */
  background: #f0f2f5;
  margin: 0 auto; /* 让整体容器居中 */
}

/* 左栏：上传 + 预览 + 说话人背景 */
.leftContainer {
  width: 500px;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

/* 中栏：转写内容 */
.middleContainer {
  width: 500px;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow-y: auto; /* 内容多时可滚动 */
}

/* 右栏：AI 生成 */
.rightContainer {
  width: 500px;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

/* 上传区外观 */
.uploadArea {
  border: 2px dashed #1890ff;
  border-radius: 8px;
  background-color: #fff;
  padding: 32px;
  text-align: center;
  cursor: pointer;
  margin-bottom: 16px;
}
.uploadArea.dragOver {
  background-color: #e6f7ff;
}

.uploadInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.uploadTitle {
  font-size: 16px;
  margin: 8px 0;
  font-weight: 500;
  color: #333;
}

.uploadDescription {
  color: #999;
  margin-top: 8px;
  line-height: 1.6;
}

.buttonArea {
  /* margin-top: auto; */
  margin-top: 16px;
}

.loading {
  color: #fa8c16;
  font-weight: bold;
  margin-bottom: 16px;
}

/* 转写列表样式 */
.speakerItem {
  margin-bottom: 12px;
}

.speakerTitle {
  font-weight: bold;
  margin-bottom: 4px;
  color: #000;
}
.speakerTitle span {
  color: #999;
  font-size: 0.9em;
  margin-left: 4px;
}

.textLine {
  margin-left: 16px;
  color: #333;
}

/* 右栏消息展示 */
.chatMessages {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #ccc;
  padding: 1px;
  margin-bottom: 16px;
  background-color: #fefefe;
  width: 450px;
}

/* 右栏下方的“生成”按钮 */
.generateButton {
  align-self: flex-end;
}

.blackText {
  color: #000;
}
