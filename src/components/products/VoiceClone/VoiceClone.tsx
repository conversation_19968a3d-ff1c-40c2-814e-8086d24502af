import React, { useState, useRef, useEffect } from 'react';
import { Button, Input, message, Card, Typography, Alert, Upload, Tabs, Slider, Modal, Space } from 'antd';
import { AudioOutlined, AudioMutedOutlined, SendOutlined, UploadOutlined, ScissorOutlined, RedoOutlined } from '@ant-design/icons';
import styles from './VoiceClone.module.css';
import { MANAGER_API_BASE_URL } from '../../../Constant/ServerConstant';
import HeaderSettings from '../../common/HeaderSettings';


const { TextArea } = Input;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 后端API地址
const API_URL = `${MANAGER_API_BASE_URL}/api/v1/audio/tts/proxy_synthesize`;
const ASR_API_URL = `${MANAGER_API_BASE_URL}/api/v1/audio/asr/transcribe`;
const getToken = (): string | null => localStorage.getItem('access_token');
const token = getToken();

// WAV格式转换函数
const convertToWav = async (audioBlob: Blob): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    // 创建音频上下文
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    
    // 创建文件读取器
    const fileReader = new FileReader();
    
    fileReader.onload = async (event) => {
      try {
        // 解码音频数据
        const arrayBuffer = event.target?.result as ArrayBuffer;
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        
        // 准备WAV格式数据
        const wavBuffer = createWavBuffer(audioBuffer);
        
        // 创建WAV格式的Blob
        const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });
        resolve(wavBlob);
      } catch (error) {
        console.error('转换音频格式失败:', error);
        reject(error);
      }
    };
    
    fileReader.onerror = (error) => {
      console.error('读取音频文件失败:', error);
      reject(error);
    };
    
    // 开始读取Blob数据
    fileReader.readAsArrayBuffer(audioBlob);
  });
};

// 创建WAV格式的Buffer
const createWavBuffer = (audioBuffer: AudioBuffer): ArrayBuffer => {
  const numOfChannels = audioBuffer.numberOfChannels;
  const length = audioBuffer.length * numOfChannels * 2; // 2字节每样本
  const sampleRate = audioBuffer.sampleRate;
  
  // WAV文件头 + 数据大小
  const buffer = new ArrayBuffer(44 + length);
  const view = new DataView(buffer);
  
  // WAV文件头
  // "RIFF"标识
  writeString(view, 0, 'RIFF');
  // 文件大小
  view.setUint32(4, 36 + length, true);
  // "WAVE"标识
  writeString(view, 8, 'WAVE');
  // "fmt "子块
  writeString(view, 12, 'fmt ');
  // 子块大小
  view.setUint32(16, 16, true);
  // 音频格式 (1 表示 PCM)
  view.setUint16(20, 1, true);
  // 通道数
  view.setUint16(22, numOfChannels, true);
  // 采样率
  view.setUint32(24, sampleRate, true);
  // 字节率
  view.setUint32(28, sampleRate * numOfChannels * 2, true);
  // 数据块对齐
  view.setUint16(32, numOfChannels * 2, true);
  // 每个样本位数
  view.setUint16(34, 16, true);
  // "data"子块
  writeString(view, 36, 'data');
  // 数据大小
  view.setUint32(40, length, true);
  
  // 写入实际音频数据
  const channelData = [];
  for (let i = 0; i < numOfChannels; i++) {
    channelData.push(audioBuffer.getChannelData(i));
  }
  
  let offset = 44;
  for (let i = 0; i < audioBuffer.length; i++) {
    for (let channel = 0; channel < numOfChannels; channel++) {
      // 将-1.0 - 1.0的浮点值转换为-32768 - 32767的整数
      const sample = Math.max(-1, Math.min(1, channelData[channel][i]));
      const value = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(offset, value, true);
      offset += 2;
    }
  }
  
  return buffer;
};

// 辅助函数：将字符串写入DataView
const writeString = (view: DataView, offset: number, string: string): void => {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
};

// 格式化录音时间
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 步骤1组件：录音组件
interface RecordingStepProps {
  onAudioReady: (blob: Blob) => void;
  onTextChange: (text: string) => void;
  promptText: string;
}

const RecordingStep: React.FC<RecordingStepProps> = ({ onAudioReady, onTextChange, promptText }) => {
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const [recordingDuration, setRecordingDuration] = useState<number>(0);
  const [showTrimmer, setShowTrimmer] = useState<boolean>(false);
  const [audioUrl, setAudioUrl] = useState<string>('');

  // 录音相关引用
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 开始录音
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          echoCancellation: true,
          noiseSuppression: true
        } 
      });
      
      // 尝试使用常见的音频格式
      let options;
      
      // 浏览器可能不直接支持WAV格式录制，我们将使用支持的格式然后必要时进行转换
      if (MediaRecorder.isTypeSupported('audio/webm')) {
        options = { mimeType: 'audio/webm' };
      } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
        options = { mimeType: 'audio/mp4' };
      } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
        options = { mimeType: 'audio/ogg; codecs=opus' };
      } else {
        // 使用默认格式
        options = {};
      }
        
      console.log('使用录音格式:', options.mimeType || '默认格式');
      const mediaRecorder = new MediaRecorder(stream, options);
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        // 将录音数据组合成一个 Blob，使用媒体记录器的实际类型
        const mimeType = mediaRecorder.mimeType || 'audio/webm';
        const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
        setRecordedBlob(audioBlob);
        
        // 创建音频URL
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        
        console.log('录音完成, Blob 信息:', {
          type: audioBlob.type,
          size: audioBlob.size,
          mimeType: mimeType
        });
        
        // 回调通知父组件
        onAudioReady(audioBlob);
        
        // 停止所有轨道以释放麦克风
        stream.getTracks().forEach(track => track.stop());
      };

      // 每 1 秒获取一次录音数据
      mediaRecorder.start(1000);
      setIsRecording(true);
      setRecordingDuration(0);
      
      // 设置计时器
      timerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('录音失败:', error);
      message.error('无法访问麦克风，请确保已授予麦克风使用权限');
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      // 清除计时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // 打开裁剪器
  const openTrimmer = () => {
    setShowTrimmer(true);
  };

  // 处理裁剪完成
  const handleTrimComplete = async (trimmedBlob: Blob) => {
    // 更新状态
    setRecordedBlob(trimmedBlob);
    
    // 释放旧的URL
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    
    // 创建新的URL
    const newUrl = URL.createObjectURL(trimmedBlob);
    setAudioUrl(newUrl);
    
    // 回调父组件
    onAudioReady(trimmedBlob);
    
    // 关闭裁剪器
    setShowTrimmer(false);
    
    message.success('音频裁剪成功');
  };

  // 清理资源
  useEffect(() => {
    return () => {
      // 清除URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, []);

  return (
    <div className={styles.cardContent}>
      <div className={styles.recordSection}>
        <Button
          type="primary"
          icon={isRecording ? <AudioMutedOutlined /> : <AudioOutlined />}
          onClick={isRecording ? stopRecording : startRecording}
          danger={isRecording}
          className={styles.recordButton}
        >
          {isRecording ? '停止录音' : '开始录音'}
        </Button>
        
        {isRecording && (
          <div className={styles.recordingIndicator}>
            <span className={styles.recordingDot}></span>
            <Text>正在录音... {formatDuration(recordingDuration)}</Text>
          </div>
        )}
        
        {recordedBlob && (
          <div className={styles.audioPreview}>
            <Text strong>录音预览:</Text>
            <audio controls src={audioUrl} className={styles.audioPlayer}></audio>
            
            <div className={styles.audioActions}>
              <Button 
                type="primary" 
                size="small" 
                icon={<ScissorOutlined />} 
                onClick={openTrimmer}
              >
                裁剪音频
              </Button>
            </div>
          </div>
        )}
      </div>
      
      <div className={styles.inputSection}>
        <Text>录音文字【请点击<strong>开始录音</strong>后，朗读下方文字（或自行输入内容后进行朗读）】:</Text>
        <TextArea
          placeholder="请输入录音的文字内容"
          value={promptText}
          onChange={(e) => onTextChange(e.target.value)}
          className={styles.textInput}
          rows={4}
        />
      </div>

      {/* 音频裁剪弹窗 */}
      <Modal
        title="音频裁剪"
        open={showTrimmer}
        footer={null}
        onCancel={() => setShowTrimmer(false)}
        width={600}
        destroyOnHidden
      >
        {audioUrl && (
          <AudioTrimmer
            audioUrl={audioUrl}
            onTrimComplete={handleTrimComplete}
            onCancel={() => setShowTrimmer(false)}
          />
        )}
      </Modal>
    </div>
  );
};

// 步骤1扩展：上传音频文件组件
interface UploadAudioStepProps {
  onAudioReady: (blob: Blob) => void;
  onTextChange: (text: string) => void;
  promptText: string;
}

const UploadAudioStep: React.FC<UploadAudioStepProps> = ({ onAudioReady, onTextChange, promptText }) => {
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [isTranscribing, setIsTranscribing] = useState<boolean>(false);
  const [showTrimmer, setShowTrimmer] = useState<boolean>(false);
  const [currentAudioBlob, setCurrentAudioBlob] = useState<Blob | null>(null);

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    // 检查文件类型
    const acceptedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/x-m4a', 'audio/flac'];
    const fileType = file.type;
    
    if (!acceptedTypes.includes(fileType) && 
        !file.name.endsWith('.mp3') && 
        !file.name.endsWith('.wav') && 
        !file.name.endsWith('.m4a') && 
        !file.name.endsWith('.flac')) {
      message.error('请上传MP3、WAV、M4A或FLAC格式的音频文件');
      return false;
    }
    
    // 检查文件大小（限制为20MB）
    if (file.size > 20 * 1024 * 1024) {
      message.error('文件大小不能超过20MB');
      return false;
    }
    
    try {
      // 释放旧的URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }

      // 创建音频URL
      const newUrl = URL.createObjectURL(file);
      setAudioUrl(newUrl);
      setCurrentAudioBlob(file);
      
      // 回调通知父组件
      onAudioReady(file);
      
      // 自动识别音频内容
      await transcribeAudio(file);
      
      return false; // 不上传到默认上传列表
    } catch (error) {
      console.error('处理上传失败:', error);
      message.error('文件处理失败，请重试');
      return false;
    }
  };

  // 识别音频内容
  const transcribeAudio = async (audioFile: Blob) => {
    setIsTranscribing(true);
    
    try {
      // 创建FormData
      const formData = new FormData();
      formData.append('input_audio_file', new File([audioFile], 'audio.mp3', { type: audioFile.type }));
      
      // 发送请求
      const response = await fetch(ASR_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error(`服务器响应错误: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result && result.transcription) {
        message.success('音频识别成功');
        onTextChange(result.transcription);
      } else {
        throw new Error('识别结果格式不正确');
      }
    } catch (error) {
      console.error('音频识别失败:', error);
      message.error(`音频识别失败: ${error instanceof Error ? error.message : '请重试'}`);
    } finally {
      setIsTranscribing(false);
    }
  };

  // 打开裁剪器
  const openTrimmer = () => {
    setShowTrimmer(true);
  };

  // 处理裁剪完成
  const handleTrimComplete = async (trimmedBlob: Blob) => {
    // 更新状态
    setCurrentAudioBlob(trimmedBlob);
    
    // 释放旧的URL
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    
    // 创建新的URL
    const newUrl = URL.createObjectURL(trimmedBlob);
    setAudioUrl(newUrl);
    
    // 回调父组件
    onAudioReady(trimmedBlob);
    
    // 关闭裁剪器
    setShowTrimmer(false);
    
    message.success('音频裁剪成功');
    
    // 询问用户是否需要重新识别
    Modal.confirm({
      title: '音频已裁剪',
      content: '是否需要重新识别音频内容？',
      okText: '是',
      cancelText: '否',
      onOk: () => transcribeAudio(trimmedBlob)
    });
  };

  // 重新识别
  const handleRetranscribe = () => {
    if (!currentAudioBlob) {
      message.error('没有可用的音频文件');
      return;
    }
    
    transcribeAudio(currentAudioBlob);
  };

  // 清理资源
  useEffect(() => {
    return () => {
      // 清除URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, []);

  return (
    <div className={styles.cardContent}>
      <div className={styles.uploadSection}>
        <Upload.Dragger
          name="audioFile"
          accept=".mp3,.wav,.m4a,.flac"
          showUploadList={false}
          beforeUpload={handleFileUpload}
          disabled={isTranscribing}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽音频文件到此区域上传</p>
          <p className="ant-upload-hint">支持 MP3、WAV、M4A、FLAC 格式，大小不超过20MB</p>
        </Upload.Dragger>
        
        {audioUrl && (
          <div className={styles.audioPreview}>
            <Text strong>音频预览:</Text>
            <audio controls src={audioUrl} className={styles.audioPlayer}></audio>
            
            <div className={styles.audioActions}>
              <Space>
                <Button 
                  type="primary" 
                  size="small" 
                  icon={<ScissorOutlined />} 
                  onClick={openTrimmer}
                >
                  裁剪音频
                </Button>
                <Button 
                  size="small" 
                  icon={<RedoOutlined />} 
                  onClick={handleRetranscribe}
                  disabled={isTranscribing}
                >
                  重新识别
                </Button>
              </Space>
            </div>
          </div>
        )}
      </div>
      
      <div className={styles.inputSection}>
        <Text>录音文字【请点击开始录音后，朗读下方文字（或自行输入内容后进行朗读）】:</Text>
        <TextArea
          placeholder={isTranscribing ? "正在识别音频内容..." : "请输入录音的文字内容"}
          value={promptText}
          onChange={(e) => onTextChange(e.target.value)}
          className={styles.textInput}
          rows={4}
          disabled={isTranscribing}
        />
      </div>

      {/* 音频裁剪弹窗 */}
      <Modal
        title="音频裁剪"
        open={showTrimmer}
        footer={null}
        onCancel={() => setShowTrimmer(false)}
        width={600}
  destroyOnHidden
      >
        {audioUrl && (
          <AudioTrimmer
            audioUrl={audioUrl}
            onTrimComplete={handleTrimComplete}
            onCancel={() => setShowTrimmer(false)}
          />
        )}
      </Modal>
    </div>
  );
};

// 步骤2组件：目标文本输入
interface TargetTextStepProps {
  targetText: string;
  onTextChange: (text: string) => void;
  onGenerate: () => void;
  isGenerating: boolean;
  isDisabled: boolean;
}

const TargetTextStep: React.FC<TargetTextStepProps> = ({ 
  targetText, 
  onTextChange, 
  onGenerate, 
  isGenerating, 
  isDisabled 
}) => {
  return (
    <div className={styles.cardContent}>
      <TextArea
        placeholder="请输入需要用您的声音生成的文字内容"
        value={targetText}
        onChange={(e) => onTextChange(e.target.value)}
        className={styles.textInput}
        rows={6}
      />
      
      <div className={styles.buttonGroup}>
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={onGenerate}
          loading={isGenerating}
          disabled={isDisabled}
          className={styles.generateButton}
        >
          {isGenerating ? '生成中...' : '生成语音'}
        </Button>
      </div>
    </div>
  );
};

// 步骤3组件：结果展示
interface ResultStepProps {
  audioUrl: string;
}

const ResultStep: React.FC<ResultStepProps> = ({ audioUrl }) => {
  if (!audioUrl) return null;
  
  return (
    <div className={styles.result}>
      <Alert
        message="语音生成成功"
        description="您可以直接播放或下载生成的语音文件"
        type="success"
        showIcon
      />
      
      <div className={styles.audioResult}>
        <audio controls src={audioUrl} className={styles.audioPlayer}></audio>
        
        <div className={styles.downloadSection}>
          <Text strong>下载链接:</Text>
          <a href={audioUrl} target="_blank" rel="noreferrer" download>
            {audioUrl}
          </a>
        </div>
      </div>
    </div>
  );
};

// 音频裁剪组件接口
interface AudioTrimmerProps {
  audioUrl: string;
  onTrimComplete: (trimmedBlob: Blob) => void;
  onCancel: () => void;
}

// 音频裁剪组件
const AudioTrimmer: React.FC<AudioTrimmerProps> = ({ audioUrl, onTrimComplete, onCancel }) => {
  const [trimRange, setTrimRange] = useState<[number, number]>([0, 0]);
  const [audioDuration, setAudioDuration] = useState<number>(0);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioBufferRef = useRef<AudioBuffer | null>(null);

  // 初始化音频信息
  useEffect(() => {
    const loadAudio = async () => {
      try {
        // 创建AudioContext
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        audioContextRef.current = audioContext;
        
        // 加载音频
        const response = await fetch(audioUrl);
        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        
        // 设置音频缓冲区和持续时间
        audioBufferRef.current = audioBuffer;
        const duration = audioBuffer.duration;
        setAudioDuration(duration);
        // 初始化裁剪范围为整个音频
        setTrimRange([0, duration]);
      } catch (error) {
        console.error('加载音频失败:', error);
        message.error('加载音频失败，请重试');
      }
    };
    
    loadAudio();
    
    // 清理函数
    return () => {
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
    };
  }, [audioUrl]);

  // 播放预览
  const playPreview = () => {
    if (!audioRef.current) return;
    
    const startTime = trimRange[0];
    const endTime = trimRange[1];
    
    audioRef.current.currentTime = startTime;
    
    // 播放指定时长后停止
    const stopTime = endTime - startTime;
    const playPromise = audioRef.current.play();
    
    if (playPromise !== undefined) {
      playPromise.then(() => {
        setTimeout(() => {
          if (audioRef.current) {
            audioRef.current.pause();
          }
        }, stopTime * 1000);
      }).catch(err => {
        console.error('播放失败:', err);
      });
    }
  };

  // 处理裁剪完成
  const handleTrimComplete = async () => {
    if (!audioBufferRef.current || !audioContextRef.current) {
      message.error('音频未加载完成，请等待');
      return;
    }
    
    setIsProcessing(true);
    
    try {
      const audioBuffer = audioBufferRef.current;
      const startSample = Math.floor((trimRange[0] / audioBuffer.duration) * audioBuffer.length);
      const endSample = Math.floor((trimRange[1] / audioBuffer.duration) * audioBuffer.length);
      const trimmedLength = endSample - startSample;
      
      // 创建新的AudioBuffer
      const trimmedBuffer = audioContextRef.current.createBuffer(
        audioBuffer.numberOfChannels,
        trimmedLength,
        audioBuffer.sampleRate
      );
      
      // 复制选中区域的数据
      for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
        const channelData = audioBuffer.getChannelData(channel);
        const trimmedData = trimmedBuffer.getChannelData(channel);
        
        for (let i = 0; i < trimmedLength; i++) {
          trimmedData[i] = channelData[startSample + i];
        }
      }
      
      // 转换为WAV Blob
      const wavBuffer = createWavBuffer(trimmedBuffer);
      const trimmedBlob = new Blob([wavBuffer], { type: 'audio/wav' });
      
      onTrimComplete(trimmedBlob);
    } catch (error) {
      console.error('裁剪音频失败:', error);
      message.error('裁剪音频失败，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理滑动条变化
  const handleSliderChange = (value: number[]) => {
    setTrimRange(value as [number, number]);
  };

  return (
    <div className={styles.trimmerContainer}>
      <div className={styles.trimmerHeader}>
        <Text strong>音频裁剪</Text>
        <Text type="secondary">选择要保留的音频片段</Text>
      </div>
      
      <div className={styles.audioPreview}>
        <audio ref={audioRef} src={audioUrl} controls className={styles.audioPlayer}></audio>
      </div>
      
      <div className={styles.trimControls}>
        <Text>裁剪范围 (秒):</Text>
        <Slider
          range
          min={0}
          max={audioDuration}
          step={0.1}
          value={trimRange}
          onChange={handleSliderChange}
          className={styles.trimSlider}
          tooltipVisible
          tipFormatter={(value?: number) => value !== undefined ? `${value.toFixed(1)}秒` : ''}
        />
        
        <div className={styles.timeLabels}>
          <Text type="secondary">{trimRange[0].toFixed(1)}秒</Text>
          <Text type="secondary">{trimRange[1].toFixed(1)}秒</Text>
        </div>
      </div>
      
      <div className={styles.trimActions}>
        <Button onClick={playPreview} icon={<AudioOutlined />}>
          预览
        </Button>
        <Button 
          type="primary" 
          onClick={handleTrimComplete} 
          loading={isProcessing}
          icon={<ScissorOutlined />}
        >
          确认裁剪
        </Button>
        <Button onClick={onCancel}>取消</Button>
      </div>
    </div>
  );
};

// 主组件
interface VoiceCloneProps {}

const VoiceClone: React.FC<VoiceCloneProps> = () => {
  // 状态管理
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const [promptText, setPromptText] = useState<string>('生活就像一杯茶，不会苦一辈子，但总会苦一阵子。重要的不是此刻的苦涩，而是你如何品味其中的回甘。保持微笑，继续前行，美好终将如期而至');
  const [targetText, setTargetText] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [generatedAudioUrl, setGeneratedAudioUrl] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('record');

  // 添加一个ref来引用容器元素
  const containerRef = useRef<HTMLDivElement>(null);

  // 添加滚动监听
  useEffect(() => {
    const handleScroll = () => {
      // 可以在这里添加滚动效果逻辑，例如显示/隐藏返回顶部按钮等
    };

    // 添加滚动事件监听
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    // 清理函数
    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  // 在生成语音成功后滚动到结果区域
  useEffect(() => {
    if (generatedAudioUrl) {
      // 使用setTimeout确保DOM已更新
      setTimeout(() => {
        const resultElement = document.querySelector(`.${styles.result}`);
        if (resultElement) {
          resultElement.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    }
  }, [generatedAudioUrl]);

  // 处理音频就绪
  const handleAudioReady = (blob: Blob) => {
    setRecordedBlob(blob);
  };

  // 生成语音
  const generateVoice = async () => {
    if (!recordedBlob) {
      message.error('请先录制或上传声音');
      return;
    }
    if (!promptText.trim()) {
      message.error('请输入录音文字');
      return;
    }
    if (!targetText.trim()) {
      message.error('请输入需要生成的文字');
      return;
    }

    setIsGenerating(true);
    try {
      console.log('开始转换音频格式为WAV...');
      
      // 转换音频为WAV格式
      let wavBlob;
      try {
        wavBlob = await convertToWav(recordedBlob);
        console.log('转换成功，WAV文件大小:', wavBlob.size);
      } catch (error) {
        console.error('转换WAV格式失败:', error);
        message.error('音频格式转换失败，请重试');
        setIsGenerating(false);
        return;
      }
      
      // 创建FormData对象
      const formData = new FormData();
      
      // 使用转换后的WAV文件
      formData.append('prompt_audio', wavBlob, 'output.wav');
      formData.append('prompt_text', promptText);
      formData.append('text', targetText);
      
      console.log('提交参数:', {
        prompt_text: promptText,
        text: targetText,
        audio_blob_size: wavBlob.size,
        audio_blob_type: wavBlob.type,
        file_name: 'output.wav'
      });

      // 发送请求
      const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });
      
      console.log('响应状态:', response.status, response.statusText);
      
      if (!response.ok) {
        let errorMessage = `服务器响应错误: ${response.status}`;
        try {
          const errorText = await response.text();
          console.error('服务器错误详情:', errorText);
          errorMessage += ` - ${errorText}`;
        } catch (e) {
          console.error('无法获取错误详情');
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('响应数据:', data);
      
      if (data && data.url) {
        setGeneratedAudioUrl(data.url);
        message.success('语音生成成功');
      } else {
        throw new Error('返回数据格式不正确');
      }
    } catch (error: any) {
      console.error('生成失败:', error);
      message.error(`语音生成失败: ${error.message || '请重试'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  // 重置所有状态
  const resetAll = () => {
    setRecordedBlob(null);
    setPromptText('生活就像一杯茶，不会苦一辈子，但总会苦一阵子。重要的不是此刻的苦涩，而是你如何品味其中的回甘。保持微笑，继续前行，美好终将如期而至');
    setTargetText('');
    setGeneratedAudioUrl('');
  };

  return (
    <div className={styles.container} ref={containerRef}>
      <div className={styles.header}>
        <Title level={2}>语音克隆工具</Title>
        <Text className={styles.description}>
          通过录制或上传您的声音，可以生成相同声音风格的其他文本语音
        </Text>
      </div>
      <div className={styles.headerSettings}>
        <HeaderSettings />
      </div>
      <div className={styles.content}>
        <Card title="步骤 1: 提供您的声音" className={styles.card}>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane 
              tab={
                <span>
                  <AudioOutlined />
                  录制声音
                </span>
              } 
              key="record"
            >
              <RecordingStep 
                onAudioReady={handleAudioReady} 
                onTextChange={setPromptText}
                promptText={promptText}
              />
            </TabPane>
            <TabPane 
              tab={
                <span>
                  <UploadOutlined />
                  上传音频
                </span>
              } 
              key="upload"
            >
              <UploadAudioStep 
                onAudioReady={handleAudioReady} 
                onTextChange={setPromptText}
                promptText={promptText}
              />
            </TabPane>
          </Tabs>
        </Card>

        <Card title="步骤 2: 输入需要生成的文字" className={styles.card}>
          <TargetTextStep 
            targetText={targetText}
            onTextChange={setTargetText}
            onGenerate={generateVoice}
            isGenerating={isGenerating}
            isDisabled={!recordedBlob || !promptText.trim() || !targetText.trim() || isGenerating}
          />
          <div className={styles.resetButtonContainer}>
              <Button onClick={resetAll} className={styles.resetButton}>
              重置所有
              </Button>
          </div>
        </Card>

        {generatedAudioUrl && (
          <Card title="步骤 3: 生成结果" className={styles.card}>
            <ResultStep audioUrl={generatedAudioUrl} />
          </Card>
        )}
      </div>
    </div>
  );
};

export default VoiceClone; 