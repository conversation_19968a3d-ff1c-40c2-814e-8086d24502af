import React, { useCallback, useEffect, useState } from 'react';
import styles from './MeetingNotes.module.css';
import { Button, notification, Input } from 'antd';
import { useXAgent, useXChat, Bubble } from '@ant-design/x';

import { MANAGER_API_BASE_URL } from '../../Constant/ServerConstant';
import VideoPreview from './VideoPreview';
import AudioPreview from './AudioPreview';
import { roles } from '../Chat/constants';

/** 后端返回的转写条目 */
type TranscriptionItem = {
  start: number;   // 开始时间(秒)
  end: number;     // 结束时间(秒)
  text: string;    // 文本内容
  speaker: string; // 说话人标识
};

/** 后端返回的数据结构 { url, content } */
type AsrResponse = {
  url: string;                  // 后端返回的音/视频URL
  content: TranscriptionItem[]; // 识别的内容数组
};

/** 我们在前端维护的 speaker 信息结构 */
type SpeakerInfo = {
  speaker: string;
  background: string; // 背景介绍
};

const MeetingNotes: React.FC = () => {
  /* ---------------------- 左栏：上传与预览 ---------------------- */
  const [isDragging, setIsDragging] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>('');

  // 拖拽事件
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);
  
  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);
  
  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    if (e.dataTransfer.files?.length) {
      const droppedFile = e.dataTransfer.files[0];
      setFile(droppedFile);
      
      // 立即创建本地预览URL
      if (droppedFile.type.includes('video') || droppedFile.type.includes('audio')) {
        const localUrl = URL.createObjectURL(droppedFile);
        setPreviewUrl(localUrl);
      }
    }
  }, []);


  // 清理预览URL
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  /* ---------------------- 中栏：转写内容 ---------------------- */
  const [transcriptions, setTranscriptions] = useState<TranscriptionItem[]>([]);
  const [speakerInfos, setSpeakerInfos] = useState<SpeakerInfo[]>([]);

  // 调用后端 API 上传 & 语音识别
  const handleUpload = async () => {
    if (!file) return;
    setLoading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${MANAGER_API_BASE_URL}/api/v1/audio/asr/predict`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = (await response.json()) as AsrResponse;
      console.log('后端完整返回:', result);

      // 更新转写内容
      setTranscriptions(result.content);

      // 提取所有 speaker 并初始化 speakerInfos
      const sorted = [...result.content].sort((a, b) => a.start - b.start);
      const uniqueSpeakers = Array.from(new Set(sorted.map((t) => t.speaker)));
      const newSpeakerInfos: SpeakerInfo[] = uniqueSpeakers.map((sp) => ({
        speaker: sp,
        background: '',
      }));
      setSpeakerInfos(newSpeakerInfos);
    } catch (error) {
      console.error('识别失败：', error);
      notification.error({
        message: '识别失败',
        description: '上传或识别失败，请稍后重试'
      });
    } finally {
      setLoading(false);
    }
  };

  // 检查文件类型
  const isAudio = file?.type.includes('audio') || previewUrl.toLowerCase().endsWith('.mp3') || previewUrl.toLowerCase().endsWith('.wav') || previewUrl.toLowerCase().endsWith('.ogg');
  const isVideo = file?.type.includes('video') || previewUrl.toLowerCase().endsWith('.mp4') || previewUrl.toLowerCase().endsWith('.mov') || previewUrl.toLowerCase().endsWith('.avi');

  // 将转写按开始时间排序
  const sortedTranscriptions = [...transcriptions].sort((a, b) => a.start - b.start);

  // 当用户在输入框中修改 speaker 背景介绍时
  const handleSpeakerInfoChange = (sp: string, newVal: string) => {
    setSpeakerInfos((prev) =>
      prev.map((si) => {
        if (si.speaker === sp) {
          return { ...si, background: newVal };
        }
        return si;
      })
    );
  };

  /* ---------------------- 右栏：流式生成 (useXAgent + useXChat) ---------------------- */
  const [agent] = useXAgent({
    request: async (info, callbacks) => {
      const { message } = info;
      const { onSuccess, onUpdate, onError } = callbacks;

      if (!message) {
        onError(new Error('No message to send.'));
        return;
      }

      let accumulatedContent = '';
      try {
        // 示例：模拟每 300ms 拼接一块文本
        for (let i = 0; i < 5; i++) {
          await new Promise((resolve) => setTimeout(resolve, 300));
          const chunk = `这是第 ${i + 1} 块内容。`;
          accumulatedContent += chunk;
          onUpdate(accumulatedContent);
        }
        onSuccess(accumulatedContent);
      } catch (error) {
        notification.error({
          message: 'Error',
          description: 'An error occurred while processing your request.',
        });
        onError(error instanceof Error ? error : new Error('Request failed.'));
      }
    },
  });

  const { onRequest, messages } = useXChat({ agent });

  // 拼接要发送给 AI 的内容
  const handleGenerate = () => {
    if (sortedTranscriptions.length === 0) {
      notification.warning({
        message: '暂无可生成内容',
        description: '中间栏没有任何识别结果，无法生成。',
      });
      return;
    }

    // 1. 拼接转写文本
    const transText = sortedTranscriptions
      .map((t) => `[${t.speaker}] ${t.text}`)
      .join('\n');

    // 2. speaker 背景信息
    let speakerInfoText = '';
    speakerInfos.forEach((si) => {
      speakerInfoText += `\n【${si.speaker} 的背景介绍】\n${si.background || '暂无'}\n`;
    });

    // 3. 合并给 AI
    const finalInput = `
      以下是会议转写内容：
      ${transText}

      以下是每位说话人的背景介绍：
      ${speakerInfoText}

      基于以上内容，请生成会议纪要：
    `;

    onRequest(finalInput.trim());
  };

  // 渲染消息列表
  const bubbleItems = messages.map(({ id, message, status }) => ({
    key: id,
    role: status === 'local' ? 'local' : 'ai',
    content: message,
  }));

  return (
    <div className={styles.container}>
      {/* 左栏：上传 / 预览 */}
      <div className={styles.leftContainer}>
        <div
          className={`${styles.uploadArea} ${isDragging ? styles.dragOver : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className={styles.uploadInfo}>
            <img
              src="https://cdn-icons-png.flaticon.com/512/1587/1587980.png"
              alt="upload icon"
              width="40"
              height="40"
              style={{ opacity: 0.6 }}
            />
            <div className={styles.uploadTitle} style={{ color: '#000' }}> 
              点击上传或拖拽文件到这里
            </div>
            <p className={styles.uploadDescription} style={{ color: '#000' }}>
              支持 MP4, MP3, WAV, MOV 等格式。<br />
              单个文件最大 20MB。
            </p>
          </div>
        </div>

        {/* 显示上传的文件名 */}
        {file && (
          <div style={{ marginTop: 12, color: '#000', fontWeight: 'bold' }}>
            已选择文件：{file.name}
          </div>
        )}

        {/* 文件预览 */}
        {previewUrl && (
          <div style={{ marginTop: 16 }}>
            {isAudio && <AudioPreview src={previewUrl} />}
            {isVideo && <VideoPreview src={previewUrl} />}
          </div>
        )}

        {/* 按钮 + 上传识别 */}
        <div className={styles.buttonArea}>
          <Button onClick={handleUpload} disabled={!file || loading}>
            {loading ? '识别中...' : '上传识别'}
          </Button>
        </div>

        {loading && <div className={styles.loading}>Loading...</div>}

        {/* 说话人背景信息输入框 */}
        {speakerInfos.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <h3 style={{ color: '#000' }}>背景介绍</h3>
            {speakerInfos.map((si) => (
              <div key={si.speaker} style={{ marginBottom: 12 }}>
                <h4 style={{ margin: '8px 0 4px 0', color: '#000' }}>
                  {si.speaker} 的背景介绍
                </h4>
                <Input.TextArea
                  rows={3}
                  value={si.background}
                  onChange={(e) => handleSpeakerInfoChange(si.speaker, e.target.value)}
                  placeholder="请填写该说话人的背景介绍"
                  style={{ color: '#000' }}
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 中栏：展示转写内容 */}
      <div className={styles.middleContainer}>
        {sortedTranscriptions.map((item, index) => (
          <div className={styles.speakerItem} key={index}>
            <div className={styles.speakerTitle} style={{ color: '#000' }}>
              {item.speaker}
              <span style={{ color: '#999' }}>
                ( {item.start.toFixed(2)} ~ {item.end.toFixed(2)} )
              </span>
            </div>
            <div className={styles.textLine} style={{ color: '#000' }}>
              {item.text}
            </div>
          </div>
        ))}
      </div>

      {/* 右栏：AI 生成 */}
      <div className={styles.rightContainer}>
        <div className={styles.chatMessages}>
          <Bubble.List
            items={bubbleItems}
            roles={roles}
          />
        </div>
        <Button
          type="primary"
          className={styles.generateButton}
          onClick={handleGenerate}
        >
          生成会议纪要
        </Button>
      </div>
    </div>
  );
};

export default MeetingNotes;