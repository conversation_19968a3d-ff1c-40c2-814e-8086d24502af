import { MANAGER_API_BASE_URL } from '../../../Constant/ServerConstant';
import { getToken } from '../../../utils/storage';
import { WorkflowCodeBlock } from '../../../utils/WorkflowCodeBlockProcessor';

/**
 * 将HTML内容转换为PDF
 * @param codeBlocks 原始的 code_blocks 数据结构
 * @param sessionId 会话ID
 * @param format PDF格式，'PPT'或'A4'，默认为'PPT'
 * @param mode 模式，'Chat'或'Product'，默认为'Chat'
 * @returns 包含PDF URL的对象
 */

export const htmlToPdf = async (
  codeBlocks: WorkflowCodeBlock[] | string[],
  sessionId: string,
  format: 'PPT' | 'A4' = 'PPT',
  mode: 'Chat' | 'Product' = 'Chat'
): Promise<{ url: string }> => {
  try {
    // 确保codeBlocks不为空
    if (!codeBlocks || Object.keys(codeBlocks).length === 0) {
      throw new Error('代码块内容不能为空');
    }
    
    // 提取所有有效的代码块


    
    // 根据后端API的要求构建请求体
    const requestBody = {
      code_blocks: codeBlocks,  // 发送完整的原始数据结构
      format: format,
      session_id: sessionId,
      mode: mode
    };
    
    console.log('PDF请求参数:', requestBody);
    
    // 使用fetch调用后端 API 将 HTML 转换为 PDF
    const response = await fetch(`${MANAGER_API_BASE_URL}/api/v1/product/html2pdf/convert-to-pdf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(requestBody)
    });
    console.log('PDF生成请求响应:', response);
    if (!response.ok) {
      const errorText = await response.text();
      console.error('PDF生成API错误:', response.status, errorText);
      throw new Error(`服务器响应错误: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    // 获取JSON响应，包含URL
    const result = await response.json();
    console.log('PDF生成成功:', result);
    
    if (!result.url) {
      throw new Error('API返回数据中缺少URL');
    }
    
    return { 
      url: result.url
    };
  } catch (error) {
    console.error('PDF 生成错误:', error);
    throw new Error('PDF 生成失败');
  }
};

/**
 * 兼容性函数：支持字符串数组输入（用于通用HTML导出场景）
 * @param htmlContents HTML内容数组
 * @param sessionId 会话ID
 * @param format PDF格式
 * @param mode 模式
 * @returns 包含PDF URL的对象
 */
export const htmlToPdfLegacy = async (
  htmlContents: WorkflowCodeBlock[] | string[],
  sessionId: string,
  format: 'PPT' | 'A4' = 'PPT',
  mode: 'Chat' | 'Product' = 'Chat'
): Promise<{ url: string }> => {
  try {
    // 确保htmlContents不为空
    if (!htmlContents || htmlContents.length === 0) {
      throw new Error('HTML内容不能为空');
    }
    
    // 将字符串数组转换为代码块格式
    console.log(htmlContents,'htmlContentshtmlToPdf');
 
    
    // 调用主函数
    console.log(htmlContents,'codeBlockshtmlToPdf');
    return await htmlToPdf(htmlContents, sessionId, format, mode);
  } catch (error) {
    console.error('PDF 生成错误 (Legacy):', error);
    throw new Error('PDF 生成失败');
  }
};

/**
 * 将PDF转换为视频
 * @param pdfUrl PDF的URL
 * @param transcriptions 转录内容数组
 * @param sessionId 会话ID
 * @returns 视频路径
 */
export const pdfToVideo = async (
  pdfUrl: string, 
  transcriptions: Array<{ title: string; content: string }>,
  sessionId: string
): Promise<string> => {
  console.log('pdfToVideo called with sessionId:', sessionId,pdfUrl);
  try {
    // 获取token
    const token = getToken();
    
    // 构建请求体 - 使用pdf_url作为字段名，与后端API匹配
    const requestBody = {
      pdf_url: pdfUrl,
      transcriptions,
      session_id: sessionId
    };
    
    console.log('视频生成请求参数:', requestBody);
    
    // 调用后端API
    const response = await fetch(`${MANAGER_API_BASE_URL}/api/v1/product/pdf2video/convert`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('视频生成API错误:', response.status, errorText);
      throw new Error(`服务器响应错误: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    // 获取响应数据
    const data = await response.json();
    console.log('视频生成成功:', data);
    
    // 视频返回的是链接
    if (data.video_path) {
      return data.video_path; // 直接返回视频路径
    } else {
      throw new Error('视频生成失败，未返回有效路径');
    }
  } catch (error) {
    console.error('视频生成错误:', error);
    throw new Error('视频生成失败');
  }
};

/**
 * 从文本中提取JSON格式的转录内容
 * @param text 包含JSON的文本
 * @returns 转录内容数组
 */
export const extractJsonFromText = (text: string): Array<{ title: string; content: string }> => {
  console.log('开始提取转录内容, 文本长度:', text.length);
  
  try {
    // 尝试几种不同的提取策略
    
    // 策略1: 寻找含有title和content字段的标准JSON对象
    const standardPattern = /\{\s*"title"\s*:\s*"([^"]*)"\s*,\s*"content"\s*:\s*"([^"]*)"\s*\}/g;
    const standardMatches = [...text.matchAll(standardPattern)];
    
    if (standardMatches.length > 0) {
      console.log('找到标准格式JSON对象:', standardMatches.length);
      return standardMatches.map(match => ({
        title: match[1],
        content: match[2]
      }));
    }
    
    // 策略2: 寻找可能跨越多行的JSON对象
    const complexPattern = /\{\s*[\r\n\s]*"title"[\s\S]*?"content"[\s\S]*?\}/g;
    const complexMatches = text.match(complexPattern);
    
    if (complexMatches && complexMatches.length > 0) {
      console.log('找到复杂格式JSON对象:', complexMatches.length);
      const results: Array<{ title: string; content: string }> = [];
      
      for (const match of complexMatches) {
        try {
          const parsed = JSON.parse(match);
          if (parsed && typeof parsed === 'object' && 'title' in parsed && 'content' in parsed) {
            results.push(parsed);
          }
        } catch (e) {
          console.log('无法解析JSON:', match.substring(0, 50) + '...');
        }
      }
      
      if (results.length > 0) {
        return results;
      }
    }
    
    // 策略3: 寻找Markdown代码块中的JSON
    const codeBlockPattern = /```(?:json)?\s*([\s\S]*?)```/g;
    const codeBlockMatches = [...text.matchAll(codeBlockPattern)];
    
    if (codeBlockMatches.length > 0) {
      console.log('找到代码块:', codeBlockMatches.length);
      const results: Array<{ title: string; content: string }> = [];
      
      for (const match of codeBlockMatches) {
        try {
          const jsonContent = match[1].trim();
          const parsed = JSON.parse(jsonContent);
          
          if (parsed && typeof parsed === 'object') {
            // 单个对象
            if ('title' in parsed && 'content' in parsed) {
              results.push(parsed);
            } 
            // 可能是数组
            else if (Array.isArray(parsed) && parsed.length > 0 && 'title' in parsed[0] && 'content' in parsed[0]) {
              results.push(...parsed);
            }
          }
        } catch (e) {
          console.log('无法解析代码块中的JSON');
        }
      }
      
      if (results.length > 0) {
        return results;
      }
    }
    
    // 策略4: 尝试提取整个文本作为JSON
    try {
      // 检查文本是否包含类似JSON的结构
      if (text.includes('"title"') && text.includes('"content"')) {
        // 寻找文本中最长的可能是JSON的部分
        let startIdx = text.indexOf('{');
        let endIdx = text.lastIndexOf('}');
        
        if (startIdx !== -1 && endIdx !== -1 && endIdx > startIdx) {
          const possibleJson = text.substring(startIdx, endIdx + 1);
          console.log('尝试解析可能的JSON:', possibleJson.substring(0, 50) + '...');
          
          try {
            const parsed = JSON.parse(possibleJson);
            
            if (parsed && typeof parsed === 'object') {
              // 单个对象
              if ('title' in parsed && 'content' in parsed) {
                return [parsed];
              } 
              // 可能是数组
              else if (Array.isArray(parsed) && parsed.length > 0 && 'title' in parsed[0] && 'content' in parsed[0]) {
                return parsed;
              }
            }
          } catch (e) {
            console.log('无法解析完整文本为JSON');
          }
        }
      }
    } catch (e) {
      console.log('策略4失败');
    }
    
    // 如果所有策略都失败，尝试创建一个默认的转录内容
    console.log('所有提取策略都失败，创建默认转录内容');
    
    // 从文本中提取段落，创建转录内容
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 20);
    if (paragraphs.length > 0) {
      // 找到文本中看起来像标题的行
      const titleLinePattern = /^#+\s+(.+)$|^(.{5,50})\s*\n[-=]+$/gm;
      const titleMatches = [...text.matchAll(titleLinePattern)];
      
      if (titleMatches.length > 0) {
        // 使用找到的标题
        return titleMatches.map((match, index) => {
          const title = match[1] || match[2];
          // 尝试找到与标题相关的内容段落
          let content = '';
          const startPos = match.index || 0;
          const nextTitlePos = index < titleMatches.length - 1 ? titleMatches[index + 1].index : text.length;
          
          if (startPos !== undefined && nextTitlePos !== undefined) {
            content = text.substring(startPos + match[0].length, nextTitlePos).trim();
          }
          
          return {
            title,
            content: content || paragraphs[Math.min(index, paragraphs.length - 1)]
          };
        });
      } else {
        // 如果没有找到标题，使用第一个段落作为标题，其余作为内容
        return [{
          title: paragraphs[0].substring(0, 50),
          content: paragraphs.slice(1).join('\n\n') || paragraphs[0]
        }];
      }
    }
    
    // 实在没有找到任何有用的转录内容，返回一个默认的
    return [{
      title: "自动生成的课程",
      content: text.trim() || "请编辑此内容以创建视频讲解"
    }];
    
  } catch (error) {
    console.error('JSON提取过程出错:', error);
    // 返回一个默认值
    return [{
      title: "提取错误",
      content: "无法从生成内容中提取转录文本，请手动编辑。"
    }];
  }
};
