.editorContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.toolbar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 2px;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
  gap: 10px; /* Add gap between wrapped items */
}

.pageControls {
  display: flex;
  align-items: center;
  margin: 0 16px;
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  background-color: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.editorContent {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.previewPanel {
  flex: 4; /* 增加比例，给预览区域更多空间 */
  height: 100%;
  border-right: 1px solid #e8e8e8;
  overflow: auto;
  display: flex;
  position: relative;
  padding-bottom: 20px; /* Add padding at the bottom to ensure content is visible */
}

.propertiesPanel {
  width: 300px;
  min-width: 280px;
  max-width: 300px;
  height: 100%;
  overflow-y: auto;
  background-color: #fff;
  border-left: 1px solid #e8e8e8;
  padding: 16px;
}

.propertySection {
  margin-bottom: 16px;
}

.editorFrame {
  flex: 1;
  width: 100%;
  height: 100%;
  border: none;
  min-height: 650px; /* Increased min-height */
  margin-bottom: 30px; /* Add margin at the bottom to ensure content is visible */
  overflow-x: visible;
}

.previewFrame {
  width: 100%;
  height: 100%;
  border: none;
}

.previewContainer {
  width: 100%;
  height: 70vh;
  overflow: auto;
  border: 1px solid #f0f0f0;
  border-radius: 2px;
}

.colorPicker {
  display: flex;
  align-items: center;
}

.elementPath {
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

.sourceEditor {
  width: 100%;
  height: 100%;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 8px;
  resize: none;
}

.viewModeToggle {
  margin-bottom: 16px;
}

.loadingContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
}