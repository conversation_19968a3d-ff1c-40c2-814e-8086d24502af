/* TranscriptionEditor.module.css */

.transcriptionEditorContainer {
  display: flex;
  height: 100%;
  gap: 20px;
  overflow: hidden;
}

.transcriptionEditingPanel {
  flex: 0.45; /* Reduce the width of the editing panel */
  overflow-y: auto;
  padding-right: 10px;
  border-right: 1px solid #f0f0f0;
  max-height: 100%;
  display: flex;
  flex-direction: column;
}

.transcriptionList {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
  padding-bottom: 10px;
}

.transcriptionEditItem {
  margin-bottom: 8px;
  background-color: #fafafa;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
}

.transcriptionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
}

.transcriptionNumber {
  font-weight: bold;
  color: #1890ff;
}

.transcriptionForm {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.transcriptionField {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.titleDisplay {
  font-weight: bold;
  font-size: 16px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #333;
}

.autoResizeTextArea {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  width: 100%;
  transition: all 0.3s;
  resize: none;
  overflow: auto;
  line-height: 1.5;
  font-size: 14px;
}

.autoResizeTextArea:hover, .autoResizeTextArea:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.previewPanel {
  flex: 0.55; /* Increase the width of the preview panel */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.previewHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.previewToggleButton {
  font-size: 13px;
  padding: 0 8px;
  height: 28px;
  border-radius: 4px;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.previewToggleButton:hover {
  background-color: #e6f7ff;
}

.previewWrapper {
  flex: 1;
  position: relative;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px; /* Ensure minimum height for preview */
  max-height: calc(90vh - 220px); /* Limit maximum height */
}

.videoPlayer {
  width: 100%;
  height: auto;
  max-height: 100%;
  object-fit: contain; /* Ensure video maintains aspect ratio */
}

.pdfPreview {
  width: 100%;
  height: 100%;
  border: none;
}

.noPreviewAvailable {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  background-color: #f5f5f5;
  padding: 20px;
  text-align: center;
}

.previewNote {
  margin-top: 12px;
  padding: 8px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.regeneratingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

/* Responsive styles */
@media (max-width: 768px) {
  .transcriptionEditorContainer {
    flex-direction: column;
  }

  .transcriptionEditingPanel,
  .previewPanel {
    flex: none;
    width: 100%;
  }

  .transcriptionEditingPanel {
    height: 50%;
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
    padding-right: 0;
    padding-bottom: 10px;
  }

  .previewPanel {
    height: 50%;
    margin-top: 10px;
  }
}
