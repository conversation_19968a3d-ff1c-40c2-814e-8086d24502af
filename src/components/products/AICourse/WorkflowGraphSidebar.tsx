import React, { useState, useEffect, useCallback } from 'react';
import { List, Pagination, Spin, Empty, Button, Typography, message, Tooltip, Popconfirm } from 'antd';
import { MenuFoldOutlined, MenuUnfoldOutlined, AppstoreOutlined, RightOutlined, DeleteOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';
import styles from './WorkflowGraphSidebar.module.css';
import { fetchBulk, deleteData } from '../../api/api';
import { WORKFLOW_GRAPH_ENDPOINT } from '../../../Constant/RouterConstant';
const { Title, Text } = Typography;


interface WorkflowGraphItem {
  id: string;
  name: string;
  workflow_id: string;
  updated_at: string;
  session_id: string;
  query: string;
}

interface WorkflowGraphSidebarProps {
  onSelectWorkflowGraph?: (sessionId: string) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
  workflowId?: string;
}

const WorkflowGraphSidebar: React.FC<WorkflowGraphSidebarProps> = ({
  onSelectWorkflowGraph,
  collapsed,
  onToggleCollapse,
  workflowId
}) => {
  const [workflowGraphs, setWorkflowGraphs] = useState<WorkflowGraphItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const pageSize = 10;

  // 从Redux获取用户信息
  const { userInfo } = useSelector((state: any) => state.user);
  const username = userInfo?.username || '';

  // 排序设置
  const sortField = 'updated_at';
  const sortOrder = 'desc';

  // 获取工作流列表
  const fetchWorkflowGraphs = async () => {
    // 如果侧边栏折叠或用户未登录，不获取数据
    if (collapsed) {
      console.log('侧边栏已折叠，跳过获取工作流数据');
      return;
    }

    if (!username) {
      message.error('用户未登录，无法获取工作流列表');
      return;
    }

    setLoading(true);
    try {
      const skip = (currentPage - 1) * pageSize;
      const limit = pageSize;
      let endpoint = `${WORKFLOW_GRAPH_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${username}`;
      
      // 如果存在workflowId，则添加到查询参数中
      if (workflowId) {
        endpoint += `&workflow_id=${workflowId}`;
      }

      console.log('获取工作流数据，页码:', currentPage);
      const result = await fetchBulk(endpoint);
      setWorkflowGraphs(result.data || []);
      console.log(result.data,'result.data');
      setTotal(result.count || 0);
      console.log('工作流数据获取成功:', result.data?.length || 0, '条记录');
    } catch (error) {
      console.error('获取工作流数据失败:', error);
      message.error('获取工作流列表失败，请稍后再试');
      setWorkflowGraphs([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 删除工作流
  const handleDeleteWorkflowGraph = async (id: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发项目点击

    try {
      setLoading(true);
      await deleteData(`${WORKFLOW_GRAPH_ENDPOINT}/${id}`);
      message.success('工作流已删除');

      // 刷新工作流列表
      fetchWorkflowGraphs();
    } catch (error) {
      console.error('删除工作流失败:', error);
      message.error('删除工作流失败');
    } finally {
      setLoading(false);
    }
  };

  // 页码变化处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 选择工作流
  const handleSelectWorkflowGraph = (workflow: WorkflowGraphItem) => {
    if (onSelectWorkflowGraph) {
      // 传递session_id而不是workflow_id
      onSelectWorkflowGraph(workflow.session_id);
      // message.success(`已选择课程: ${workflow.name || '未命名课程'}`);
    }
  };

  // 使用useCallback包装fetchWorkflowGraphs，避免无限循环
  const memoizedFetchWorkflowGraphs = useCallback(fetchWorkflowGraphs, [
    collapsed, username, currentPage, pageSize, sortField, sortOrder, workflowId
  ]);

  // 监听页码变化，重新获取数据
  useEffect(() => {
    memoizedFetchWorkflowGraphs();
  }, [memoizedFetchWorkflowGraphs]);

  // 组件挂载时获取数据
  useEffect(() => {
    if (!collapsed) {
      console.log('组件挂载或侧边栏展开，自动获取最新工作流数据');
      memoizedFetchWorkflowGraphs();
    }
  }, [collapsed, memoizedFetchWorkflowGraphs]);

  // 如果侧边栏折叠，只显示折叠按钮
  if (collapsed) {
    return (
      <div className={styles.collapsedSidebar}>
        <Button
          type="text"
          icon={<MenuUnfoldOutlined />}
          onClick={onToggleCollapse}
          className={styles.collapseButton}
        />
      </div>
    );
  }

  return (
    <div className={styles.sidebar}>
      <div className={styles.sidebarHeader}>
        <Title level={5} style={{ margin: 0 }}>
          <AppstoreOutlined style={{ marginRight: 8 }} />
          AI
        </Title>
        <Button
          type="text"
          icon={<MenuFoldOutlined />}
          onClick={onToggleCollapse}
          className={styles.collapseButton}
        />
      </div>

      <div className={styles.sidebarContent}>
        <Spin spinning={loading}>
          {workflowGraphs.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="没有找到工作流"
            />
          ) : (
            <List
              dataSource={workflowGraphs}
              renderItem={(item) => (
                <List.Item
                  className={styles.workflowItem}
                  onClick={() => handleSelectWorkflowGraph(item)}
                >
                  <div className={styles.workflowInfo}>
                    <Tooltip title={item.query || '未命名工作流'}>
                      <Text ellipsis style={{ maxWidth: '80%' }}>
                        {item.query || '未命名工作流'}
                      </Text>
                    </Tooltip>
                    <Text type="secondary" className={styles.workflowDate}>
                      {new Date(item.updated_at).toLocaleDateString()}
                    </Text>
                  </div>
                  <div className={styles.workflowActions}>
                    <Popconfirm
                      title="确定要删除这个工作流吗？"
                      description="删除后将无法恢复！"
                      onConfirm={(e) => handleDeleteWorkflowGraph(item.id, e as React.MouseEvent)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        type="text"
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        className={styles.deleteButton}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </Popconfirm>
                    <RightOutlined className={styles.workflowArrow} />
                  </div>
                </List.Item>
              )}
            />
          )}
        </Spin>
      </div>

      {total > pageSize && (
        <div className={styles.paginationContainer}>
          <Pagination
            current={currentPage}
            total={total}
            pageSize={pageSize}
            size="small"
            onChange={handlePageChange}
            showSizeChanger={false}
            simple
          />
        </div>
      )}
    </div>
  );
};

export default WorkflowGraphSidebar;