.appContainer {
  display: flex;
  height: calc(100vh - 64px);
}

.mainContainer {
  flex: 1;
  display: flex;
  height: 100%;
  overflow: hidden;
}

.leftSide {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  border-right: 1px solid #e8e8e8;
  overflow: hidden;
}

.contentContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.rightSide {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.resourcePanel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.inputAreaContainer {
  border-bottom: 1px solid #e8e8e8;
  background-color: #f8f8f8;
}

.inputAreaHeader {
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.inputAreaHeader:hover {
  background-color: #f0f0f0;
}

.inputArea {
  padding: 12px 20px;
}

.inputTextArea {
  border-radius: 8px !important;
  resize: none;
  font-size: 14px;
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
}

.actionButtonContainer {
  display: flex;
  justify-content: flex-end;
  margin: 0 20px 16px;
  gap: 12px;
}

.actionButton {
  border-radius: 6px;
}

.responseContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  overflow-y: auto;
}

.markdownContainer {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #ffffff;
}

.toolbarContainer {
  display: flex;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #f5f5f5;
}

/* 自定义Steps组件样式 */
.toolbarContainer :global(.ant-steps-item-description) {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 2px;
}

.toolbarContainer :global(.ant-steps-item-title) {
  font-size: 14px;
  font-weight: 500;
}

.toolbarContainer :global(.ant-steps-item-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.videoGenerationContainer {
  padding: 20px;
  background-color: #ffffff;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.transcriptionBlock {
  margin-top: 8px;
  background-color: #f0f0f0;
  padding: 16px;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.processBlock {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 8px;
}

.statusIcon {
  font-size: 16px;
  margin-right: 8px;
}

.successIcon {
  color: #52c41a;
}

.loadingIcon {
  color: #1890ff;
}

.errorIcon {
  color: #f5222d;
}

.videoPreviewContainer {
  margin-top: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
}

.videoPlayer {
  width: 100%;
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pdfPreviewContainer {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
}

.downloadButtonContainer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.downloadButton {
  border-radius: 6px;
}

.headerSettings {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Video preview container for AICourseGenerate */

.editTranscriptionButton {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.videoPreviewContainer {
  position: relative;
  margin-top: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .mainContainer {
    flex-direction: column;
  }

  .leftSide, .rightSide {
    flex: none;
    height: 50%;
    width: 100%;
  }

  .leftSide {
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
  }

  .videoGenerationContainer {
    max-height: none;
  }
}