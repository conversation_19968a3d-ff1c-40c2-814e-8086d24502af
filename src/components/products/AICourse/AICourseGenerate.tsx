import React, { useState, useRef, useEffect } from 'react';
import { Button, Typography, Input, message, Steps, Tag, Space } from 'antd';
import {
  SendOutlined,
  ReloadOutlined,
  DownloadOutlined,
  VideoCameraOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UpOutlined,
  DownOutlined,
  EditOutlined
} from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import { useParams, useNavigate } from 'react-router-dom';
import createOpenAIClient from '../../../utils/openaiClient';
import MarkdownRenderer from '../../Chat/MarkdownRenderer';
import HeaderSettings from '../../common/HeaderSettings';
import WorkflowGraphSidebar from './WorkflowGraphSidebar';
import TranscriptionEditor from './TranscriptionEditor';
import PdfHtmlEditor from './PdfHtmlEditor';
import styles from './AICourse.module.css';
import { WORKFLOW_GRAPH_ENDPOINT } from '../../../Constant/RouterConstant';
import { updateData } from '../../api/api';
import { htmlToPdf, pdfToVideo, extractJsonFromText } from './AICourseUtils';
import WorkflowContentProcessor from '../../../utils/WorkflowContentProcessor';
import WorkflowCodeBlockProcessor, { WorkflowCodeBlock,WorkflowGraphData } from '../../../utils/WorkflowCodeBlockProcessor';

/**
 * 数据获取优先级说明：
 * 1. 优先从 WorkflowGraph.code_blocks 中获取HTML代码块（数据库中的最新数据）
 * 2. 如果没有，则从 Markdown 内容中提取HTML代码块
 * 3. 生成PDF时，使用获取到的HTML代码块数据
 * 
 * 这种方式确保：
 * - 使用最新且准确的代码块数据
 * - 支持历史数据的重新渲染
 * - 保持向后兼容性
 */

const { TextArea } = Input;
const { Title, Text } = Typography;
const { Step } = Steps;
// const { Panel } = Collapse;

// 固定的工作流ID
const WORKFLOW_ID = '4690fe9f-f313-e673-685e-fe072a315f17';

interface AICourseGenerateProps {
  initialText?: string;
}

interface ProductUrl {
  type: string;  // 'pdf' 或 'video'
  url: string;   // 资源URL
}

const AICourseGenerate: React.FC<AICourseGenerateProps> = ({ initialText = '' }) => {
  // 获取路由参数和导航函数
  const { session_id } = useParams<{ session_id?: string }>();
  const navigate = useNavigate();

  // 状态管理
  const [inputText, setInputText] = useState<string>(initialText);
  const [streamContent, setStreamContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [pdfGenerating, setPdfGenerating] = useState<boolean>(false);
  const [videoGenerating, setVideoGenerating] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [htmlBlocks, setHtmlBlocks] = useState<WorkflowCodeBlock[]>([]);
  const [videoTranscriptions, setVideoTranscriptions] = useState<Array<{ title: string; content: string }>>([]);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [isStreamingComplete, setIsStreamingComplete] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  
  // 多个 WorkflowGraph 记录支持
  // const [workflowGraphList, setWorkflowGraphList] = useState<WorkflowGraphData[]>([]);
  // const [activeTabKey, setActiveTabKey] = useState<string>('0');
  
  const [selectedWorkflowGraphData, setSelectedWorkflowGraphData] = useState<WorkflowGraphData | null>(null);
  const [selectedWorkflowSessionId, setSelectedWorkflowSessionId] = useState<string>('');
  const [videoStatus, setVideoStatus] = useState<string>('');

  // 侧边栏折叠状态
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(true);
  // 输入区域折叠状态
  const [inputCollapsed, setInputCollapsed] = useState<boolean>(false);
  // 转录编辑器状态
  const [transcriptionEditorVisible, setTranscriptionEditorVisible] = useState<boolean>(false);
  // 视频重新生成状态
  const [videoRegenerating, setVideoRegenerating] = useState<boolean>(false);

  // PDF编辑状态
  const [pdfEditorVisible, setPdfEditorVisible] = useState<boolean>(false);
  const [selectedHtmlBlockId, setSelectedHtmlBlockId] = useState<string>('');

  // 使用URL中的session_id或生成新的
  const sessionId = useRef<string>(session_id || uuidv4());
  const markdownContainerRef = useRef<HTMLDivElement>(null);
  // 使用ref跟踪PDF生成状态，避免重复请求
  const isPdfGeneratingRef = useRef<boolean>(false);


  // 监听URL中的session_id参数变化
  useEffect(() => {
    if (session_id) {
      console.log('URL中的session_id变化:', session_id);
      // 更新sessionId.current为URL中的session_id
      sessionId.current = session_id;

      // 如果当前selectedWorkflowSessionId与URL中的session_id不同，则更新selectedWorkflowSessionId
      if (selectedWorkflowSessionId !== session_id) {
        console.log('URL中的session_id与当前选中的不同，更新selectedWorkflowSessionId');
        setSelectedWorkflowSessionId(session_id);
      }
      // 注意：不在这里直接调用fetchWorkflowGraphData，让selectedWorkflowSessionId的useEffect处理
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session_id]);

  // 检测直接传入的JSON数据
  useEffect(() => {
    // 检查initialText是否是JSON字符串
    if (initialText && initialText.trim().startsWith('{') && initialText.trim().endsWith('}')) {
      try {
        const jsonData = JSON.parse(initialText);

        // 如果包含htmlContents字段，说明是直接传入的HTML内容
        if (jsonData.htmlContents && Array.isArray(jsonData.htmlContents) && jsonData.htmlContents.length > 0) {
          console.log('检测到直接传入的HTML内容:', jsonData.htmlContents.length, '个页面');

          // 如果传入了session_id，使用它
          if (jsonData.session_id) {
            // 更新sessionId.current为传入的session_id
            sessionId.current = jsonData.session_id;
            setSelectedWorkflowSessionId(jsonData.session_id);
            console.log('使用传入的session_id:', jsonData.session_id);

            // 更新URL以反映当前session_id
            navigate(`/product/ai-course/${jsonData.session_id}`, { replace: true });

            // 注意：这里不直接调用fetchWorkflowGraphData，而是在下面的useEffect中处理
            // 这样可以避免循环依赖
          }
        }
      } catch (error) {
        console.error('解析JSON数据失败:', error);
      }
    }
  }, [initialText, navigate]);

  // 当selectedWorkflowSessionId变化时，获取工作流数据
  useEffect(() => {
    if (selectedWorkflowSessionId && !pdfGenerating && !isPdfGeneratingRef.current) {
      console.log('selectedWorkflowSessionId变化，获取工作流数据:', selectedWorkflowSessionId);
      // 注意：重复点击同一个工作流时，这个useEffect不会触发，
      // 因为selectedWorkflowSessionId没有变化，所以在handleSelectWorkflowGraph中处理了重复点击的情况
      fetchWorkflowGraphData(selectedWorkflowSessionId);
    } else if (selectedWorkflowSessionId && (pdfGenerating || isPdfGeneratingRef.current)) {
      console.log('selectedWorkflowSessionId变化，但PDF正在生成中，跳过获取工作流数据');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedWorkflowSessionId]);

  // 处理用户输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setInputText(newValue);

    // 如果之前选择了历史数据（selectedWorkflowSessionId不为空），
    // 且用户开始输入新内容，则重置session_id为新的
    if (selectedWorkflowSessionId && newValue.trim() !== '') {
      // 生成新的session_id
      const newSessionId = uuidv4();
      sessionId.current = newSessionId;
      console.log('用户输入新内容，重置session_id为新值:', newSessionId);

      // 清除选中的工作流session_id
      setSelectedWorkflowSessionId('');

      // 更新URL，移除session_id参数
      navigate('/product/ai-course', { replace: true });
    }
  };

  // 切换侧边栏折叠状态
  const toggleSidebar = () => {
    // 切换侧边栏状态
    const newCollapsedState = !sidebarCollapsed;
    setSidebarCollapsed(newCollapsedState);

    // 如果是展开侧边栏，则获取最新的工作流数据
    if (!newCollapsedState) {
      console.log('侧边栏展开，获取最新工作流数据');
      // 这里不需要做任何操作，因为WorkflowGraphSidebar组件内部会在挂载和展示时自动获取数据
    }
  };

  // 切换输入区域折叠状态
  const toggleInputCollapse = () => {
    setInputCollapsed(!inputCollapsed);
  };

  // 获取多个工作流数据记录
  const fetchWorkflowGraphData = async (workflowSessionId: string) => {
    try {
      console.log('开始获取工作流数据记录，session_id:', workflowSessionId);
      setLoading(true);

      // 重置状态
      setPdfUrl('');
      setVideoUrl('');
      setHtmlBlocks([]);
      setVideoTranscriptions([]);

      // 如果已经在生成PDF，则不重复获取
      if (pdfGenerating || isPdfGeneratingRef.current) {
        console.log('PDF正在生成中，跳过工作流数据获取');
        return;
      }

      // 使用新的方法获取多条记录
      const workflowGraphList = await WorkflowCodeBlockProcessor.getWorkflowGraphsBySessionId(workflowSessionId);
      
      if (workflowGraphList.length === 0) {
        console.log('未找到工作流数据记录');
        // setWorkflowGraphList([]);
        return;
      }

      // console.log(`获取到${workflowGraphList.length}条WorkflowGraph记录`);
      // setWorkflowGraphList(workflowGraphList);
      
      // 设置第一个记录为活跃tab
      // setActiveTabKey('0');
      
      // 处理第一个记录的数据
      const firstWorkflow = workflowGraphList[0];
      setSelectedWorkflowGraphData(firstWorkflow);
      
      // 处理产品URL
      let foundPdfUrl = '';
      let foundVideoUrl = '';
      let currentStepValue = 0;

      if (firstWorkflow.product_urls && Array.isArray(firstWorkflow.product_urls)) {
        firstWorkflow.product_urls.forEach((item: ProductUrl) => {
          if (item.type === 'pdf' && item.url) {
            foundPdfUrl = item.url;
            if (currentStepValue < 1) currentStepValue = 1;
          }
          if (item.type === 'video' && item.url) {
            foundVideoUrl = item.url;
            if (currentStepValue < 3) currentStepValue = 3;
          }
        });
      }

      // 设置URL和状态
      if (foundPdfUrl) setPdfUrl(foundPdfUrl);
      if (foundVideoUrl) setVideoUrl(foundVideoUrl);
      setCurrentStep(currentStepValue);

      // 处理转录数据
      if (firstWorkflow.transcriptions && Array.isArray(firstWorkflow.transcriptions)) {
        setVideoTranscriptions(firstWorkflow.transcriptions);
      }

      // 处理视频状态
      if (firstWorkflow.video_status) {
        setVideoStatus(firstWorkflow.video_status);
      }

      // 生成 Markdown 内容（基于第一个记录）
      const markdownContent = WorkflowContentProcessor.generateMarkdownFromGraph(firstWorkflow.graph);
      setStreamContent(markdownContent);
      setIsStreamingComplete(true);

      // 设置当前记录的HTML块
      console.log(firstWorkflow,'firstWorkflow.graph');
      const currentHtmlBlocks:WorkflowCodeBlock[] = firstWorkflow?.code_blocks;
      console.log(currentHtmlBlocks,'currentHtmlBlocks');
      setHtmlBlocks(currentHtmlBlocks);

    } catch (error) {
      console.error('获取工作流数据失败:', error);
      message.error('获取工作流数据失败');
      setHasError(true);
      setErrorMessage(error instanceof Error ? error.message : '获取工作流数据时发生未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 选择工作流
  const handleSelectWorkflowGraph = (workflowSessionId: string) => {
    // 检查是否重复点击同一个工作流
    const isReselecting = selectedWorkflowSessionId === workflowSessionId;
    console.log('选择工作流:', workflowSessionId, isReselecting ? '(重复选择)' : '(新选择)');

    // 强制彻底重置状态，确保不会使用旧数据
    console.log('重置所有状态，确保获取新数据');
    setStreamContent('');
    setHtmlBlocks([]);
    setVideoTranscriptions([]);
    setPdfUrl('');  // 清除已有的PDF URL
    setVideoUrl('');
    setCurrentStep(0);
    setIsStreamingComplete(false);
    setHasError(false);
    setErrorMessage('');
    setVideoStatus('');

    // 不需要保留以前的selectedWorkflowGraphData
    setSelectedWorkflowGraphData(null);

    // 更新sessionId.current为选中的工作流session_id
    sessionId.current = workflowSessionId;
    console.log('选中工作流session_id，并更新sessionId.current:', workflowSessionId);

    // 保存选中的工作流session_id
    setSelectedWorkflowSessionId(workflowSessionId);

    // 更新URL以反映当前session_id
    navigate(`/product/ai-course/${workflowSessionId}`, { replace: true });

    // 无论是否重复选择，都直接调用fetchWorkflowGraphData以确保获取最新数据
    // 只有在PDF生成中才会跳过
    if (!pdfGenerating && !isPdfGeneratingRef.current) {
      console.log('直接调用fetchWorkflowGraphData获取最新数据');
      fetchWorkflowGraphData(workflowSessionId);
    } else {
      console.log('PDF正在生成中，跳过获取工作流数据');
    }
  };
  // 提取 JSON 转录内容
  const extractTranscriptions = async (nodes: Array<{ content: string; isMarkdown?: boolean }>): Promise<Array<{ title: string; content: string }>> => {
    if (nodes.length === 0) return [];

    // 只从最后一个节点提取转录内容
    const lastNode = nodes[nodes.length - 1];

    try {
      // 直接从文本内容中提取JSON
      const result = extractJsonFromText(lastNode.content);
      console.log('提取结果:', result);

      return result;
    } catch (error) {
      console.error('转录提取错误:', error);
      return [];
    }
  };

  // 处理提交
  const handleSubmit = async () => {
    if (!inputText.trim()) {
      message.warning('请输入问题');
      return;
    }

    // 自动折叠输入框
    setInputCollapsed(true);

    // 重置状态 - 这是新的请求，需要清除所有状态
    setStreamContent('');
    setHtmlBlocks([]);
    setVideoTranscriptions([]);
    setPdfUrl(''); // 清除已有的PDF URL
    setVideoUrl('');
    setLoading(true);
    setCurrentStep(0);
    setIsStreamingComplete(false);
    setHasError(false);
    setErrorMessage('');
    setVideoStatus('');

    // 确保PDF生成状态也被重置
    setPdfGenerating(false);
    isPdfGeneratingRef.current = false;

    try {
      // 构建请求内容
      // 如果用户在输入框中输入了新内容，sessionId.current应该已经被重置为新值
      // 如果用户点击了历史数据但没有输入新内容，则使用选中的session_id
      const chatContent = {
        content: inputText,
        session_id: sessionId.current,
        workflow_or_agent: 'workflow',
        source: "product"
      };

      console.log('提交请求使用的session_id:', sessionId.current);

      // 更新URL以反映当前session_id
      navigate(`/product/ai-course/${sessionId.current}`, { replace: true });

      const chatContentString = JSON.stringify(chatContent);

      // 创建 OpenAI 客户端
      const client = createOpenAIClient();

      // 显示内容生成中的消息
      message.info('内容生成中，请稍候...');

      // 发送流式请求
      const stream = await client.chat.completions.create({
        model: WORKFLOW_ID,
        messages: [{ role: 'user', content: chatContentString }],
        stream: true,
      });

      let accumulatedContent = '';

      // 接收流式返回数据
      for await (const chunk of stream) {
        const deltaContent = chunk.choices[0]?.delta?.content || '';
        if (deltaContent) {
          accumulatedContent += deltaContent;
          setStreamContent(accumulatedContent);
        }
      }

      setIsStreamingComplete(true);
      message.success('内容生成完成');

      // 提取节点内容
      const extractedNodes = WorkflowContentProcessor.extractNodes(accumulatedContent);

      // 检查是否有产品URL的标志
      let hasPdfUrlInProductUrls = false;

      // 检查workflowGraphData是否有PDF URL
      if (selectedWorkflowGraphData && selectedWorkflowGraphData.product_urls) {
        console.log('检查现有workflowGraphData中的product_urls');

        if (Array.isArray(selectedWorkflowGraphData.product_urls)) {
          selectedWorkflowGraphData.product_urls.forEach((item: ProductUrl) => {
            if (item.type === 'pdf' && item.url) {
              console.log('在现有workflowGraphData中找到PDF URL:', item.url);
              hasPdfUrlInProductUrls = true;
              setPdfUrl(item.url);
            }
          });
        }
      }

      if (extractedNodes.length > 0) {
        // 对于新生成的内容，优先尝试从 WorkflowGraph 获取代码块，否则从 Markdown 提取
        let extractedRawCodeBlocks: WorkflowCodeBlock[] | string[] = [];
        // let extractedHtmlBlocks: string[] = [];
        
        // 首先尝试从 WorkflowGraph 获取已存在的代码块
        // extractedRawCodeBlocks = await getWorkflowGraphCodeBlocks(sessionId.current);
        extractedRawCodeBlocks = await WorkflowCodeBlockProcessor.getCodeBlocksBySessionId(sessionId.current)
        const rawCodeBlockCount = Array.isArray(extractedRawCodeBlocks) ? extractedRawCodeBlocks.length : (extractedRawCodeBlocks ? Object.keys(extractedRawCodeBlocks).length : 0);
        console.log('从 WorkflowGraph 获取到代码块数量:', rawCodeBlockCount);
        // console.log('从 WorkflowGraph 获取到代码块数量:', extractedRawCodeBlocks ? Object.keys(extractedRawCodeBlocks).length : 0);

        if (extractedRawCodeBlocks) { // 确保它不是null/undefined
          setHtmlBlocks(extractedRawCodeBlocks); // 假设这是正确的，或者需要转换
        }
        // 提取 JSON 转录内容
        const extractedTranscriptions = await extractTranscriptions(extractedNodes);
        setVideoTranscriptions(extractedTranscriptions);

        // 如果有HTML内容，更新selectedWorkflowGraphData
        if (rawCodeBlockCount > 0 && selectedWorkflowGraphData) {
          // 更新本地工作流数据的 code_blocks（用于界面显示和编辑）
          setSelectedWorkflowGraphData({
            ...selectedWorkflowGraphData,
            code_blocks: extractedRawCodeBlocks
          });
          console.log('更新本地 selectedWorkflowGraphData 的 code_blocks，数量:', rawCodeBlockCount);
        }

        // 自动进行下一步：生成 PDF，但只在确认没有现有PDF URL时
        if (rawCodeBlockCount > 0 && !pdfGenerating && !isPdfGeneratingRef.current && !hasPdfUrlInProductUrls) {
          console.log('开始生成PDF');
          setCurrentStep(1);
          setPdfGenerating(true);
          // 立即设置ref，确保其他代码路径知道PDF生成已经开始
          isPdfGeneratingRef.current = true;

          // 显示PDF生成中的消息
          message.info('PDF生成中，请稍候...');

          try {
            // 使用当前会话的session_id，因为这是新生成的内容
            // 使用原始的代码块数据生成PDF
            if (extractedRawCodeBlocks) {
              const { url } = await htmlToPdf(extractedRawCodeBlocks, sessionId.current, 'PPT',"Product");
              setPdfUrl(url);
            } else {
              throw new Error('缺少代码块数据');
            }
          } finally {
            setPdfGenerating(false);
            // 重置ref
            isPdfGeneratingRef.current = false;
          }
        }
        // 如果找到了PDF但没有找到视频，并且有转录内容，设置当前步骤为2（等待生成视频）
        else if (pdfUrl && !videoUrl && extractedTranscriptions.length > 0) {
          setCurrentStep(2);
          // 不自动生成视频，等待用户点击"生成视频"按钮
          message.info('已找到PDF和转录文本，请点击"生成视频"按钮生成视频');
        }
        // 如果已经有了PDF，记录日志
        else if (pdfUrl) {
          console.log('已有PDF，不需要重新生成:', pdfUrl);
        }
        // 如果hasPdfUrlInProductUrls为true但foundPdfUrl为空的情况（可能是URL格式问题）
        else if (hasPdfUrlInProductUrls && !pdfUrl) {
          console.log('在product_urls中找到PDF记录，但URL无效，不重新生成');
        }
      }
    } catch (error) {
      console.error('请求失败:', error);
      setHasError(true);
      setErrorMessage(error instanceof Error ? error.message : '发生未知错误');
      message.error('请求处理失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 重新开始
  const handleRestart = () => {
    setInputText('');
    setStreamContent('');
    setHtmlBlocks([]);
    setVideoTranscriptions([]);
    setPdfUrl('');
    setVideoUrl('');
    setLoading(false);
    setPdfGenerating(false);
    setVideoGenerating(false);
    setCurrentStep(0);
    setIsStreamingComplete(false);
    setHasError(false);
    setErrorMessage('');
    setInputCollapsed(false);
    setSelectedWorkflowGraphData(null);
    setVideoStatus('');

    // 重置PDF生成状态ref
    isPdfGeneratingRef.current = false;

    // 清除选中的工作流session_id
    setSelectedWorkflowSessionId('');

    // 生成新的session_id
    const newSessionId = uuidv4();
    sessionId.current = newSessionId;
    console.log('重新开始，重置session_id为新值:', newSessionId);

    // 更新URL，移除session_id参数
    navigate('/product/ai-course', { replace: true });
  };

  // 下载 PDF
  const handleDownloadPdf = () => {
    if (pdfUrl) {
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = `course-${sessionId.current.substring(0, 8)}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // 下载视频
  const handleDownloadVideo = () => {
    if (videoUrl) {
      // 直接从视频URL下载
      window.open(videoUrl, '_blank');
    }
  };

  // 直接生成视频
  const handleGenerateVideo = async () => {
    if (!pdfUrl) {
      message.error('无法生成视频：PDF文件不存在');
      return;
    }

    if (videoTranscriptions.length === 0) {
      message.error('无法生成视频：转录内容不存在');
      return;
    }

    try {
      setVideoRegenerating(true);
      setVideoStatus('生成中');
      // 设置当前步骤为生成视频（步骤2）
      setCurrentStep(2);
      console.log('视频生成开始，设置状态为生成中');

      // 显示视频生成中的消息
      message.info('视频生成中，请稍候...');

      // 使用当前会话ID或选中的工作流会话ID
      const currentSessionId = selectedWorkflowSessionId || sessionId.current;

      // 更新selectedWorkflowGraphData中的video_status字段
      if (selectedWorkflowGraphData) {
        const updatedData = { ...selectedWorkflowGraphData };
        updatedData.video_status = '生成中';
        setSelectedWorkflowGraphData(updatedData);
      }

      // 调用pdfToVideo函数生成视频
      const videoPath = await pdfToVideo(pdfUrl, videoTranscriptions, currentSessionId);

      // 更新视频URL
      setVideoUrl(videoPath);

      // 设置视频状态为已完成
      setVideoStatus('已完成');

      // 设置当前步骤为完成（步骤3）
      setCurrentStep(3);

      // 更新selectedWorkflowGraphData中的视频URL和状态
      if (selectedWorkflowGraphData) {
        const updatedData = { ...selectedWorkflowGraphData };

        // 更新 product_urls
        if (!updatedData.product_urls) {
          updatedData.product_urls = [];
        }

        // 检查是否已存在视频URL
        const videoUrlIndex = updatedData.product_urls.findIndex((item: ProductUrl) => item.type === 'video');
        if (videoUrlIndex >= 0) {
          updatedData.product_urls[videoUrlIndex].url = videoPath;
        } else {
          updatedData.product_urls.push({ type: 'video', url: videoPath });
        }

        // 更新 video_status 字段为已完成
        updatedData.video_status = '已完成';

        setSelectedWorkflowGraphData(updatedData);
      }

      message.success('视频生成成功');
    } catch (error) {
      console.error('视频生成失败:', error);
      message.error('视频生成失败');
      setVideoStatus('');
    } finally {
      setVideoRegenerating(false);
    }
  };

  // 打开转录编辑器
  const openTranscriptionEditor = async () => {
    // 首先检查是否有来自 WorkflowGraph 的 transcriptions 数据
    if (selectedWorkflowGraphData && selectedWorkflowGraphData.transcriptions &&
        Array.isArray(selectedWorkflowGraphData.transcriptions) &&
        selectedWorkflowGraphData.transcriptions.length > 0) {

      console.log('使用 WorkflowGraph 中的 transcriptions 数据:', selectedWorkflowGraphData.transcriptions);
      setVideoTranscriptions(selectedWorkflowGraphData.transcriptions);

    } else if (streamContent) {
      // 如果没有 WorkflowGraph 中的数据，但有节点内容，则尝试提取转录数据
      try {
        console.log('打开转录编辑器前重新提取转录内容');
        const extractedNodes = WorkflowContentProcessor.extractNodes(streamContent);

        if (extractedNodes.length > 0) {
          // 重新提取转录内容
          const freshTranscriptions = await extractTranscriptions(extractedNodes);
          console.log('重新提取的转录内容:', freshTranscriptions);

          // 如果成功提取到转录内容，则更新状态
          if (freshTranscriptions && freshTranscriptions.length > 0) {
            setVideoTranscriptions(freshTranscriptions);

            // 如果有 selectedWorkflowGraphData，也更新其中的 transcriptions 字段
            if (selectedWorkflowGraphData) {
              const updatedData = { ...selectedWorkflowGraphData };
              updatedData.transcriptions = freshTranscriptions;
              setSelectedWorkflowGraphData(updatedData);
              console.log('更新 WorkflowGraph 中的 transcriptions 字段:', freshTranscriptions);
            }

            message.success('已更新转录内容');
          } else if (videoTranscriptions.length === 0) {
            // 如果没有提取到内容，且当前没有转录内容，则创建一个默认的
            const defaultTranscriptions = [{
              title: "默认课程标题",
              content: "请在此处编辑课程内容，点击'保存并重新生成视频'按钮更新视频。"
            }];
            setVideoTranscriptions(defaultTranscriptions);

            // 如果有 selectedWorkflowGraphData，也更新其中的 transcriptions 字段
            if (selectedWorkflowGraphData) {
              const updatedData = { ...selectedWorkflowGraphData };
              updatedData.transcriptions = defaultTranscriptions;
              setSelectedWorkflowGraphData(updatedData);
            }
          }
        }
      } catch (error) {
        console.error('重新提取转录内容失败:', error);
        // 如果当前没有转录内容，则创建一个默认的
        if (videoTranscriptions.length === 0) {
          const defaultTranscriptions = [{
            title: "默认课程标题",
            content: "请在此处编辑课程内容，点击'保存并重新生成视频'按钮更新视频。"
          }];
          setVideoTranscriptions(defaultTranscriptions);

          // 如果有 selectedWorkflowGraphData，也更新其中的 transcriptions 字段
          if (selectedWorkflowGraphData) {
            const updatedData = { ...selectedWorkflowGraphData };
            updatedData.transcriptions = defaultTranscriptions;
            setSelectedWorkflowGraphData(updatedData);
          }
        }
      }
    } else if (videoTranscriptions.length === 0) {
      // 如果没有流内容且当前没有转录内容，则创建一个默认的
      const defaultTranscriptions = [{
        title: "默认课程标题",
        content: "请在此处编辑课程内容，点击'保存并重新生成视频'按钮更新视频。"
      }];
      setVideoTranscriptions(defaultTranscriptions);

      // 如果有 selectedWorkflowGraphData，也更新其中的 transcriptions 字段
      if (selectedWorkflowGraphData) {
        const updatedData = { ...selectedWorkflowGraphData };
        updatedData.transcriptions = defaultTranscriptions;
        setSelectedWorkflowGraphData(updatedData);
      }
    }

    // 打开编辑器
    setTranscriptionEditorVisible(true);
  };

  // 关闭转录编辑器
  const closeTranscriptionEditor = () => {
    setTranscriptionEditorVisible(false);

    // 如果正在生成PDF，则不获取工作流数据
    if (pdfGenerating || isPdfGeneratingRef.current) {
      console.log('PDF正在生成中，关闭编辑器后不获取工作流数据');
      return;
    }

    // 获取当前会话的最新数据，延时0.5秒执行
    const currentSessionId = selectedWorkflowSessionId || sessionId.current;
    if (currentSessionId) {
      console.log('关闭编辑器，0.5秒后获取最新会话数据:', currentSessionId);

      // 使用setTimeout延时0.5秒
      setTimeout(() => {
        // 再次检查是否正在生成PDF
        if (!pdfGenerating && !isPdfGeneratingRef.current) {
          console.log('开始获取延时后的会话数据:', currentSessionId);
          fetchWorkflowGraphData(currentSessionId).catch(error => {
            console.error('获取会话数据失败:', error);
          });
        } else {
          console.log('延时后发现PDF正在生成中，不获取工作流数据');
        }
      }, 500); // 延时500毫秒（0.5秒）
    }
  };

  // 保存编辑后的转录内容并根据需要重新生成视频
  const handleSaveTranscriptionsAndRegenerateVideo = async (
    editedTranscriptions: Array<{ title: string; content: string }>,
    regenerateVideo: boolean
  ) => {
    if (regenerateVideo && !pdfUrl) {
      message.error('无法重新生成视频：PDF文件不存在');
      return;
    }

    // console.log('保存编辑后的转录内容:', editedTranscriptions, '重新生成视频:', regenerateVideo);

    try {
      // 更新转录内容
      setVideoTranscriptions(editedTranscriptions);

      // 更新selectedWorkflowGraphData中的transcriptions字段
      if (selectedWorkflowGraphData) {
        const updatedData = { ...selectedWorkflowGraphData };

        // 更新 transcriptions 字段
        updatedData.transcriptions = editedTranscriptions;

        // 如果需要重新生成视频，更新 video_status 字段为处理中
        if (regenerateVideo) {
          updatedData.video_status = '生成中';
          setVideoStatus('生成中');
          console.log('更新 WorkflowGraph 中的 video_status 字段:', '生成中');
        }

        console.log('更新 WorkflowGraph 中的 transcriptions 字段:', editedTranscriptions);

        setSelectedWorkflowGraphData(updatedData);
      }

      // 如果需要重新生成视频
      if (regenerateVideo) {
        setVideoRegenerating(true);
        // 设置当前步骤为生成视频（步骤2）
        setCurrentStep(2);

        // 显示视频重新生成中的消息
        message.info('视频重新生成中，请稍候...');

        // 使用当前会话ID或选中的工作流会话ID
        const currentSessionId = selectedWorkflowSessionId || sessionId.current;

        // 调用pdfToVideo函数重新生成视频
        const videoPath = await pdfToVideo(pdfUrl, editedTranscriptions, currentSessionId);

        // 更新视频URL
        setVideoUrl(videoPath);

        // 设置视频状态为已完成
        setVideoStatus('已完成');

        // 设置当前步骤为完成（步骤3）
        setCurrentStep(3);

        // 更新selectedWorkflowGraphData中的视频URL
        if (selectedWorkflowGraphData) {
          const updatedData = { ...selectedWorkflowGraphData };

          // 更新 product_urls
          if (!updatedData.product_urls) {
            updatedData.product_urls = [];
          }

          // 检查是否已存在视频URL
          const videoUrlIndex = updatedData.product_urls.findIndex((item: ProductUrl) => item.type === 'video');
          if (videoUrlIndex >= 0) {
            updatedData.product_urls[videoUrlIndex].url = videoPath;
          } else {
            updatedData.product_urls.push({ type: 'video', url: videoPath });
          }

          // 更新 video_status 字段为已完成
          updatedData.video_status = '已完成';
          console.log('更新 WorkflowGraph 中的 video_status 字段:', '已完成');

          setSelectedWorkflowGraphData(updatedData);
        }

        // 显示成功消息
        message.success('视频重新生成成功');

        // 不需要在这里重新获取数据，因为关闭编辑器时会自动延时获取最新数据
      } else {
        // 如果不需要重新生成视频，只保存转录内容
        message.success('转录内容保存成功');
      }

      // 关闭转录编辑器
      closeTranscriptionEditor();
    } catch (error) {
      console.error(regenerateVideo ? '视频重新生成失败:' : '保存转录内容失败:', error);
      message.error(regenerateVideo ? '视频重新生成失败' : '保存转录内容失败');
    } finally {
      if (regenerateVideo) {
        setVideoRegenerating(false);
      }
    }
  };

  // 处理 HTML/SVG 代码块
  const handleHtmlSvgCodeDetected = (
    codeBlocks: { id: string; type: 'html' | 'svg'; content: string }[]
  ) => {
    console.log('检测到HTML/SVG代码块:', codeBlocks.length);
  };

  // 处理PDF HTML内容编辑
  const handleEditPdfHtml = (htmlIndex?: number) => {
    // 如果没有HTML代码块，显示提示
    if (!selectedWorkflowGraphData?.code_blocks ||
        !Array.isArray(selectedWorkflowGraphData.code_blocks) ||
        selectedWorkflowGraphData.code_blocks.length === 0) {
      message.info('没有可编辑的HTML内容');
      return;
    }

    // 设置当前选中的HTML索引
    const selectedIndex = (htmlIndex !== undefined && htmlIndex >= 0 && htmlIndex < selectedWorkflowGraphData.code_blocks.length)
      ? htmlIndex
      : 0;

    // 设置选中的HTML块ID (用于在保存时识别)
    setSelectedHtmlBlockId(selectedIndex.toString());

    // 显示编辑器 - 不再需要设置单个HTML内容，PdfHtmlEditor组件会处理数组
    setPdfEditorVisible(true);
  };

  // 处理HTML内容保存
  const handleSavePdfHtml = async (updatedWorkflowGraphData: WorkflowGraphData) => {
    if (!selectedWorkflowSessionId) {
      message.error('保存失败：缺少会话ID');
      return;
    }

    try {
      // 关闭编辑器
      setPdfEditorVisible(false);

      // 更新本地状态
      setSelectedWorkflowGraphData(updatedWorkflowGraphData);

      // 调用API保存到后端
      const updateEndpoint = `${WORKFLOW_GRAPH_ENDPOINT}/${updatedWorkflowGraphData.id}`;
      const updatePayload = {
        code_blocks: updatedWorkflowGraphData.code_blocks
      };

      console.log('保存到API:', updateEndpoint, updatePayload);
      
      // 调用API更新
      const response = await updateData(updateEndpoint, updatePayload);
      
      if (response) {
        // 设置PDF生成状态
        setPdfGenerating(true);
        isPdfGeneratingRef.current = true;

        message.info('内容已保存到后端，正在重新生成PDF...');

        // 提取HTML类型的代码块用于生成PDF
        const htmlCodeBlocks = Array.isArray(updatedWorkflowGraphData.code_blocks) ? 
          updatedWorkflowGraphData.code_blocks.filter((block: WorkflowCodeBlock) => block.language === 'html') : [];
        
        if (htmlCodeBlocks.length > 0) {
          console.log('📤 [PDF编辑] 调用htmlToPdf重新生成PDF，session_id:', selectedWorkflowSessionId);
          console.log('📊 [PDF编辑] HTML代码块数量:', htmlCodeBlocks.length);
          
          // 将HTML代码块转换为旧格式用于生成PDF
          const htmlContents = htmlCodeBlocks.map((block: WorkflowCodeBlock) => block.code);
          // const convertedCodeBlocks = convertHtmlStringsToCodeBlocks(htmlContents, selectedWorkflowSessionId);
          const { url } = await htmlToPdf(htmlContents, selectedWorkflowSessionId, 'PPT', "Product");
          setPdfUrl(url);
          message.success('内容已保存，PDF已更新');
        } else {
          message.warning('未找到HTML内容，无法生成PDF');
        }

        // 重置PDF生成状态
        setPdfGenerating(false);
        isPdfGeneratingRef.current = false;
      } else {
        throw new Error('API响应为空');
      }
    } catch (error) {
      console.error('保存HTML内容失败:', error);
      message.error(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`);

      // 确保在出错时也重置PDF生成状态
      setPdfGenerating(false);
      isPdfGeneratingRef.current = false;
    }
  };

  // 处理PDF编辑器取消
  const handleCancelPdfEditor = () => {
    setPdfEditorVisible(false);
  };

  // 渲染PDF编辑按钮
  const renderPdfEditButton = () => {
    return (
      <Button
        type="primary"
        size="small"
        icon={<EditOutlined />}
        onClick={() => handleEditPdfHtml()}
        disabled={!selectedWorkflowGraphData?.code_blocks || 
                 !Array.isArray(selectedWorkflowGraphData.code_blocks) ||
                 selectedWorkflowGraphData.code_blocks.filter((block: WorkflowCodeBlock) => block.language === 'html').length === 0}
        className={styles.downloadButton}
      >
        编辑PDF
      </Button>
    );
  };


  // 渲染输入区域
  const renderInputArea = () => (
    <div className={styles.inputAreaContainer}>
      <div
        className={styles.inputAreaHeader}
        onClick={toggleInputCollapse}
      >
        <Title level={5} style={{ margin: 0 }}>输入问题</Title>
        <Button
          type="text"
          icon={inputCollapsed ? <DownOutlined /> : <UpOutlined />}
          size="small"
        />
      </div>

      {!inputCollapsed && (
        <>
          <div className={styles.inputArea}>
            <TextArea
              rows={4}
              value={inputText}
              onChange={handleInputChange}
              placeholder="请输入您的主题或问题..."
              className={styles.inputTextArea}
              disabled={loading}
            />
          </div>

          <div className={styles.actionButtonContainer}>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRestart}
              disabled={loading}
            >
              重新开始
            </Button>
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSubmit}
              loading={loading}
              disabled={!inputText.trim() || loading}
            >
              生成课程
            </Button>
          </div>
        </>
      )}
    </div>
  );

  // 渲染主内容区域
  const renderMainContent = () => (
    <div className={styles.contentContainer}>
      {renderInputArea()}

      <div className={styles.markdownContainer} ref={markdownContainerRef}>
        {streamContent ? (
          <MarkdownRenderer
            content={streamContent}
            isStreaming={loading}
            onHtmlSvgCodeDetected={handleHtmlSvgCodeDetected}
          />
        ) : (
          <div style={{ textAlign: 'center', marginTop: '100px', color: '#999' }}>
            {hasError ? (
              <div>
                <CloseCircleOutlined style={{ fontSize: 48, color: '#ff4d4f', marginBottom: 16 }} />
                <p>生成失败: {errorMessage}</p>
              </div>
            ) : (
              <div>
                <p>在这里显示生成的课程内容</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  // 渲染课程内容和资源
  const renderResourcePanel = () => (
    <div className={styles.resourcePanel}>
      <div className={styles.toolbarContainer}>
        <Steps
          size="small"
          current={videoStatus === '生成中' ? 2 : currentStep}
          style={{ flex: 1 }}
          status={hasError ? "error" : (videoUrl ? "finish" : ((videoGenerating || videoStatus === '生成中') ? "process" : "wait"))}
        >
          <Step
            title={<span style={{ color: loading ? '#1890ff' : (currentStep > 0 ? '#52c41a' : undefined) }}>生成内容</span>}
            icon={loading ? <LoadingOutlined style={{ color: '#1890ff' }} /> : (currentStep > 0 ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : undefined)}
            status={loading ? "process" : (currentStep > 0 ? "finish" : "wait")}
            description={loading ? <span style={{ color: '#1890ff' }}>内容生成中...</span> : (isStreamingComplete ? "内容已生成" : undefined)}
          />
          <Step
            title={<span style={{ color: pdfGenerating && !pdfUrl ? '#1890ff' : (pdfUrl ? '#52c41a' : undefined) }}>生成PDF</span>}
            icon={pdfGenerating && !pdfUrl ? <LoadingOutlined style={{ color: '#1890ff' }} /> : (pdfUrl ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : undefined)}
            status={pdfUrl ? "finish" : (pdfGenerating && !pdfUrl ? "process" : "wait")}
            description={pdfGenerating && !pdfUrl ?
              <span style={{ color: '#1890ff' }}>PDF生成中...</span> :
              (pdfUrl ? "PDF已生成" : (htmlBlocks.length > 0 ? `${htmlBlocks.length}页` : undefined))}
          />
          <Step
            title={<span style={{ color: (videoGenerating || videoStatus === '生成中') ? '#1890ff' : (videoUrl ? '#52c41a' : undefined) }}>生成视频</span>}
            icon={(videoGenerating || videoStatus === '生成中') ? <LoadingOutlined style={{ color: '#1890ff' }} /> : (videoUrl ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : undefined)}
            status={videoUrl ? "finish" : ((videoGenerating || videoStatus === '生成中') ? "process" : "wait")}
            description={(videoGenerating || videoStatus === '生成中') ?
              <span style={{ color: '#1890ff' }}>视频生成中...</span> :
              (videoTranscriptions.length > 0 ? `${videoTranscriptions.length}段` : undefined)}
          />
          <Step
            title={<span style={{ color: currentStep === 3 ? '#52c41a' : undefined }}>完成</span>}
            icon={currentStep === 3 ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : undefined}
            description={currentStep === 3 ? <span style={{ color: '#52c41a' }}>全部完成</span> : undefined}
          />
        </Steps>
        <div className={styles.headerSettings}>
          <HeaderSettings />
        </div>
      </div>

      <div className={styles.responseContainer}>
        {(isStreamingComplete || pdfUrl || videoUrl) && (
          <div className={styles.videoGenerationContainer} style={{ maxHeight: '100%', flex: 1 }}>

            {/* PDF预览区域，放在顶部 */}
            {pdfUrl && (
              <div className={styles.pdfPreviewContainer}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                  <Text strong>PDF教材</Text>
                  <Space>
                    {renderPdfEditButton()}
                    <Button
                      type="primary"
                      size="small"
                      icon={<DownloadOutlined />}
                      onClick={handleDownloadPdf}
                      className={styles.downloadButton}
                    >
                      下载PDF
                    </Button>
                  </Space>
                </div>
                <div style={{ height: '300px', overflow: 'hidden' }}>
                  <iframe
                    src={pdfUrl}
                    width="100%"
                    height="100%"
                    style={{ border: 'none', borderRadius: 8 }}
                  />
                </div>
              </div>
            )}

            {/* 视频转录文本展示 - 放在PDF和视频之间 */}
            {pdfUrl && videoTranscriptions.length > 0 && (
              <div style={{ marginTop: 16, marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                  <Text strong>视频转录文本</Text>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    {!videoUrl && (
                      <>
                        <Button
                          type="primary"
                          size="small"
                          icon={<EditOutlined />}
                          onClick={openTranscriptionEditor}
                          disabled={videoStatus === '生成中'}
                          title={videoStatus === '生成中' ? '视频生成中，无法编辑文案' : '编辑视频文案'}
                          className={styles.downloadButton}
                        >
                          编辑文案
                        </Button>
                        <Button
                          type="primary"
                          size="small"
                          icon={<VideoCameraOutlined />}
                          onClick={handleGenerateVideo}
                          loading={videoRegenerating || videoStatus === '生成中'}
                          disabled={videoRegenerating || videoStatus === '生成中'}
                          title={videoStatus === '生成中' ? '视频正在生成中' : '生成视频'}
                          className={styles.downloadButton}
                        >
                          生成视频
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                <div className={styles.transcriptionBlock} style={{ maxHeight: '200px', overflowY: 'auto', padding: '8px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                  {videoTranscriptions.map((item, index) => (
                    <div key={index} style={{ marginBottom: 8 }}>
                      <Tag color="blue">{`第${index+1}部分`}</Tag>
                      <div><b>{item.title}</b></div>
                      <div>{item.content}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 视频预览区域，放在转录文本下方 */}
            {videoUrl && (
              <div className={styles.videoPreviewContainer} style={{ marginTop: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                  <Text strong>视频讲解</Text>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    <Button
                      type="primary"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={openTranscriptionEditor}
                      className={styles.downloadButton}
                      disabled={videoStatus !== '已完成'}
                      title={videoStatus !== '已完成' ? '视频生成未完成，无法编辑文案' : '编辑视频文案'}
                    >
                      编辑文案
                    </Button>
                    <Button
                      type="primary"
                      size="small"
                      icon={<DownloadOutlined />}
                      onClick={handleDownloadVideo}
                      className={styles.downloadButton}
                    >
                      下载视频
                    </Button>
                  </div>
                </div>
                <video
                  src={videoUrl}
                  controls
                  className={styles.videoPlayer}
                />
              </div>
            )}

            {/* 转录编辑器模态框 */}
            <TranscriptionEditor
              visible={transcriptionEditorVisible}
              onCancel={closeTranscriptionEditor}
              transcriptions={videoTranscriptions}
              videoUrl={videoUrl}
              pdfUrl={pdfUrl}
              onSave={handleSaveTranscriptionsAndRegenerateVideo}
              isRegenerating={videoRegenerating}
            />

            {/* PDF HTML编辑器模态框 */}
            {selectedWorkflowGraphData && (
              <PdfHtmlEditor
                visible={pdfEditorVisible}
                workflow_graph_data={selectedWorkflowGraphData}   // 传递整个WorkflowGraphData对象
                onSave={handleSavePdfHtml}
                onCancel={handleCancelPdfEditor}
                initialPage={parseInt(selectedHtmlBlockId) || 0}
              />
            )}
          </div>
        )}

        {!isStreamingComplete && !pdfUrl && !videoUrl && (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', color: '#999' }}>
            <div style={{ textAlign: 'center' }}>
              <VideoCameraOutlined style={{ fontSize: 48, marginBottom: 16 }} />
              <p>生成内容后将在此处显示PDF和视频</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className={styles.appContainer}>
      {/* 侧边栏 */}
      <WorkflowGraphSidebar
        collapsed={sidebarCollapsed}
        onToggleCollapse={toggleSidebar}
        onSelectWorkflowGraph={handleSelectWorkflowGraph}
        workflowId={WORKFLOW_ID}
      />

      {/* 主内容区域 */}
      <div className={styles.mainContainer}>
        <div className={styles.leftSide}>
          {renderMainContent()}
        </div>

        <div className={styles.rightSide}>
          {renderResourcePanel()}
        </div>
      </div>
    </div>
  );
};

export default AICourseGenerate;