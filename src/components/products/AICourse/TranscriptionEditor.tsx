import React, { useState, useEffect } from 'react';
import { Modal, Button, Input, Divider, message, Spin, Tooltip } from 'antd';
import { SaveOutlined, FileTextOutlined, VideoCameraOutlined, SwapOutlined } from '@ant-design/icons';
import styles from './TranscriptionEditor.module.css';

const { TextArea } = Input;

interface TranscriptionItem {
  title: string;
  content: string;
}

interface TranscriptionEditorProps {
  visible: boolean;
  onCancel: () => void;
  transcriptions: TranscriptionItem[];
  videoUrl: string;
  pdfUrl?: string; // 添加PDF URL
  onSave: (editedTranscriptions: TranscriptionItem[], regenerateVideo: boolean) => Promise<void>;
  isRegenerating: boolean;
}

const TranscriptionEditor: React.FC<TranscriptionEditorProps> = ({
  visible,
  onCancel,
  transcriptions,
  videoUrl,
  pdfUrl,
  onSave,
  isRegenerating
}) => {
  // State for edited transcriptions
  const [editedTranscriptions, setEditedTranscriptions] = useState<TranscriptionItem[]>([]);
  // State for preview mode (video or PDF)
  const [previewMode, setPreviewMode] = useState<'video' | 'pdf'>('video');
  // State to track if transcriptions have been modified
  const [isModified, setIsModified] = useState<boolean>(false);

  // Initialize edited transcriptions when the component mounts or transcriptions change
  useEffect(() => {
    // 如果有转录内容，使用它们
    if (transcriptions && transcriptions.length > 0) {
      setEditedTranscriptions([...transcriptions]);
      // Reset modified state when transcriptions are loaded
      setIsModified(false);
    }
  }, [transcriptions]);

  // 标题不可编辑，只显示

  // Handle content change for a specific transcription
  const handleContentChange = (index: number, newContent: string) => {
    // console.log(`修改第 ${index + 1} 部分内容:`, newContent.substring(0, 20) + '...');

    // Validate index is within bounds
    if (index < 0 || index >= editedTranscriptions.length) {
      console.error(`Invalid index: ${index}, transcriptions length: ${editedTranscriptions.length}`);
      return;
    }

    // We'll check for modifications in the next step

    const updatedTranscriptions = [...editedTranscriptions];
    updatedTranscriptions[index] = {
      ...updatedTranscriptions[index],
      content: newContent
    };
    setEditedTranscriptions(updatedTranscriptions);

    // Check if any transcription has been modified
    const hasAnyModification = updatedTranscriptions.some((item, idx) => {
      const original = transcriptions[idx]?.content || '';
      return item.content !== original;
    });

    // Update modified state
    setIsModified(hasAnyModification);
  };

  // Toggle between video and PDF preview
  const togglePreviewMode = () => {
    setPreviewMode(prevMode => prevMode === 'video' ? 'pdf' : 'video');
  };

  // Set initial preview mode based on available resources
  useEffect(() => {
    // If video URL doesn't exist but PDF URL does, default to PDF preview
    if (!videoUrl && pdfUrl) {
      setPreviewMode('pdf');
    }
  }, [videoUrl, pdfUrl]);

  // 不需要添加和删除功能，因为 transcriptions 数据来自于 AICourseGenerate 组件

  // Handle save and regenerate video
  const handleSaveAndRegenerate = async () => {
    // Validate that all transcriptions have content
    const hasEmptyTranscriptions = editedTranscriptions.some(
      item => !item.content.trim()
    );

    if (hasEmptyTranscriptions) {
      message.warning('内容不能为空，请检查所有转录文本');
      return;
    }

    try {
      // Different behavior based on whether content was modified
      if (isModified) {
        console.log('保存并重新生成视频，使用编辑后的转录内容:', editedTranscriptions);
        message.info('正在保存修改并重新生成视频，请稍候...');

        // Call onSave with regenerateVideo=true
        await onSave(editedTranscriptions, true);

        message.success('视频文案修改成功，正在重新生成视频');
      } else {
        console.log('保存转录内容，无修改:', editedTranscriptions);
        message.info('正在保存转录内容...');

        // Call onSave with regenerateVideo=false
        await onSave(editedTranscriptions, false);

        message.success('转录内容保存成功');
        // Close the modal after saving if no modifications were made
        onCancel();
      }
    } catch (error) {
      console.error('保存转录文本失败:', error);
      message.error('保存转录文本失败');
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
          <span>编辑视频文案</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width="95%"
      style={{ top: 20 }}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          icon={<SaveOutlined />}
          onClick={handleSaveAndRegenerate}
          loading={isRegenerating}
          disabled={isRegenerating}
        >
          {isModified
            ? (videoUrl ? '保存并重新生成视频' : '保存并生成视频')
            : '保存'}
        </Button>
      ]}
      styles={{
        body: { height: 'calc(90vh - 100px)', padding: '16px', overflow: 'hidden' },
        header: { paddingBottom: '12px' },
        content: { maxHeight: '90vh' }
      }}
      destroyOnHidden
    >
      <div className={styles.transcriptionEditorContainer}>
        {/* Left side - Transcription editing */}
        <div className={styles.transcriptionEditingPanel}>
          <div style={{ marginBottom: '12px' }}>
            <h3>文案编辑</h3>
          </div>
          <div className={styles.transcriptionList}>
            {editedTranscriptions && editedTranscriptions.length > 0 ? (
              editedTranscriptions.map((item, index) => (
                <div key={index} className={styles.transcriptionEditItem}>
                  <div className={styles.transcriptionHeader}>
                    <span className={styles.transcriptionNumber}>第 {index + 1} 部分</span>
                  </div>
                  <div className={styles.transcriptionForm}>
                    <div className={styles.transcriptionField}>
                      <label>标题:</label>
                      <div className={styles.titleDisplay}>
                        {item.title}
                      </div>
                    </div>
                    <div className={styles.transcriptionField}>
                      <label>内容:</label>
                      <TextArea
                        value={item.content}
                        onChange={(e) => handleContentChange(index, e.target.value)}
                        placeholder="输入内容"
                        autoSize={{ minRows: 2, maxRows: 10 }}
                        className={styles.autoResizeTextArea}
                      />
                    </div>
                  </div>
                  {index < editedTranscriptions.length - 1 && (
                    <Divider style={{ margin: '8px 0' }} />
                  )}
                </div>
              ))
            ) : (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                <p>没有可用的转录内容</p>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Preview panel (Video or PDF) */}
        <div className={styles.previewPanel}>
          <div className={styles.previewHeader}>
            <h3>{previewMode === 'video' ? '视频预览' : 'PDF预览'}</h3>
            {pdfUrl && videoUrl && (
              <Tooltip title={`切换到${previewMode === 'video' ? 'PDF' : '视频'}预览`}>
                <Button
                  type="text"
                  icon={<SwapOutlined />}
                  onClick={togglePreviewMode}
                  className={styles.previewToggleButton}
                >
                  切换到{previewMode === 'video' ? 'PDF' : '视频'}
                </Button>
              </Tooltip>
            )}
          </div>

          <div className={styles.previewWrapper}>
            {isRegenerating ? (
              <div className={styles.regeneratingOverlay}>
                <Spin size="large" />
                <p>正在重新生成{previewMode === 'video' ? '视频' : 'PDF'}，请稍候...</p>
              </div>
            ) : previewMode === 'video' ? (
              videoUrl ? (
                <video
                  src={videoUrl}
                  controls
                  className={styles.videoPlayer}
                />
              ) : (
                <div className={styles.noPreviewAvailable}>
                  <VideoCameraOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                  <p>没有可用的视频预览</p>
                </div>
              )
            ) : (
              pdfUrl ? (
                <iframe
                  src={pdfUrl}
                  className={styles.pdfPreview}
                  title="PDF预览"
                />
              ) : (
                <div className={styles.noPreviewAvailable}>
                  <FileTextOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                  <p>没有可用的PDF预览</p>
                </div>
              )
            )}
          </div>

          <div className={styles.previewNote}>
            <p>注意：{isModified
              ? (videoUrl
                ? `修改文案后，需要点击"保存并重新生成视频"按钮来更新视频。`
                : `修改文案后，需要点击"保存并生成视频"按钮来生成视频。`)
              : `如需修改文案，请编辑左侧内容后点击"${videoUrl ? '保存并重新生成视频' : '保存并生成视频'}"按钮。`}
            </p>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default TranscriptionEditor;
