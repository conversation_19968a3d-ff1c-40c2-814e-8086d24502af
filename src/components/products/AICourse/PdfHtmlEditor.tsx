import React, { useState, useEffect, useRef } from 'react';
import { Button, Tabs, Space, Modal, message, Input, Form, Select, Slider, ColorPicker, Radio, Divider, Pagination, Spin } from 'antd';
import { SaveOutlined, EyeOutlined, UndoOutlined, RedoOutlined, CodeOutlined, EditOutlined, LeftOutlined, RightOutlined, ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons';
import styles from './PdfHtmlEditor.module.css';
import { WorkflowGraphData } from '../../../utils/WorkflowCodeBlockProcessor';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface PdfHtmlEditorProps {
  workflow_graph_data: WorkflowGraphData; // WorkflowGraph数据，包含所有代码块
  onSave: (updatedWorkflowGraphData: WorkflowGraphData) => void; // 保存回调，返回更新后的完整数据
  onCancel: () => void; // 取消回调
  visible: boolean; // 是否显示
  initialPage?: number; // 初始页码
}

/**
 * PDF HTML编辑器组件
 * 提供HTML内容的编辑功能，可以修改PDF的内容、样式和布局
 * 支持多页面编辑
 */
const PdfHtmlEditor: React.FC<PdfHtmlEditorProps> = ({
  workflow_graph_data,
  onSave,
  onCancel,
  visible,
  initialPage = 0
}) => {
  // 编辑模式：可视化编辑或源代码编辑
  const [editMode, setEditMode] = useState<'visual' | 'source'>('visual');

  // 当前页码
  const [currentPage, setCurrentPage] = useState<number>(initialPage);

  // 当前页的HTML内容
  const [htmlContent, setHtmlContent] = useState<string>('');

  // 内容是否被修改
  const [isContentModified, setIsContentModified] = useState<boolean>(false);

  // 加载状态
  const [loading, setLoading] = useState<boolean>(true);

  // 缩放级别
  const [zoomLevel, setZoomLevel] = useState<number>(100);

  // 预览模式
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);

  // 编辑历史
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(0);

  // iframe引用
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 当前选中的元素
  const [selectedElement, setSelectedElement] = useState<{
    element: HTMLElement | null;
    path: string;
    styles: Record<string, string>;
    content: string;
    tagName?: string;
  } | null>(null);

  // 样式编辑状态
  const [textColor, setTextColor] = useState<string>('#000000');
  const [backgroundColor, setBackgroundColor] = useState<string>('transparent');
  const [fontSize, setFontSize] = useState<number>(16);
  const [fontWeight, setFontWeight] = useState<string>('normal');
  const [textAlign, setTextAlign] = useState<string>('left');
  const [padding, setPadding] = useState<number>(0);
  const [margin, setMargin] = useState<number>(0);
  const [borderRadius, setBorderRadius] = useState<number>(0);
  const [elementContent, setElementContent] = useState<string>('');

  // ECharts 数据状态
  const [isEchartsElement, setIsEchartsElement] = useState<boolean>(false);
  const [echartsData, setEchartsData] = useState<string>('');
  const [echartsType, setEchartsType] = useState<string>('');

  // 原始HTML编辑状态
  const [rawHtmlModalVisible, setRawHtmlModalVisible] = useState<boolean>(false);
  const [rawHtmlContent, setRawHtmlContent] = useState<string>('');
  const [modalTitle, setModalTitle] = useState<string>("编辑原始HTML");

  // 监听visible变化，重置状态
  useEffect(() => {
    if (visible) {
      setCurrentPage(initialPage);
      setLoading(editMode === 'visual'); // 根据编辑模式设置加载状态
      setIsContentModified(false);
    }
  }, [visible, initialPage, editMode]);

  // 过滤出HTML类型的代码块
  const htmlCodeBlocks = workflow_graph_data?.code_blocks?.filter(block => block.language === 'html') || [];

  // 监听currentPage变化，更新当前页的内容
  useEffect(() => {
    if (visible && htmlCodeBlocks && htmlCodeBlocks.length > 0) {
      if (currentPage >= 0 && currentPage < htmlCodeBlocks.length) {
        const codeBlock = htmlCodeBlocks[currentPage];
        const content = codeBlock?.code || '';
        setHtmlContent(content);
        setHistory([content]);
        setHistoryIndex(0);
        setLoading(true); // 页面切换时开始加载
        setIsContentModified(false); // 重置修改状态
      } else {
        // 如果页码超出范围，设置为默认内容
        const defaultContent = `
<div style="padding: 20px; font-family: Arial, sans-serif;">
  <h1 style="color: #333;">PDF 内容</h1>
  <p style="font-size: 16px; line-height: 1.5;">
    这是默认的PDF内容模板，您可以开始编辑这段文本。
  </p>
  <div style="background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-top: 20px;">
    <h3 style="color: #0066cc;">编辑说明</h3>
    <ul>
      <li>点击任意文本或元素进行选择</li>
      <li>在右侧面板修改内容和样式</li>
      <li>使用源代码模式直接编辑HTML</li>
    </ul>
  </div>
</div>
        `;
        setHtmlContent(defaultContent);
        setHistory([defaultContent]);
        setHistoryIndex(0);
        setIsContentModified(true); // 新页面默认为已修改状态
      }
    }
  }, [visible, htmlCodeBlocks, currentPage]);

  // 当HTML内容变化时更新预览
  useEffect(() => {
    if (editMode === 'visual' && htmlContent) {
      updateVisualEditor();
    } else if (editMode === 'source' && htmlContent) {
      // 源代码模式下直接关闭加载状态
      setLoading(false);
    }
  }, [htmlContent, editMode]);

  // 组件挂载时立即更新预览
  useEffect(() => {
    if (visible && editMode === 'visual' && htmlContent) {
      setTimeout(() => {
        updateVisualEditor();
      }, 300);
    } else if (visible && editMode === 'source') {
      // 源代码模式下直接关闭加载状态
      setLoading(false);
    }
  }, [visible, editMode, htmlContent]);

  // 更新可视化编辑器
  const updateVisualEditor = () => {
    if (!iframeRef.current) return;

    const iframe = iframeRef.current;
    setLoading(true);

    // 设置超时，确保加载状态不会无限期显示
    const loadingTimeout = setTimeout(() => {
      setLoading(false);
      console.log('加载超时，自动关闭加载状态');
    }, 2000); // 2秒后自动关闭加载状态

    // 保存超时ID到ref中，以便在收到加载完成消息时清除
    const timeoutId = loadingTimeout;

    // 将timeoutId存储在window对象上，以便在消息处理函数中访问
    (window as any).__loadingTimeoutId = timeoutId;

    try {
      // 创建完整的HTML文档
      const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      margin: 0 auto;
      padding: 20px;
      font-family: Arial, sans-serif;
      font-size: 16px;
      line-height: 1.6;
      width: 900px;
      max-width: 95%;
      box-sizing: border-box;
      background-color: white;
      overflow-x: visible;
    }

    h1, h2, h3, h4, h5, h6 {
      margin-top: 1.2em;
      margin-bottom: 0.8em;
    }

    p {
      margin-bottom: 1em;
    }

    .element-highlight {
      outline: 3px dashed #1890ff;
      position: relative;
    }

    .element-highlight.draggable {
      cursor: move;
    }

    .element-highlight.dragging {
      opacity: 0.5;
      outline: 3px dashed #ff4d4f;
    }

    .element-path {
      position: fixed;
      bottom: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 1000;
    }

    .drag-handle {
      position: absolute;
      top: -20px;
      left: 0;
      background: #1890ff;
      color: white;
      padding: 2px 8px;
      font-size: 12px;
      border-radius: 4px 4px 0 0;
      cursor: move;
      z-index: 100;
    }
    
    /* 调整大小控制点样式 */
    .resize-handle {
      position: absolute;
      width: 8px;
      height: 8px;
      background-color: #1890ff;
      border: 1px solid white;
      z-index: 100;
    }
    
    .resize-handle.top-left {
      top: -5px;
      left: -5px;
      cursor: nw-resize;
    }
    
    .resize-handle.top-right {
      top: -5px;
      right: -5px;
      cursor: ne-resize;
    }
    
    .resize-handle.bottom-left {
      bottom: -5px;
      left: -5px;
      cursor: sw-resize;
    }
    
    .resize-handle.bottom-right {
      bottom: -5px;
      right: -5px;
      cursor: se-resize;
    }
    
    .resize-handle.right {
      top: 50%;
      right: -5px;
      transform: translateY(-50%);
      cursor: e-resize;
    }
    
    .resize-handle.left {
      top: 50%;
      left: -5px;
      transform: translateY(-50%);
      cursor: w-resize;
    }
    
    .resize-handle.top {
      top: -5px;
      left: 50%;
      transform: translateX(-50%);
      cursor: n-resize;
    }
    
    .resize-handle.bottom {
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      cursor: s-resize;
    }
    
    /* 元素控制面板 */
    .element-controls {
      position: absolute;
      top: -24px;
      right: 0;
      display: flex;
      background: #1890ff;
      border-radius: 4px 4px 0 0;
      z-index: 100;
    }
    
    .element-controls button {
      background: none;
      border: none;
      color: white;
      padding: 2px 6px;
      font-size: 12px;
      cursor: pointer;
    }
    
    .element-controls button:hover {
      background-color: rgba(255,255,255,0.2);
    }
    
    .element-controls .divider {
      width: 1px;
      background-color: rgba(255,255,255,0.4);
      margin: 2px 0;
    }

    * {
      cursor: pointer;
    }

    *:hover {
      outline: 1px dashed rgba(24, 144, 255, 0.5);
    }
  </style>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 通知父窗口iframe已加载完成
      window.parent.postMessage({
        type: 'iframeLoaded'
      }, '*');
    
      // 元素路径显示
      let pathDisplay = document.createElement('div');
      pathDisplay.className = 'element-path';
      pathDisplay.style.display = 'none';
      document.body.appendChild(pathDisplay);

      // 获取元素路径
      function getElementPath(element) {
        let path = [];
        let currentElement = element;

        while (currentElement && currentElement !== document.body) {
          let tag = currentElement.tagName.toLowerCase();
          let id = currentElement.id ? '#' + currentElement.id : '';
          let classes = Array.from(currentElement.classList)
            .filter(cls => cls !== 'element-highlight' && cls !== 'draggable' && cls !== 'dragging')
            .map(cls => '.' + cls)
            .join('');

          path.unshift(tag + id + classes);
          currentElement = currentElement.parentElement;
        }

        return path.join(' > ');
      }

      // 检测元素是否为ECharts容器
      function detectEchartsElement(element) {
        // 检查是否有id属性
        if (element.id) {
          // 在页面中查找是否有初始化该元素的ECharts代码
          const scripts = document.querySelectorAll('script');
          for (let i = 0; i < scripts.length; i++) {
            const scriptContent = scripts[i].textContent || '';

            // 检查脚本中是否包含echarts.init和元素id
            if (
              scriptContent.includes('echarts.init') &&
              scriptContent.includes(element.id)
            ) {
              // 尝试提取ECharts配置数据
              try {
                // 查找option对象
                const optionMatch = scriptContent.match(/var\\s+option\\s*=\\s*(\\{[\\s\\S]*?\\});/);
                if (optionMatch && optionMatch[1]) {
                  // 确定图表类型
                  let chartType = '';
                  if (scriptContent.includes('type: \\'pie\\'')) chartType = 'pie';
                  else if (scriptContent.includes('type: \\'bar\\'')) chartType = 'bar';
                  else if (scriptContent.includes('type: \\'line\\'')) chartType = 'line';
                  else chartType = '未知';

                  return {
                    isEcharts: true,
                    data: optionMatch[1],
                    type: chartType
                  };
                }
              } catch (e) {
                console.error('解析ECharts数据失败:', e);
              }

              // 即使无法提取具体数据，也标记为ECharts元素
              return {
                isEcharts: true,
                data: '',
                type: '未知'
              };
            }
          }
        }

        return { isEcharts: false };
      }

      // 为所有元素添加点击事件和拖拽功能
      function makeElementsEditable() {
        const allElements = document.querySelectorAll('body *');
        let draggedElement = null;
        let dragStartX = 0;
        let dragStartY = 0;
        let originalElementX = 0;
        let originalElementY = 0;
        let originalElementWidth = 0;
        let originalElementHeight = 0;
        let resizeDirection = '';
        let isResizing = false;
        
        // 添加样式到元素
        function addPositionStyle(element) {
          // 确保元素已设置为相对定位，便于调整位置
          const position = window.getComputedStyle(element).position;
          if (position === 'static') {
            element.style.position = 'relative';
          }
        }
        
        // 创建调整大小控制点
        function createResizeHandles(element) {
          // 移除旧的控制点
          element.querySelectorAll('.resize-handle').forEach(handle => {
            handle.remove();
          });
          
          // 创建8个方向的控制点
          const directions = [
            'top-left', 'top', 'top-right',
            'left', 'right',
            'bottom-left', 'bottom', 'bottom-right'
          ];
          
          directions.forEach(direction => {
            const handle = document.createElement('div');
            handle.className = 'resize-handle ' + direction;
            handle.setAttribute('data-direction', direction);
            element.appendChild(handle);
            
            // 添加事件监听器
            handle.addEventListener('mousedown', startResize);
          });
        }
        
        // 创建控制面板
        function createControlPanel(element) {
          // 移除旧的控制面板
          element.querySelectorAll('.element-controls').forEach(panel => {
            panel.remove();
          });
          
          const controlPanel = document.createElement('div');
          controlPanel.className = 'element-controls';
          
          // 添加各种控制按钮
          const moveButton = document.createElement('button');
          moveButton.textContent = '移动';
          moveButton.title = '拖动调整位置';
          moveButton.className = 'move-btn';
          controlPanel.appendChild(moveButton);
          
          const divider1 = document.createElement('div');
          divider1.className = 'divider';
          controlPanel.appendChild(divider1);
          
          const widthButton = document.createElement('button');
          widthButton.textContent = '100%宽';
          widthButton.title = '设置为100%宽度';
          widthButton.className = 'width-btn';
          widthButton.addEventListener('click', (e) => {
            e.stopPropagation();
            element.style.width = '100%';
            updateParent(element);
          });
          controlPanel.appendChild(widthButton);
          
          const widthAdjustBtn = document.createElement('button');
          widthAdjustBtn.textContent = '宽+20';
          widthAdjustBtn.title = '增加20px宽度';
          widthAdjustBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            const currentWidth = parseInt(window.getComputedStyle(element).width);
            element.style.width = (currentWidth + 20) + 'px';
            updateParent(element);
          });
          controlPanel.appendChild(widthAdjustBtn);
          
          const divider2 = document.createElement('div');
          divider2.className = 'divider';
          controlPanel.appendChild(divider2);
          
          const centerButton = document.createElement('button');
          centerButton.textContent = '居中';
          centerButton.title = '水平居中';
          centerButton.addEventListener('click', (e) => {
            e.stopPropagation();
            element.style.marginLeft = 'auto';
            element.style.marginRight = 'auto';
            element.style.display = 'block';
            updateParent(element);
          });
          controlPanel.appendChild(centerButton);
          
          element.appendChild(controlPanel);
          
          // 启用拖拽
          moveButton.addEventListener('mousedown', (e) => {
            e.stopPropagation();
            startDrag(e, element);
          });
        }

        // 添加拖拽功能
        function setupDraggable(element) {
          if (element.tagName.toLowerCase() === 'div' || 
              element.tagName.toLowerCase() === 'section' || 
              element.tagName.toLowerCase() === 'article') {
              
            element.classList.add('draggable');
            
            // 确保元素可定位
            addPositionStyle(element);
            
            // 创建调整大小控制点
            createResizeHandles(element);
            
            // 创建控制面板
            createControlPanel(element);
          }
        }
        
        // 开始调整大小
        function startResize(e) {
          e.stopPropagation();
          e.preventDefault();
          
          const resizeHandle = e.target;
          const element = resizeHandle.parentElement;
          
          if (!element.classList.contains('element-highlight')) {
            return;
          }
          
          isResizing = true;
          resizeDirection = resizeHandle.getAttribute('data-direction');
          
          // 记录初始位置和尺寸
          const rect = element.getBoundingClientRect();
          originalElementX = rect.left;
          originalElementY = rect.top;
          originalElementWidth = rect.width;
          originalElementHeight = rect.height;
          dragStartX = e.clientX;
          dragStartY = e.clientY;
          
          // 添加全局事件监听器
          document.addEventListener('mousemove', handleResize);
          document.addEventListener('mouseup', stopResize);
          
          // 阻止文本选择
          document.body.style.userSelect = 'none';
        }
        
        // 处理调整大小
        function handleResize(e) {
          if (!isResizing) return;
          
          const deltaX = e.clientX - dragStartX;
          const deltaY = e.clientY - dragStartY;
          
          // 从文档中获取当前高亮的元素
          const element = document.querySelector('.element-highlight');
          if (!element) {
            stopResize();
            return;
          }
          
          // 根据调整方向更新元素尺寸和位置
          switch (resizeDirection) {
            case 'right':
              element.style.width = (originalElementWidth + deltaX) + 'px';
              break;
            case 'left':
              element.style.width = (originalElementWidth - deltaX) + 'px';
              if (element.style.position === 'relative') {
                element.style.left = deltaX + 'px';
              }
              break;
            case 'bottom':
              element.style.height = (originalElementHeight + deltaY) + 'px';
              break;
            case 'top':
              element.style.height = (originalElementHeight - deltaY) + 'px';
              if (element.style.position === 'relative') {
                element.style.top = deltaY + 'px';
              }
              break;
            case 'bottom-right':
              element.style.width = (originalElementWidth + deltaX) + 'px';
              element.style.height = (originalElementHeight + deltaY) + 'px';
              break;
            case 'bottom-left':
              element.style.width = (originalElementWidth - deltaX) + 'px';
              element.style.height = (originalElementHeight + deltaY) + 'px';
              if (element.style.position === 'relative') {
                element.style.left = deltaX + 'px';
              }
              break;
            case 'top-right':
              element.style.width = (originalElementWidth + deltaX) + 'px';
              element.style.height = (originalElementHeight - deltaY) + 'px';
              if (element.style.position === 'relative') {
                element.style.top = deltaY + 'px';
              }
              break;
            case 'top-left':
              element.style.width = (originalElementWidth - deltaX) + 'px';
              element.style.height = (originalElementHeight - deltaY) + 'px';
              if (element.style.position === 'relative') {
                element.style.top = deltaY + 'px';
                element.style.left = deltaX + 'px';
              }
              break;
          }
        }
        
        // 停止调整大小
        function stopResize() {
          if (!isResizing) return;
          
          isResizing = false;
          document.removeEventListener('mousemove', handleResize);
          document.removeEventListener('mouseup', stopResize);
          document.body.style.userSelect = '';
          
          // 获取当前选中元素
          const element = document.querySelector('.element-highlight');
          if (element) {
            // 通知父窗口内容已更新
            updateParent(element);
          }
        }
        
        // 开始拖拽
        function startDrag(e, element) {
          e.stopPropagation();
          e.preventDefault();
          
          if (!element.classList.contains('element-highlight')) {
            return;
          }
          
          draggedElement = element;
          draggedElement.classList.add('dragging');
          
          // 记录初始位置
          const rect = element.getBoundingClientRect();
          originalElementX = rect.left;
          originalElementY = rect.top;
          dragStartX = e.clientX;
          dragStartY = e.clientY;
          
          // 确保元素可定位
          addPositionStyle(element);
          
          // 添加全局事件监听器
          document.addEventListener('mousemove', handleDrag);
          document.addEventListener('mouseup', stopDrag);
          
          // 阻止文本选择
          document.body.style.userSelect = 'none';
        }
        
        // 处理拖拽
        function handleDrag(e) {
          if (!draggedElement) return;
          
          const deltaX = e.clientX - dragStartX;
          const deltaY = e.clientY - dragStartY;
          
          // 计算新位置
          const currentLeft = parseFloat(draggedElement.style.left || '0');
          const currentTop = parseFloat(draggedElement.style.top || '0');
          
          // 更新位置
          draggedElement.style.left = (currentLeft + deltaX) + 'px';
          draggedElement.style.top = (currentTop + deltaY) + 'px';
          
          // 更新起始位置
          dragStartX = e.clientX;
          dragStartY = e.clientY;
        }
        
        // 更新父窗口
        function updateParent(element) {
          // 更新HTML内容并通知父窗口
          window.parent.postMessage({
            type: 'contentUpdated',
            content: document.body.innerHTML
          }, '*');
        }
        
        // 停止拖拽
        function stopDrag() {
          if (!draggedElement) return;
          
          draggedElement.classList.remove('dragging');
          updateParent(draggedElement);
          draggedElement = null;
          
          document.removeEventListener('mousemove', handleDrag);
          document.removeEventListener('mouseup', stopDrag);
          document.body.style.userSelect = '';
        }

        // 添加事件监听器
        allElements.forEach(el => {
          // 鼠标悬停显示路径
          el.addEventListener('mouseover', (e) => {
            e.stopPropagation();
            pathDisplay.textContent = getElementPath(el);
            pathDisplay.style.display = 'block';
          });

          el.addEventListener('mouseout', () => {
            pathDisplay.style.display = 'none';
          });

          // 点击选择元素
          el.addEventListener('click', (e) => {
            e.stopPropagation();
            e.preventDefault();

            // 移除其他元素的高亮和拖拽手柄
            document.querySelectorAll('.element-highlight').forEach(highlighted => {
              highlighted.classList.remove('element-highlight');
              const handle = highlighted.querySelector('.drag-handle');
              if (handle) handle.style.display = 'none';
              
              // 移除调整大小控制点
              highlighted.querySelectorAll('.resize-handle').forEach(handle => {
                handle.remove();
              });
              
              // 移除控制面板
              highlighted.querySelectorAll('.element-controls').forEach(panel => {
                panel.remove();
              });
            });

            // 添加高亮
            el.classList.add('element-highlight');

            // 如果是div/section/article元素，设置为可拖拽
            if (el.tagName.toLowerCase() === 'div' || 
                el.tagName.toLowerCase() === 'section' || 
                el.tagName.toLowerCase() === 'article') {
              setupDraggable(el);
            }

            // 获取计算样式
            const computedStyle = window.getComputedStyle(el);
            const styles = {
              color: computedStyle.color,
              backgroundColor: computedStyle.backgroundColor,
              fontSize: computedStyle.fontSize,
              fontWeight: computedStyle.fontWeight,
              textAlign: computedStyle.textAlign,
              padding: computedStyle.padding,
              margin: computedStyle.margin,
              borderRadius: computedStyle.borderRadius,
              width: computedStyle.width,
              height: computedStyle.height
            };

            // 检测是否为ECharts元素
            const echartsInfo = detectEchartsElement(el);

            // 通知父窗口
            window.parent.postMessage({
              type: 'elementSelected',
              path: getElementPath(el),
              tagName: el.tagName,
              content: el.innerHTML,
              text: el.innerText || el.textContent,
              styles: styles,
              attributes: Array.from(el.attributes).reduce((obj, attr) => {
                obj[attr.name] = attr.value;
                return obj;
              }, {}),
              isEcharts: echartsInfo.isEcharts,
              echartsData: echartsInfo.data || '',
              echartsType: echartsInfo.type || ''
            }, '*');
          });
        });

        // 点击body时清除选择
        document.body.addEventListener('click', (e) => {
          if (e.target === document.body) {
            document.querySelectorAll('.element-highlight').forEach(highlighted => {
              highlighted.classList.remove('element-highlight');
              // 移除调整大小控制点
              highlighted.querySelectorAll('.resize-handle').forEach(handle => {
                handle.remove();
              });
              
              // 移除控制面板
              highlighted.querySelectorAll('.element-controls').forEach(panel => {
                panel.remove();
              });
            });

            window.parent.postMessage({
              type: 'elementSelected',
              path: 'body',
              tagName: 'BODY',
              content: document.body.innerHTML,
              text: document.body.innerText,
              styles: {},
              attributes: {},
              isEcharts: false,
              echartsData: '',
              echartsType: ''
            }, '*');
          }
        });
      }

      // 初始化编辑功能
      makeElementsEditable();
    });
  </script>
</head>
<body>
  ${htmlContent}
</body>
</html>
      `;

      // 使用srcdoc属性设置iframe内容
      iframe.srcdoc = fullHtml;

      // 添加iframe加载完成处理
      iframe.onload = () => {
        // 有时iframe加载完成但没有触发DOMContentLoaded事件
        // 这里添加一个额外的检查，确保加载状态被正确清除
        setTimeout(() => {
          console.log('iframe.onload: 检查并关闭加载状态');
          setLoading(false);

          // 清除加载超时定时器
          if ((window as any).__loadingTimeoutId) {
            clearTimeout((window as any).__loadingTimeoutId);
            (window as any).__loadingTimeoutId = null;
          }
        }, 500); // 0.5秒后关闭加载状态
      };

      // 添加iframe加载错误处理
      iframe.onerror = () => {
        console.error('iframe加载失败');
        setLoading(false);

        // 清除加载超时定时器
        if ((window as any).__loadingTimeoutId) {
          clearTimeout((window as any).__loadingTimeoutId);
          (window as any).__loadingTimeoutId = null;
        }
      };
    } catch (error) {
      console.error('更新可视化编辑器失败:', error);
      message.error('更新可视化编辑器失败');
      setLoading(false);

      // 清除加载超时定时器
      if ((window as any).__loadingTimeoutId) {
        clearTimeout((window as any).__loadingTimeoutId);
        (window as any).__loadingTimeoutId = null;
      }
    }
  };

  // 监听iframe消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 处理iframe加载完成消息
      if (event.data && event.data.type === 'iframeLoaded') {
        console.log('Iframe加载完成');
        setLoading(false);

        // 清除加载超时定时器
        if ((window as any).__loadingTimeoutId) {
          clearTimeout((window as any).__loadingTimeoutId);
          (window as any).__loadingTimeoutId = null;
        }
      }
      // 处理元素选中消息
      else if (event.data && event.data.type === 'elementSelected') {
        console.log('选中元素:', event.data);

        // 更新选中元素状态
        setSelectedElement({
          element: null, // 无法直接传递DOM元素
          path: event.data.path,
          styles: event.data.styles || {},
          content: event.data.content || '',
          tagName: event.data.tagName
        });

        // 更新样式编辑状态
        const styles = event.data.styles || {};
        setTextColor(styles.color || '#000000');
        setBackgroundColor(styles.backgroundColor || 'transparent');
        setFontSize(parseInt(styles.fontSize) || 16);
        setFontWeight(styles.fontWeight || 'normal');
        setTextAlign(styles.textAlign || 'left');
        setPadding(parseInt(styles.padding) || 0);
        setMargin(parseInt(styles.margin) || 0);
        setBorderRadius(parseInt(styles.borderRadius) || 0);

        // 更新内容编辑状态
        setElementContent(event.data.content || '');

        // 处理ECharts数据
        if (event.data.isEcharts) {
          setIsEchartsElement(true);
          setEchartsData(event.data.echartsData || '');
          setEchartsType(event.data.echartsType || '未知');
          console.log('检测到ECharts元素:', event.data.echartsType);
        } else {
          setIsEchartsElement(false);
          setEchartsData('');
          setEchartsType('');
        }
      }
      // 处理内容更新消息（拖拽后）
      else if (event.data && event.data.type === 'contentUpdated') {
        console.log('内容已更新（拖拽）');

        // 更新HTML内容
        setHtmlContent(event.data.content);

        // 标记为已修改
        setIsContentModified(true);

        // 添加到历史记录
        addToHistory(event.data.content);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);

      // 清除所有可能存在的超时定时器
      if ((window as any).__loadingTimeoutId) {
        clearTimeout((window as any).__loadingTimeoutId);
        (window as any).__loadingTimeoutId = null;
      }
    };
  }, []);

  // 更新元素内容
  const updateElementContent = () => {
    if (!selectedElement || !iframeRef.current) return;

    try {
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) return;

      // 查找选中的元素
      const selectedDomElement = iframeDoc.querySelector('.element-highlight');
      if (selectedDomElement) {
        // 更新内容
        selectedDomElement.innerHTML = elementContent;

        // 更新HTML内容
        setHtmlContent(iframeDoc.body.innerHTML);

        // 标记为已修改
        setIsContentModified(true);

        // 添加到历史记录
        addToHistory(iframeDoc.body.innerHTML);

        message.success('内容已更新');
      }
    } catch (error) {
      console.error('更新元素内容失败:', error);
      message.error('更新元素内容失败');
    }
  };

  // 删除选中的元素
  const deleteSelectedElement = () => {
    if (!selectedElement || !iframeRef.current) return;

    try {
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) return;

      // 查找选中的元素
      const selectedDomElement = iframeDoc.querySelector('.element-highlight');
      if (selectedDomElement) {
        // 确认是否为div元素
        if (selectedDomElement.tagName.toLowerCase() === 'div') {
          // 删除元素
          selectedDomElement.parentNode?.removeChild(selectedDomElement);

          // 更新HTML内容
          setHtmlContent(iframeDoc.body.innerHTML);

          // 清除选中状态
          setSelectedElement(null);

          // 标记为已修改
          setIsContentModified(true);

          // 添加到历史记录
          addToHistory(iframeDoc.body.innerHTML);

          message.success('已删除选中的div元素');
        } else {
          message.warning('只能删除div元素');
        }
      }
    } catch (error) {
      console.error('删除元素失败:', error);
      message.error('删除元素失败');
    }
  };

  // 更新元素样式
  const updateElementStyle = (property: string, value: string | number) => {
    if (!selectedElement || !iframeRef.current) return;

    try {
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) return;

      // 查找选中的元素
      const selectedDomElement = iframeDoc.querySelector('.element-highlight') as HTMLElement;
      if (selectedDomElement) {
        // 更新样式
        selectedDomElement.style[property as any] = value.toString();

        // 更新HTML内容
        setHtmlContent(iframeDoc.body.innerHTML);

        // 标记为已修改
        setIsContentModified(true);

        // 添加到历史记录
        addToHistory(iframeDoc.body.innerHTML);
      }
    } catch (error) {
      console.error('更新元素样式失败:', error);
      message.error('更新元素样式失败');
    }
  };

  // 添加到历史记录
  const addToHistory = (newContent: string) => {
    // 如果当前不是最新的历史记录，则删除之后的记录
    const newHistory = [...history.slice(0, historyIndex + 1), newContent];
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // 撤销操作
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setHtmlContent(history[historyIndex - 1]);
    }
  };

  // 重做操作
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setHtmlContent(history[historyIndex + 1]);
    }
  };

  // 放大
  const handleZoomIn = () => {
    if (zoomLevel < 200) {
      setZoomLevel(zoomLevel + 20);
    }
  };

  // 缩小
  const handleZoomOut = () => {
    if (zoomLevel > 60) {
      setZoomLevel(zoomLevel - 20);
    }
  };

  // 切换编辑模式
  const toggleEditMode = () => {
    if (editMode === 'visual') {
      // 从可视化模式切换到源代码模式
      setEditMode('source');
      setLoading(false); // 源代码模式无需加载
    } else {
      // 从源代码模式切换到可视化模式
      setEditMode('visual');
      setLoading(true); // 切换到可视化模式时开始加载
      // 更新历史记录
      addToHistory(htmlContent);
    }
  };

  // 显示预览
  const showPreview = () => {
    setPreviewVisible(true);
  };

  // 关闭预览
  const closePreview = () => {
    setPreviewVisible(false);
  };

  // 打开原始HTML编辑模态框
  const openRawHtmlEditor = (titleOrEvent?: string | React.MouseEvent<HTMLElement>, template?: string) => {
    if (!iframeRef.current) return;

    try {
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) return;

      // 如果有传入模板内容
      if (typeof titleOrEvent === 'string' && template) {
        // 设置模态框标题
        setModalTitle(titleOrEvent);
        // 如果是添加预设，将内容设置为模板
        setRawHtmlContent(template);
        setRawHtmlModalVisible(true);
        return;
      }

      // 默认标题
      setModalTitle("编辑原始HTML");
      
      // 获取选中元素的原始HTML
      const selectedDomElement = iframeDoc.querySelector('.element-highlight');
      if (selectedDomElement) {
        // 获取元素的outerHTML
        setRawHtmlContent(selectedDomElement.outerHTML);
        setRawHtmlModalVisible(true);
      } else {
        // 如果没有选中元素，则编辑整个body内容
        setRawHtmlContent(iframeDoc.body.innerHTML);
        setRawHtmlModalVisible(true);
      }
    } catch (error) {
      console.error('获取原始HTML失败:', error);
      message.error('获取原始HTML失败');
    }
  };

  // 关闭原始HTML编辑模态框
  const closeRawHtmlEditor = () => {
    setRawHtmlModalVisible(false);
  };

  // 应用原始HTML编辑
  const applyRawHtmlEdit = () => {
    if (!iframeRef.current) return;

    try {
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) return;

      // 获取选中元素
      const selectedDomElement = iframeDoc.querySelector('.element-highlight');

      if (selectedDomElement) {
        // 创建临时元素解析HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = rawHtmlContent;

        // 替换选中元素
        if (tempDiv.firstElementChild) {
          selectedDomElement.replaceWith(tempDiv.firstElementChild);

          // 更新HTML内容
          setHtmlContent(iframeDoc.body.innerHTML);

          // 标记为已修改
          setIsContentModified(true);

          // 添加到历史记录
          addToHistory(iframeDoc.body.innerHTML);

          message.success('HTML已更新');
        } else {
          message.error('无效的HTML内容');
        }
      } else {
        // 如果没有选中元素，则更新整个body内容
        iframeDoc.body.innerHTML = rawHtmlContent;

        // 更新HTML内容
        setHtmlContent(rawHtmlContent);

        // 标记为已修改
        setIsContentModified(true);

        // 添加到历史记录
        addToHistory(rawHtmlContent);

        message.success('整个页面HTML已更新');
      }

      // 关闭模态框
      setRawHtmlModalVisible(false);

      // 更新可视化编辑器
      updateVisualEditor();
    } catch (error) {
      console.error('应用HTML编辑失败:', error);
      message.error('应用HTML编辑失败');
    }
  };

  // 保存当前页编辑
  const handleSave = () => {
    try {
      // 确保内容不为空
      if (!htmlContent || htmlContent.trim() === '') {
        message.warning('内容不能为空');
        return;
      }

      // 确保有HTML代码块可以更新
      if (!htmlCodeBlocks || htmlCodeBlocks.length === 0 || currentPage >= htmlCodeBlocks.length) {
        message.error('未找到可更新的HTML代码块');
        return;
      }

      // 创建更新后的WorkflowGraphData副本
      const updatedWorkflowGraphData = { ...workflow_graph_data };
      const updatedCodeBlocks = [...(updatedWorkflowGraphData.code_blocks || [])];
      
      // 找到当前HTML代码块在原始code_blocks数组中的位置
      const currentHtmlBlock = htmlCodeBlocks[currentPage];
      const originalIndex = updatedCodeBlocks.findIndex(block => 
        block.content_id === currentHtmlBlock.content_id
      );
      
      if (originalIndex !== -1) {
        // 更新对应的代码块
        updatedCodeBlocks[originalIndex] = {
          ...updatedCodeBlocks[originalIndex],
          code: htmlContent
        };
        
        updatedWorkflowGraphData.code_blocks = updatedCodeBlocks;
        
        // 调用保存回调
        onSave(updatedWorkflowGraphData);

        // 显示保存成功消息
        if (isContentModified) {
          message.success(`第${currentPage + 1}页HTML内容已修改并保存成功`);
        } else {
          message.success(`第${currentPage + 1}页保存成功（无修改）`);
        }

        // 重置修改状态
        setIsContentModified(false);
      } else {
        message.error('无法找到对应的代码块进行更新');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 切换到上一页
  const goToPrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  // 切换到下一页
  const goToNextPage = () => {
    if (htmlCodeBlocks && currentPage < htmlCodeBlocks.length - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page - 1); // 页码从1开始，索引从0开始
  };

  // 渲染工具栏
  const renderToolbar = () => {
    return (
      <div className={styles.toolbar}>
        <Space>
          <Button
            icon={<UndoOutlined />}
            onClick={handleUndo}
            disabled={historyIndex <= 0}
            title="撤销"
          >
            撤销
          </Button>
          <Button
            icon={<RedoOutlined />}
            onClick={handleRedo}
            disabled={historyIndex >= history.length - 1}
            title="重做"
          >
            重做
          </Button>
          <Button
            icon={<EyeOutlined />}
            onClick={showPreview}
            title="预览"
          >
            预览
          </Button>
          <Button
            icon={editMode === 'visual' ? <CodeOutlined /> : <EditOutlined />}
            onClick={toggleEditMode}
            type={editMode === 'source' ? 'primary' : 'default'}
            title={editMode === 'visual' ? '源代码编辑' : '可视化编辑'}
          >
            {editMode === 'visual' ? '源代码编辑' : '可视化编辑'}
          </Button>
          <Button
            icon={<SaveOutlined />}
            type="primary"
            onClick={handleSave}
            title="保存"
          >
            保存
          </Button>
        </Space>

        <div className={styles.pageControls}>
          <Space size="small">
            <Button
              icon={<LeftOutlined />}
              onClick={goToPrevPage}
              disabled={currentPage <= 0}
              title="上一页"
              size="small"
              type="text"
            />
            <span style={{ margin: '0 4px', fontSize: '14px', color: '#333' }}>
              第 {currentPage + 1} 页 / 共 {htmlCodeBlocks?.length || 0} 页
            </span>
            <Button
              icon={<RightOutlined />}
              onClick={goToNextPage}
              disabled={!htmlCodeBlocks || currentPage >= htmlCodeBlocks.length - 1}
              title="下一页"
              size="small"
              type="text"
            />
            <Divider type="vertical" style={{ margin: '0 8px', height: '20px' }} />
            <Button
              icon={<ZoomOutOutlined />}
              onClick={handleZoomOut}
              disabled={zoomLevel <= 60}
              title="缩小"
              size="small"
              type="text"
            />
            <span style={{ minWidth: '50px', textAlign: 'center', fontSize: '14px' }}>
              {zoomLevel}%
            </span>
            <Button
              icon={<ZoomInOutlined />}
              onClick={handleZoomIn}
              disabled={zoomLevel >= 200}
              title="放大"
              size="small"
              type="text"
            />
          </Space>
        </div>

        <div style={{ marginLeft: 16, color: '#666' }}>
          {editMode === 'visual'
            ? '提示: 点击左侧预览区域中的任意元素进行编辑，然后在右侧面板中修改内容和样式'
            : '提示: 直接编辑HTML源代码，完成后点击"保存"按钮'}
        </div>
      </div>
    );
  };

  // 渲染源代码编辑器
  const renderSourceEditor = () => {
    return (
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', padding: '16px' }}>
        <TextArea
          value={htmlContent}
          onChange={(e) => {
            setHtmlContent(e.target.value);
            setIsContentModified(true);
          }}
          style={{
            flex: 1,
            minHeight: '650px',
            fontFamily: 'Consolas, Monaco, "Andale Mono", monospace',
            fontSize: '14px',
            lineHeight: '1.6',
            padding: '16px',
            backgroundColor: '#f8f8f8',
            border: '1px solid #e8e8e8',
            borderRadius: '4px'
          }}
          placeholder="在此输入HTML代码..."
        />
      </div>
    );
  };

  // 渲染可视化编辑器
  const renderVisualEditor = () => {
    return (
      <>
        {/* 左侧预览区域 */}
        <div className={styles.previewPanel}>
          <Spin spinning={loading} tip="正在加载页面内容..." size="large">
            <div style={{
              overflow: 'auto',
              height: '100%',
              padding: '10px 10px 30px 10px', /* Add extra padding at the bottom */
              backgroundColor: '#f0f0f0',
              display: 'flex',
              justifyContent: 'center',
              minWidth: '100%',
              overflowX: 'auto'
            }}>
              <iframe
                ref={iframeRef}
                className={styles.editorFrame}
                title="HTML编辑预览"
                sandbox="allow-same-origin allow-scripts"
                style={{
                  transform: `scale(${zoomLevel / 100})`,
                  transformOrigin: 'top center',
                  width: '1200px',
                  minWidth: '900px',
                  height: `${120 * (100 / zoomLevel)}%`, /* Increase height to ensure content is visible */
                  border: '1px solid #f0f0f0',
                  borderRadius: '4px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  marginBottom: '20px', /* Add margin at the bottom */
                  overflowX: 'visible'
                }}
              />
            </div>
          </Spin>
        </div>

        {/* 右侧属性面板 */}
        <div className={styles.propertiesPanel}>
          <Tabs defaultActiveKey="content">
            <TabPane tab="内容" key="content">
              <div className={styles.propertySection}>
                {selectedElement ? (
                  <Form layout="vertical">
                    <Form.Item label="HTML内容">
                      <TextArea
                        value={elementContent}
                        onChange={(e) => setElementContent(e.target.value)}
                        rows={6}
                        placeholder="输入HTML内容..."
                      />
                    </Form.Item>
                    <Form.Item>
                      <Space>
                        <Button type="primary" onClick={updateElementContent}>
                          更新内容
                        </Button>
                        <Button type="primary" onClick={openRawHtmlEditor} style={{ background: '#722ed1' }}>
                          编辑原始HTML
                        </Button>
                        {selectedElement.tagName?.toLowerCase() === 'div' && (
                          <Button danger onClick={deleteSelectedElement}>
                            删除此DIV
                          </Button>
                        )}
                      </Space>
                    </Form.Item>
                    {selectedElement.path && (
                      <div style={{ marginTop: 16, fontSize: 12, color: '#666' }}>
                        <div>当前选中: {selectedElement.path}</div>
                        <div>元素类型: {selectedElement.tagName}</div>
                      </div>
                    )}
                  </Form>
                ) : (
                  <div>
                    <p>请在左侧选择一个元素进行编辑</p>
                    <p style={{ marginTop: '16px', color: '#1890ff' }}>
                      提示: 如果无法选择元素，请点击上方的"源代码编辑"按钮，直接编辑HTML代码。
                    </p>
                    <Button
                      type="primary"
                      onClick={openRawHtmlEditor}
                      style={{ marginTop: '16px', background: '#722ed1' }}
                    >
                      编辑整个页面HTML
                    </Button>
                  </div>
                )}
              </div>
            </TabPane>

            {isEchartsElement && (
              <TabPane tab="图表数据" key="echarts">
                <div className={styles.propertySection}>
                  <Form layout="vertical">
                    <Form.Item label={`ECharts数据 (${echartsType}图表)`}>
                      <TextArea
                        value={echartsData}
                        rows={12}
                        readOnly
                        style={{
                          fontFamily: 'Consolas, Monaco, "Andale Mono", monospace',
                          fontSize: '13px',
                          backgroundColor: '#f5f5f5'
                        }}
                      />
                    </Form.Item>
                    <div style={{ marginTop: 8, color: '#666', fontSize: '13px' }}>
                      <p>提示: 这是当前图表的配置数据，您可以复制此数据进行修改。</p>
                      <p>修改后，请在源代码编辑模式下更新相应的script标签内容。</p>
                    </div>
                  </Form>
                </div>
              </TabPane>
            )}

            <TabPane tab="样式" key="style">
              <div className={styles.propertySection}>
                {selectedElement ? (
                  <Form layout="vertical">
                    <Form.Item label="文字颜色">
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ColorPicker
                          value={textColor}
                          onChange={(color) => {
                            const hexColor = color.toHexString();
                            setTextColor(hexColor);
                            updateElementStyle('color', hexColor);
                          }}
                        />
                        <span style={{ marginLeft: 8 }}>{textColor}</span>
                      </div>
                    </Form.Item>

                    <Form.Item label="背景颜色">
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ColorPicker
                          value={backgroundColor}
                          onChange={(color) => {
                            const hexColor = color.toHexString();
                            setBackgroundColor(hexColor);
                            updateElementStyle('backgroundColor', hexColor);
                          }}
                        />
                        <span style={{ marginLeft: 8 }}>{backgroundColor}</span>
                      </div>
                    </Form.Item>

                    <Form.Item label="字体大小">
                      <Slider
                        min={8}
                        max={72}
                        value={fontSize}
                        onChange={(value) => {
                          setFontSize(value);
                          updateElementStyle('fontSize', `${value}px`);
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{fontSize}px</div>
                    </Form.Item>

                    <Form.Item label="字体粗细">
                      <Select
                        value={fontWeight}
                        onChange={(value) => {
                          setFontWeight(value);
                          updateElementStyle('fontWeight', value);
                        }}
                        style={{ width: '100%' }}
                      >
                        <Option value="normal">正常</Option>
                        <Option value="bold">粗体</Option>
                        <Option value="lighter">细体</Option>
                        <Option value="bolder">更粗</Option>
                        <Option value="100">100</Option>
                        <Option value="200">200</Option>
                        <Option value="300">300</Option>
                        <Option value="400">400</Option>
                        <Option value="500">500</Option>
                        <Option value="600">600</Option>
                        <Option value="700">700</Option>
                        <Option value="800">800</Option>
                        <Option value="900">900</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item label="文本对齐">
                      <Radio.Group
                        value={textAlign}
                        onChange={(e) => {
                          setTextAlign(e.target.value);
                          updateElementStyle('textAlign', e.target.value);
                        }}
                      >
                        <Radio.Button value="left">左对齐</Radio.Button>
                        <Radio.Button value="center">居中</Radio.Button>
                        <Radio.Button value="right">右对齐</Radio.Button>
                        <Radio.Button value="justify">两端对齐</Radio.Button>
                      </Radio.Group>
                    </Form.Item>
                    
                    <Divider orientation="left">位置尺寸</Divider>
                    
                    {(selectedElement.tagName?.toLowerCase() === 'div' || 
                      selectedElement.tagName?.toLowerCase() === 'section' || 
                      selectedElement.tagName?.toLowerCase() === 'article') && (
                      <>
                        <Form.Item label="宽度">
                          <Space.Compact style={{ width: '100%' }}>
                            <Input 
                              style={{ width: '70%' }} 
                              placeholder="如: 300px 或 100%"
                              value={selectedElement.styles.width || ''}
                              onChange={(e) => {
                                updateElementStyle('width', e.target.value);
                              }}
                            />
                            <Button 
                              style={{ width: '30%' }} 
                              onClick={() => updateElementStyle('width', '100%')}
                            >
                              设为100%
                            </Button>
                          </Space.Compact>
                        </Form.Item>
                        
                        <Form.Item label="高度">
                          <Input 
                            placeholder="如: 200px 或 auto" 
                            value={selectedElement.styles.height || ''}
                            onChange={(e) => {
                              updateElementStyle('height', e.target.value);
                            }}
                          />
                        </Form.Item>
                        
                        <Form.Item label="水平对齐">
                          <Space style={{ width: '100%' }}>
                            <Button 
                              onClick={() => {
                                updateElementStyle('marginLeft', 'auto');
                                updateElementStyle('marginRight', 'auto');
                                updateElementStyle('display', 'block');
                              }}
                            >
                              居中
                            </Button>
                            <Button
                              onClick={() => {
                                updateElementStyle('marginLeft', '0');
                                updateElementStyle('marginRight', 'auto');
                              }}
                            >
                              左对齐
                            </Button>
                            <Button
                              onClick={() => {
                                updateElementStyle('marginLeft', 'auto');
                                updateElementStyle('marginRight', '0');
                              }}
                            >
                              右对齐
                            </Button>
                          </Space>
                        </Form.Item>
                      </>
                    )}
                    
                    <Form.Item label="左边距">
                      <Slider
                        min={0}
                        max={100}
                        value={margin}
                        onChange={(value) => {
                          setMargin(value);
                          updateElementStyle('marginLeft', `${value}px`);
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{margin}px</div>
                    </Form.Item>
                    
                    <Form.Item label="元素位置" tooltip="控制元素的定位方式">
                      <Radio.Group
                        onChange={(e) => updateElementStyle('position', e.target.value)}
                        value={selectedElement.styles.position || 'static'}
                      >
                        <Radio value="static">默认</Radio>
                        <Radio value="relative">相对定位</Radio>
                        <Radio value="absolute">绝对定位</Radio>
                        <Radio value="fixed">固定定位</Radio>
                      </Radio.Group>
                    </Form.Item>
                  </Form>
                ) : (
                  <div>
                    <p>请在左侧选择一个元素进行编辑</p>
                    <p style={{ marginTop: '16px', color: '#1890ff' }}>
                      提示: 如果无法选择元素，请点击上方的"源代码编辑"按钮，直接编辑HTML代码。
                    </p>
                  </div>
                )}
              </div>
            </TabPane>

            <TabPane tab="布局" key="layout">
              <div className={styles.propertySection}>
                {selectedElement ? (
                  <Form layout="vertical">
                    <Form.Item label="内边距 (padding)">
                      <Slider
                        min={0}
                        max={100}
                        value={padding}
                        onChange={(value) => {
                          setPadding(value);
                          updateElementStyle('padding', `${value}px`);
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{padding}px</div>
                    </Form.Item>
                    
                    <Form.Item label="四边内边距">
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                        <Button 
                          onClick={() => updateElementStyle('paddingTop', '10px')}
                        >
                          上内边距+10px
                        </Button>
                        <Button 
                          onClick={() => updateElementStyle('paddingBottom', '10px')}
                        >
                          下内边距+10px
                        </Button>
                        <Button 
                          onClick={() => updateElementStyle('paddingLeft', '10px')}
                        >
                          左内边距+10px
                        </Button>
                        <Button 
                          onClick={() => updateElementStyle('paddingRight', '10px')}
                        >
                          右内边距+10px
                        </Button>
                      </div>
                    </Form.Item>

                    <Form.Item label="外边距 (margin)">
                      <Slider
                        min={0}
                        max={100}
                        value={margin}
                        onChange={(value) => {
                          setMargin(value);
                          updateElementStyle('margin', `${value}px`);
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{margin}px</div>
                    </Form.Item>
                    
                    <Form.Item label="四边外边距">
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                        <Button 
                          onClick={() => updateElementStyle('marginTop', '10px')}
                        >
                          上外边距+10px
                        </Button>
                        <Button 
                          onClick={() => updateElementStyle('marginBottom', '10px')}
                        >
                          下外边距+10px
                        </Button>
                        <Button 
                          onClick={() => updateElementStyle('marginLeft', '10px')}
                        >
                          左外边距+10px
                        </Button>
                        <Button 
                          onClick={() => updateElementStyle('marginRight', '10px')}
                        >
                          右外边距+10px
                        </Button>
                      </div>
                    </Form.Item>

                    <Form.Item label="圆角 (border-radius)">
                      <Slider
                        min={0}
                        max={50}
                        value={borderRadius}
                        onChange={(value) => {
                          setBorderRadius(value);
                          updateElementStyle('borderRadius', `${value}px`);
                        }}
                      />
                      <div style={{ marginTop: 8 }}>{borderRadius}px</div>
                    </Form.Item>
                    
                    <Divider orientation="left">边框样式</Divider>
                    
                    <Form.Item label="边框宽度">
                      <Slider
                        min={0}
                        max={10}
                        defaultValue={1}
                        onChange={(value) => updateElementStyle('borderWidth', `${value}px`)}
                      />
                    </Form.Item>
                    
                    <Form.Item label="边框颜色">
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ColorPicker
                          defaultValue="#cccccc"
                          onChange={(color) => {
                            const hexColor = color.toHexString();
                            updateElementStyle('borderColor', hexColor);
                          }}
                        />
                      </div>
                    </Form.Item>
                    
                    <Form.Item label="边框样式">
                      <Select
                        defaultValue="solid"
                        style={{ width: '100%' }}
                        onChange={(value) => updateElementStyle('borderStyle', value)}
                      >
                        <Option value="none">无边框</Option>
                        <Option value="solid">实线</Option>
                        <Option value="dashed">虚线</Option>
                        <Option value="dotted">点线</Option>
                        <Option value="double">双实线</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item label="快速边框">
                      <Space>
                        <Button 
                          onClick={() => {
                            updateElementStyle('border', '1px solid #cccccc');
                          }}
                        >
                          标准边框
                        </Button>
                        <Button 
                          onClick={() => {
                            updateElementStyle('border', '1px solid #1890ff');
                          }}
                        >
                          蓝色边框
                        </Button>
                        <Button 
                          onClick={() => {
                            updateElementStyle('boxShadow', '0 2px 8px rgba(0, 0, 0, 0.15)');
                          }}
                        >
                          阴影效果
                        </Button>
                      </Space>
                    </Form.Item>
                  </Form>
                ) : (
                  <div>
                    <p>请在左侧选择一个元素进行编辑</p>
                    <p style={{ marginTop: '16px', color: '#1890ff' }}>
                      提示: 如果无法选择元素，请点击上方的"源代码编辑"按钮，直接编辑HTML代码。
                    </p>
                  </div>
                )}
              </div>
            </TabPane>

            <TabPane tab="布局预设" key="layout-presets">
              <div className={styles.propertySection}>
                <Form layout="vertical">
                  <Form.Item label="添加新元素">
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                      <Button onClick={() => openRawHtmlEditor('添加新标题', '<h2 style="color: #333; margin: 20px 0;">新标题文本</h2>')}>
                        添加标题
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加段落', '<p style="margin: 10px 0; line-height: 1.6;">这是一个新段落，请在这里编辑您的内容。</p>')}>
                        添加段落
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加分隔线', '<hr style="border: 0; height: 1px; background: #ddd; margin: 20px 0;" />')}>
                        添加分隔线
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加图片', '<div style="text-align: center; margin: 20px 0;"><img src="https://via.placeholder.com/400x200" alt="图片描述" style="max-width: 100%; height: auto;" /></div>')}>
                        添加图片
                      </Button>
                    </div>
                  </Form.Item>
                  
                  <Divider orientation="left">容器布局</Divider>
                  
                  <Form.Item label="常用布局容器">
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                      <Button onClick={() => openRawHtmlEditor('添加卡片容器', '<div style="border: 1px solid #eee; border-radius: 8px; padding: 15px; margin: 15px 0; background-color: #fff; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">\n  <h3 style="margin-top: 0; color: #333;">卡片标题</h3>\n  <p>这里是卡片内容，您可以在这里添加描述文本。</p>\n</div>')}>
                        卡片容器
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加两列布局', '<div style="display: flex; gap: 20px; margin: 15px 0;">\n  <div style="flex: 1; padding: 10px; border: 1px solid #eee; border-radius: 5px;">\n    <h3>左侧内容</h3>\n    <p>这是左侧列的内容</p>\n  </div>\n  <div style="flex: 1; padding: 10px; border: 1px solid #eee; border-radius: 5px;">\n    <h3>右侧内容</h3>\n    <p>这是右侧列的内容</p>\n  </div>\n</div>')}>
                        两列布局
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加引用块', '<blockquote style="border-left: 4px solid #1890ff; margin-left: 0; padding-left: 15px; color: #666; font-style: italic;">\n  <p>这是一个引用块，通常用于引用他人的话或强调某些内容。</p>\n</blockquote>')}>
                        引用块
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加提示框', '<div style="background-color: #e6f7ff; border: 1px solid #91d5ff; border-radius: 5px; padding: 10px 15px; margin: 15px 0;">\n  <h4 style="color: #1890ff; margin-top: 0;">提示</h4>\n  <p style="margin-bottom: 0;">这是一个提示框，用于显示重要信息或提示。</p>\n</div>')}>
                        提示框
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加列表', '<ul style="margin-left: 20px;">\n  <li style="margin-bottom: 8px;">列表项目一</li>\n  <li style="margin-bottom: 8px;">列表项目二</li>\n  <li style="margin-bottom: 8px;">列表项目三</li>\n</ul>')}>
                        无序列表
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加步骤', '<ol style="margin-left: 20px;">\n  <li style="margin-bottom: 8px;">第一步：开始</li>\n  <li style="margin-bottom: 8px;">第二步：处理</li>\n  <li style="margin-bottom: 8px;">第三步：完成</li>\n</ol>')}>
                        有序列表
                      </Button>
                    </div>
                  </Form.Item>
                  
                  <Form.Item label="内容组件">
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                      <Button onClick={() => openRawHtmlEditor('添加表格', '<table style="width: 100%; border-collapse: collapse; margin: 15px 0;">\n  <thead>\n    <tr style="background-color: #f7f7f7;">\n      <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">表头1</th>\n      <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">表头2</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td style="border: 1px solid #ddd; padding: 8px;">内容1</td>\n      <td style="border: 1px solid #ddd; padding: 8px;">内容2</td>\n    </tr>\n    <tr style="background-color: #f9f9f9;">\n      <td style="border: 1px solid #ddd; padding: 8px;">内容3</td>\n      <td style="border: 1px solid #ddd; padding: 8px;">内容4</td>\n    </tr>\n  </tbody>\n</table>')}>
                        表格
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加按钮', '<button style="background-color: #1890ff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">按钮文本</button>')}>
                        按钮
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加代码块', '<pre style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; font-family: monospace; font-size: 14px; line-height: 1.4;">\n  <code>\n// 这是一个代码示例\nfunction example() {\n  console.log("Hello, world!");\n}\n  </code>\n</pre>')}>
                        代码块
                      </Button>
                      <Button onClick={() => openRawHtmlEditor('添加标注', '<div style="display: flex; margin: 15px 0;">\n  <div style="flex: 0 0 80px; color: #1890ff; font-weight: bold; text-align: right; padding-right: 15px;">标注：</div>\n  <div style="flex: 1;">这里是标注内容，可以用于解释或补充说明某个概念。</div>\n</div>')}>
                        标注
                      </Button>
                    </div>
                  </Form.Item>
                </Form>
              </div>
            </TabPane>
          </Tabs>
        </div>
      </>
    );
  };

  return (
    <Modal
      title={`PDF HTML编辑器 - 第 ${currentPage + 1} 页 / 共 ${htmlCodeBlocks?.length || 0} 页`}
      open={visible}
      onCancel={onCancel}
      width="95%"
      style={{ top: 10 }}
      footer={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: '10px', color: '#666' }}>页面导航:</span>
            <Pagination
              simple
              current={currentPage + 1}
              total={htmlCodeBlocks?.length || 0}
              pageSize={1}
              onChange={handlePageChange}
              disabled={loading}
              size="small"
              style={{ marginRight: '10px' }}
            />
            <span style={{ color: '#666', fontSize: '13px' }}>
              (共 {htmlCodeBlocks?.length || 0} 页)
            </span>
          </div>
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" onClick={handleSave} disabled={loading}>
              保存当前页
            </Button>
          </Space>
        </div>
      }
      styles={{
        body: {
          height: 'calc(95vh - 120px)',
          padding: 0,
          overflow: 'hidden',
          marginBottom: '10px' // Add margin to ensure content is not cut off
        }
      }}
      destroyOnHidden
    >
      <div className={styles.editorContainer}>
        {/* 工具栏 */}
        {renderToolbar()}

        {/* 编辑区域 */}
        <div className={styles.editorContent}>
          {editMode === 'source' ? renderSourceEditor() : renderVisualEditor()}
        </div>
      </div>

      {/* 预览模态框 */}
      <Modal
        title={`PDF HTML 预览 - 第 ${currentPage + 1} 页`}
        open={previewVisible}
        onCancel={closePreview}
        width="90%"
        styles={{
          body: { padding: '20px', maxHeight: '80vh', overflow: 'auto' },
          mask: { backgroundColor: 'rgba(0, 0, 0, 0.65)' }
        }}
        footer={<Button onClick={closePreview}>关闭预览</Button>}
      >
        <div className={styles.previewContainer}>
          <iframe
            srcDoc={htmlContent}
            className={styles.previewFrame}
            title="HTML内容预览"
            style={{ width: '100%', height: '75vh', border: 'none', backgroundColor: 'white' }}
          />
        </div>
      </Modal>

      {/* 原始HTML编辑模态框 */}
      <Modal
        title={modalTitle}
        open={rawHtmlModalVisible}
        onCancel={closeRawHtmlEditor}
        width="90%"
        styles={{
          body: { padding: '20px', maxHeight: '80vh', overflow: 'auto' },
          mask: { backgroundColor: 'rgba(0, 0, 0, 0.65)' }
        }}
        footer={
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Space>
              <Button onClick={closeRawHtmlEditor}>取消</Button>
              <Button type="primary" onClick={applyRawHtmlEdit}>
                应用HTML修改
              </Button>
            </Space>
          </div>
        }
      >
        <div style={{ marginBottom: '16px', color: '#666' }}>
          <p>在下方编辑原始HTML代码。您可以直接修改HTML标签、属性和内容。</p>
          <p style={{ color: '#1890ff' }}>提示: 确保HTML代码格式正确，否则可能导致渲染错误。</p>
        </div>
        <TextArea
          value={rawHtmlContent}
          onChange={(e) => setRawHtmlContent(e.target.value)}
          style={{
            width: '100%',
            minHeight: '500px',
            fontFamily: 'Consolas, Monaco, "Andale Mono", monospace',
            fontSize: '14px',
            lineHeight: '1.6',
            padding: '16px',
            backgroundColor: '#f8f8f8',
            border: '1px solid #e8e8e8',
            borderRadius: '4px'
          }}
          placeholder="在此输入HTML代码..."
        />
      </Modal>
    </Modal>
  );
};

export default PdfHtmlEditor;