.sidebar {
  width: 240px;
  height: 100%;
  border-right: 1px solid #e8e8e8;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
}

.collapsedSidebar {
  width: 48px;
  height: 100%;
  border-right: 1px solid #e8e8e8;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 16px;
  transition: all 0.3s;
}

.sidebarHeader {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.collapseButton {
  height: 30px;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.sidebarContent {
  flex: 1;
  overflow-y: auto;
  padding: 12px 0;
}

.workflowItem {
  padding: 8px 16px !important;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workflowItem:hover {
  background-color: #eaeaea;
}

.workflowInfo {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.workflowDate {
  font-size: 12px;
  margin-top: 4px;
}

.workflowArrow {
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s, transform 0.3s;
}

.workflowItem:hover .workflowArrow {
  opacity: 1;
  transform: translateX(3px);
}

.paginationContainer {
  padding: 12px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e8e8e8;
}

.workflowActions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.deleteButton {
  opacity: 0;
  transition: opacity 0.3s;
}

.workflowItem:hover .deleteButton {
  opacity: 1;
} 