/* src/components/Logout.module.css */

.logoutBackground {
    background: linear-gradient(135deg, #00aaff, #00ffaa); /* 蓝绿渐变背景 */
    height: 100vh; /* 全屏高度 */
    width: 100vw;  /* 全屏宽度 */
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center;     /* 垂直居中 */
    position: relative;
}

.logoutContainer {
    background-color: #e0f7fa; /* 浅蓝色背景 */
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 90%;
    box-sizing: border-box;
    text-align: center;
    animation: fadeIn 0.5s ease-out;
}

.logoutMessage {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
}

/* 淡入动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}
