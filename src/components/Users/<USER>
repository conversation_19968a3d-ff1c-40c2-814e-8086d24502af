// src/components/LoginForm.tsx
import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styles from './LoginForm.module.css'; // 导入 CSS 模块
import { REGISTER_API_NAME, RESET_PASSWORD_API_NAME } from '../../Constant/Constant'; // 导入重置密码路由
import { setUserInfo } from './userSlice';
import { useDispatch } from 'react-redux';
import { login } from '../api/auth'; // 正确导入 login 函数
import { FaUser, FaLock } from 'react-icons/fa'; // 导入图标

interface LoginFormProps {
    prefillUsername?: string;
    prefillPassword?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ prefillUsername = '', prefillPassword = '' }) => {
    const [username, setUsername] = useState(prefillUsername);
    const [password, setPassword] = useState(prefillPassword);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(false); // 添加加载状态
    const location = useLocation();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const handleGoToRegister = () => {
        navigate(`${REGISTER_API_NAME}`); // 使用注册页面的路由路径
    };

    const handleGoToResetPassword = () => {
        navigate(`${RESET_PASSWORD_API_NAME}`); // 使用重置密码页面的路由路径
    };

    const handleLogin = async (event: React.FormEvent) => {
        event.preventDefault();
        setError(null);

        setLoading(true); // 开始加载
        try {
            const login_info = await login(username, password);
            // 更新 Redux（同时存储 token 已由 userSlice 处理）
            dispatch(setUserInfo(login_info));
            const from = (location.state as { from?: string })?.from || '/chat/new/new?workflow_or_agent=workflow'; // 默认跳转到聊天界面
            navigate(from, { replace: true });
        } catch (error: any) {
            console.error('Login attempt failed:', error);
            setError(error.message || '登录失败，请检查用户名和密码。');
        } finally {
            setLoading(false); // 结束加载
        }
    };

    return (
        <div className={styles.loginBackground}>
            <div className={styles.loginContainer}>
                <h2 className={styles.loginTitle}>欢迎使用管理系统</h2>
                <form onSubmit={handleLogin} className={styles.loginForm}>
                    <div className={styles.inputContainer}>
                        <FaUser className={styles.icon} />
                        <input
                            id="username"
                            type="text"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            placeholder="用户名"
                            className={styles.loginInput}
                            required
                        />
                    </div>
                    <div className={styles.inputContainer}>
                        <FaLock className={styles.icon} />
                        <input
                            id="password"
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder="密码"
                            className={styles.loginInput}
                            required
                        />
                    </div>
                    {error && <div className={styles.errorMessage}>{error}</div>} {/* 显示错误消息 */}
                    <button type="submit" className={styles.loginButton} disabled={loading}>
                        {loading ? '登录中...' : '登录'}
                    </button>
                    <button type="button" onClick={handleGoToRegister} className={styles.toggleButton}>
                        无账户？注册
                    </button>
                    <button type="button" onClick={handleGoToResetPassword} className={styles.toggleButton}>
                        忘记密码？重置密码
                    </button>
                </form>
            </div>
        </div>
    );
};

export default LoginForm;
