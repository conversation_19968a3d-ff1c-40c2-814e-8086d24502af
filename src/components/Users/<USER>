// src/components/ResetPasswordForm.tsx
import React, { useState } from "react";
import { resetPassword } from "../api/user";
import { useNavigate } from "react-router-dom";
import styles from "./ResetPasswordForm.module.css"; // 导入 CSS 模块
import { FaLock, FaKey } from 'react-icons/fa'; // 导入图标
import { LOGIN_API_NAME } from '../../Constant/Constant'; // 导入路由常量

const ResetPasswordForm: React.FC = () => {
    const [oldPassword, setOldPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState(""); // 新增确认密码状态
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    const userId = 1; // TODO: 根据实际情况获取用户 ID，建议通过认证上下文或 Redux 获取

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);

        // 验证新密码与确认密码是否一致
        if (newPassword !== confirmPassword) {
            setError("新密码与确认密码不一致。");
            return;
        }

        // 可选：添加密码强度验证
        if (newPassword.length < 6) {
            setError("新密码长度至少为6个字符。");
            return;
        }

        setLoading(true);
        try {
            await resetPassword(userId, { old_password: oldPassword, new_password: newPassword });
            alert("密码重置成功！");
            navigate(LOGIN_API_NAME); // 重置成功后跳转到登录页面
        } catch (error: any) {
            console.error('Password reset failed:', error);
            setError(error.message || "密码重置失败，请重试。");
        } finally {
            setLoading(false);
        }
    };

    const handleGoToLogin = () => {
        navigate(LOGIN_API_NAME); // 导航到登录页面
    };

    return (
        <div className={styles.resetPasswordBackground}>
            <div className={styles.resetPasswordContainer}>
                <h2 className={styles.resetPasswordTitle}>重置密码</h2>
                <form onSubmit={handleSubmit} className={styles.resetPasswordForm}>
                    <div className={styles.inputContainer}>
                        <FaLock className={styles.icon} />
                        <input
                            type="password"
                            placeholder="旧密码"
                            value={oldPassword}
                            onChange={(e) => setOldPassword(e.target.value)}
                            className={styles.resetPasswordInput}
                            required
                        />
                    </div>
                    <div className={styles.inputContainer}>
                        <FaKey className={styles.icon} />
                        <input
                            type="password"
                            placeholder="新密码"
                            value={newPassword}
                            onChange={(e) => setNewPassword(e.target.value)}
                            className={styles.resetPasswordInput}
                            required
                        />
                    </div>
                    <div className={styles.inputContainer}>
                        <FaKey className={styles.icon} />
                        <input
                            type="password"
                            placeholder="确认新密码"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            className={styles.resetPasswordInput}
                            required
                        />
                    </div>
                    {error && <div className={styles.errorMessage}>{error}</div>} {/* 显示错误消息 */}
                    <button type="submit" className={styles.resetPasswordButton} disabled={loading}>
                        {loading ? "重置中..." : "重置密码"}
                    </button>
                </form>
                <button onClick={handleGoToLogin} className={styles.backToLoginButton}>
                    返回登录页面
                </button>
            </div>
        </div>
    );
};

export default ResetPasswordForm;
