/* src/components/ResetPasswordForm.module.css */

/* 定义CSS变量，用于颜色主题 */
:root {
    --color-bg: #ffffff;
    --color-text: #333333;
    --color-primary: #007BFF;
    --color-border: #cccccc;
    --color-bg-form: #e0f7fa; /* 浅蓝色背景 */
    --color-error: #ff4d4f;
}

/* 暗色模式下的颜色变量 */
@media (prefers-color-scheme: dark) {
    :root {
        --color-bg: #1a1a1a;
        --color-text: #f0f0f0;
        --color-primary: #3399FF;
        --color-border: #555555;
        --color-bg-form: #004d40; /* 深色模式下的浅蓝色背景 */
        --color-error: #ff7875;
    }
}

/* 容器填满全屏并使用Flexbox居中内容 */
.resetPasswordBackground {
    background: linear-gradient(135deg, #00aaff, #00ffaa); /* 蓝绿渐变背景 */
    height: 100vh; /* 全屏高度 */
    width: 100vw;  /* 全屏宽度 */
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center;     /* 垂直居中 */
    position: relative;
}

/* 表单窗口样式 */
.resetPasswordContainer {
    background-color: var(--color-bg-form);
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px; /* 最大宽度 */
    height: auto; /* 高度自适应 */
    max-height: 90%; /* 防止超出视口 */
    box-sizing: border-box;
    overflow-y: auto; /* 允许垂直滚动 */
    animation: fadeIn 0.5s ease-out;
}

/* 标题样式 */
.resetPasswordTitle {
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
    color: var(--color-text);
}

/* 表单布局 */
.resetPasswordForm {
    display: flex;
    flex-direction: column;
}

/* 每行表单元素 */
.inputContainer {
    position: relative;
    margin-bottom: 15px;
}

/* 图标样式 */
.icon {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    color: var(--color-border);
}

/* 输入框样式 */
.resetPasswordInput {
    width: 100%;
    padding: 10px 15px 10px 35px; /* 为图标留出空间 */
    font-size: 16px;
    color: var(--color-text);
    background-color: #ffffff; /* 固定背景颜色为白色 */
    border: 1px solid var(--color-border);
    border-radius: 5px;
    outline: none;
    transition: border-color 0.3s;
}

.resetPasswordInput:focus {
    border-color: var(--color-primary);
}

/* 提交按钮样式 */
.resetPasswordButton {
    background-color: var(--color-primary);
    color: #ffffff;
    padding: 12px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.resetPasswordButton:hover {
    background-color: #0056b3;
}

.resetPasswordButton:disabled {
    background-color: #a0c4ff;
    cursor: not-allowed;
}

/* 切换按钮样式（可选） */
.toggleButton {
    margin-top: 10px;
    background: none;
    border: none;
    color: var(--color-primary);
    cursor: pointer;
    font-size: 14px;
    text-decoration: underline;
    transition: color 0.3s;
}

.toggleButton:hover {
    color: #0056b3;
}

/* 错误消息样式 */
.errorMessage {
    color: var(--color-error);
    margin-bottom: 10px;
    font-size: 14px;
    text-align: center;
}

/* 淡入动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 600px) {
    .resetPasswordContainer {
        padding: 20px;
    }
    .resetPasswordTitle {
        font-size: 20px;
    }
    .resetPasswordInput {
        font-size: 14px;
        padding: 8px 12px 8px 30px;
    }
    .resetPasswordButton {
        font-size: 14px;
        padding: 10px;
    }
}

.backToLoginButton {
    background: none;
    border: none;
    color: var(--color-primary);
    font-size: 14px;
    cursor: pointer;
    text-decoration: underline;
    margin-top: 10px;
    transition: color 0.3s;
}

.backToLoginButton:hover {
    color: #0056b3;
}
