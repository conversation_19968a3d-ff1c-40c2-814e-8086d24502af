// src/store/users/userSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 定义用户信息接口
interface UserInfo {
    access_token: string;
    username: string;
    token_type: string;
    role: string;
}

// 定义用户状态接口
interface UserState {
    userInfo: UserInfo | null;
}

// 从 localStorage 中获取初始 token（如果存在）
const token = localStorage.getItem('access_token');
const token_type = localStorage.getItem('token_type') || '';
const username = localStorage.getItem('username') || '';
const role = localStorage.getItem('role') || '';
const initialState: UserState = {
    userInfo: token ? { access_token: token, username: username, token_type: token_type, role: role } : null,
};

// 创建用户 Slice
const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        // 设置用户信息并存储 token
        setUserInfo(state, action: PayloadAction<UserInfo>) {
            state.userInfo = action.payload;
            // console.log('action.payload', action.payload);
            localStorage.setItem('username', state.userInfo.username);
            localStorage.setItem('role', state.userInfo.role);
            localStorage.setItem('token_type', state.userInfo.token_type);
            localStorage.setItem('access_token', action.payload.access_token);
        },
        // 清除用户信息并移除 token
        logout(state) {
            state.userInfo = null;
            localStorage.removeItem('access_token');
        },
    },
});

// 导出动作
export const { setUserInfo, logout } = userSlice.actions;

// 导出 reducer
export default userSlice.reducer;
