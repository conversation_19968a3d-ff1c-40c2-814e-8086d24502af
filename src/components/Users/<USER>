// src/components/RegisterForm.tsx
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { register } from "../api/user";  
import styles from "./RegisterForm.module.css";  // 引入CSS模块
import { LOGIN_API_NAME } from "../../Constant/Constant";
import { FaUser, FaEnvelope, FaLock } from 'react-icons/fa'; // 导入图标

const RegisterForm: React.FC = () => {
    const [username, setUsername] = useState("");
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [error, setError] = useState<string | null>(null); 
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        setError(null);

        if (password !== confirmPassword) {
            setError("两次输入的密码不一致");
            return;
        }

        setLoading(true);
        try {
            await register({ username, email, password });
            alert("注册成功！");
            navigate(LOGIN_API_NAME); // 注册成功后跳转到登录页面
        } catch (error: any) {
            setError(error.message || "注册失败！");
        } finally {
            setLoading(false);
        }
    };

    const handleGoToLogin = () => {
        navigate(LOGIN_API_NAME); // 导航到登录页面
    };

    return (
        <div className={styles.registerContainer}>
            <div className={styles.formContainer}>
                <h2 className={styles.formTitle}>注册</h2>
                <form onSubmit={handleSubmit} className={styles.form}>
                    <div className={styles.formRow}>
                        <label className={styles.formLabel}>用户名:</label>
                        <div className={styles.inputWrapper}>
                            <FaUser className={styles.icon} />
                            <input
                                type="text"
                                placeholder="用户名"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                className={styles.formInput}
                                required
                            />
                        </div>
                    </div>
                    <div className={styles.formRow}>
                        <label className={styles.formLabel}>邮箱:</label>
                        <div className={styles.inputWrapper}>
                            <FaEnvelope className={styles.icon} />
                            <input
                                type="email"
                                placeholder="邮箱"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                className={styles.formInput}
                                required
                            />
                        </div>
                    </div>
                    <div className={styles.formRow}>
                        <label className={styles.formLabel}>密码:</label>
                        <div className={styles.inputWrapper}>
                            <FaLock className={styles.icon} />
                            <input
                                type="password"
                                placeholder="密码"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                className={styles.formInput}
                                required
                            />
                        </div>
                    </div>
                    <div className={styles.formRow}>
                        <label className={styles.formLabel}>确认密码:</label>
                        <div className={styles.inputWrapper}>
                            <FaLock className={styles.icon} />
                            <input
                                type="password"
                                placeholder="确认密码"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                className={styles.formInput}
                                required
                            />
                        </div>
                    </div>
                    {error && <div className={styles.errorMessage}>{error}</div>} {/* 显示错误消息 */}
                    <button type="submit" className={styles.submitButton} disabled={loading}>
                        {loading ? "注册中..." : "注册"}
                    </button>
                </form>
                <button type="button" onClick={handleGoToLogin} className={styles.backToLoginButton}>
                    返回登录页面
                </button>
            </div>
        </div>
    );
};

export default RegisterForm;
