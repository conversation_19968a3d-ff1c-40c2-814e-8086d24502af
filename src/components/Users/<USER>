/* src/components/RegisterForm.module.css */

/* 定义CSS变量，用于颜色主题 */
:root {
    --color-bg-form: #e0f7fa; /* 淡蓝色背景 */
    --color-text: #333333;
    --color-primary: #007BFF;
    --color-border: #cccccc;
    --color-error: #ff4d4f;
}

@media (prefers-color-scheme: dark) {
    :root {
        --color-bg-form: #004d40; /* 深色模式下的淡蓝色背景 */
        --color-text: #f0f0f0;
        --color-primary: #3399FF;
        --color-border: #555555;
        --color-error: #ff7875;
    }
}

/* 容器填满全屏并使用Flexbox居中内容 */
.registerContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #28a745, #ffffff); /* 绿白渐变背景 */
    height: 100vh; /* 全屏高度 */
    width: 100vw;  /* 全屏宽度 */
    padding: 20px; /* 为小屏设备添加内边距 */
    box-sizing: border-box;
}

/* 表单容器样式 */
.formContainer {
    background-color: var(--color-bg-form);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
    animation: fadeIn 0.5s ease-out;
}

/* 标题样式 */
.formTitle {
    text-align: center;
    margin-bottom: 20px;
    color: var(--color-text);
    font-size: 24px;
}

/* 表单布局 */
.form {
    display: flex;
    flex-direction: column;
}

/* 每行表单元素 */
.formRow {
    margin-bottom: 15px;
}

/* 标签样式 */
.formLabel {
    display: block;
    margin-bottom: 8px;
    color: var(--color-text);
    font-weight: bold;
}

/* 输入框容器，用于图标和输入框 */
.inputWrapper {
    position: relative;
}

/* 图标样式 */
.icon {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    color: var(--color-border);
}

/* 输入框样式 */
.formInput {
    width: 100%;
    padding: 10px 15px 10px 35px; /* 为图标留出空间 */
    font-size: 16px;
    color: var(--color-text);
    background-color: #ffffff; /* 固定背景颜色为白色 */
    border: 1px solid var(--color-border);
    border-radius: 5px;
    outline: none;
    transition: border-color 0.3s;
}

.formInput:focus {
    border-color: var(--color-primary);
}

/* 提交按钮样式 */
.submitButton {
    background-color: var(--color-primary);
    color: #ffffff;
    padding: 12px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    margin-top: 10px;
    transition: background-color 0.3s;
}

.submitButton:hover {
    background-color: #0056b3;
}

.submitButton:disabled {
    background-color: #a0c4ff;
    cursor: not-allowed;
}

/* 错误消息样式 */
.errorMessage {
    color: var(--color-error);
    margin-bottom: 10px;
    font-size: 14px;
    text-align: center;
}
/* 返回登录按钮样式 */
.backToLoginButton {
    background: none;
    border: none;
    color: var(--color-primary);
    font-size: 14px;
    cursor: pointer;
    text-decoration: underline;
    margin-top: 10px;
    transition: color 0.3s;
}

.backToLoginButton:hover {
    color: #0056b3;
}
/* 淡入动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 600px) {
    .formContainer {
        padding: 20px;
    }
    .formTitle {
        font-size: 20px;
    }
    .formInput {
        font-size: 14px;
        padding: 8px 12px 8px 30px;
    }
    .submitButton {
        font-size: 14px;
        padding: 10px;
    }
}
