// src/components/Logout.tsx
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import styles from './Logout.module.css';
import { logout } from './userSlice';

const Logout: React.FC = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    useEffect(() => {
        // 执行登出逻辑
        dispatch(logout());
        // 重定向到登录页面
        navigate('/login', { replace: true });
    }, [dispatch, navigate]);

    return (
        <div className={styles.logoutBackground}>
            <div className={styles.logoutContainer}>
                <h2 className={styles.logoutMessage}>正在登出...</h2>
                {/* 可以添加一个加载指示器 */}
            </div>
        </div>
    );
};

export default Logout;
