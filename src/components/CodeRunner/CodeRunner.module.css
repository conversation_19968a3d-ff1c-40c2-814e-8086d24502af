.container {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 600px;
  height: 100%;
}

.errorContainer {
  color: #dc3545;
  padding: 12px;
  background-color: #fff5f5;
  border-bottom: 1px solid #ffd7d7;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}

.iframe {
  border: none;
  background-color: #ffffff;
  flex-grow: 1;
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #666;
}

/* 编辑模式样式 */
.editorSection {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  flex: 1;
}

.codeEditor {
  flex: 1;
  padding: 15px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.6;
  border: none;
  resize: none;
  background-color: #f8f9fa;
  outline: none;
  overflow: auto;
  width: 100%;
  min-height: 400px;
}

.buttonContainer {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f0f0;
  border-top: 1px solid #e0e0e0;
  width: 100%;
}

.leftButtons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.runButton {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.runButton:hover {
  background-color: #40a9ff;
}

.typeSelector {
  display: flex;
  gap: 12px;
}

.typeSelector label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.typeSelector input {
  margin: 0;
}

.exampleButtons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.exampleButton {
  background-color: #52c41a;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
}

.exampleButton:hover {
  background-color: #73d13d;
}

/* 结果模式样式 */
.resultSection {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
}

.editButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 10;
  opacity: 0.6;
}

.editButton:hover {
  opacity: 1;
  background-color: white;
  border-color: #1890ff;
  color: #1890ff;
} 