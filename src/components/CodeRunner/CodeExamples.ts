// 登录页面HTML示例代码
export const loginHtmlExample = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        
        .login-container {
            background-color: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            border-color: #4285f4;
            outline: none;
        }
        
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
        }
        
        .remember-me input {
            margin-right: 8px;
        }
        
        .forgot-password a {
            color: #4285f4;
            text-decoration: none;
        }
        
        .login-button {
            width: 100%;
            padding: 12px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .login-button:hover {
            background-color: #3367d6;
        }
        
        .register-link {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }
        
        .register-link a {
            color: #4285f4;
            text-decoration: none;
            margin-left: 5px;
        }
        
        .error-message {
            color: #d32f2f;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>欢迎登录</h1>
            <p>请输入您的账号和密码</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                <div class="error-message" id="username-error"></div>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" placeholder="请输入密码" required>
                <div class="error-message" id="password-error"></div>
            </div>
            
            <div class="remember-forgot">
                <div class="remember-me">
                    <input type="checkbox" id="remember" name="remember">
                    <label for="remember">记住我</label>
                </div>
                <div class="forgot-password">
                    <a href="#">忘记密码?</a>
                </div>
            </div>
            
            <button type="submit" class="login-button">登录</button>
        </form>
        
        <div class="register-link">
            还没有账号?<a href="#">立即注册</a>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 获取输入值
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('remember').checked;
            
            // 简单的验证
            const usernameError = document.getElementById('username-error');
            const passwordError = document.getElementById('password-error');
            
            let isValid = true;
            
            // 清空错误信息
            usernameError.style.display = 'none';
            passwordError.style.display = 'none';
            
            if (username === '') {
                usernameError.textContent = '请输入用户名';
                usernameError.style.display = 'block';
                isValid = false;
            }
            
            if (password === '') {
                passwordError.textContent = '请输入密码';
                passwordError.style.display = 'block';
                isValid = false;
            } else if (password.length < 6) {
                passwordError.textContent = '密码长度不能少于6位';
                passwordError.style.display = 'block';
                isValid = false;
            }
            
            if (isValid) {
                // 这里可以添加实际的登录逻辑，比如发送AJAX请求
                console.log('登录信息:', { username, password, rememberMe });
                
                // 模拟登录成功
                alert('登录成功！');
                // window.location.href = 'dashboard.html'; // 跳转到主页
            }
        });
    </script>
</body>
</html>`;

// 流程图SVG示例代码
export const svgExample = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600">
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="400" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#333">代码打包与部署流程图</text>
  
  <!-- 步骤1: 准备代码 -->
  <rect x="100" y="80" width="180" height="60" rx="10" ry="10" fill="#4285f4" stroke="#2b579a" stroke-width="2"/>
  <text x="190" y="115" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">1. 准备应用代码</text>
  
  <!-- 步骤2: 编写Dockerfile -->
  <rect x="100" y="180" width="180" height="60" rx="10" ry="10" fill="#4285f4" stroke="#2b579a" stroke-width="2"/>
  <text x="190" y="215" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">2. 编写Dockerfile</text>
  
  <!-- 步骤3: 构建镜像 -->
  <rect x="100" y="280" width="180" height="60" rx="10" ry="10" fill="#4285f4" stroke="#2b579a" stroke-width="2"/>
  <text x="190" y="315" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">3. 构建Docker镜像</text>
  
  <!-- 步骤4: 测试镜像 -->
  <rect x="100" y="380" width="180" height="60" rx="10" ry="10" fill="#4285f4" stroke="#2b579a" stroke-width="2"/>
  <text x="190" y="415" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">4. 本地测试镜像</text>
  
  <!-- 步骤5: 上传到仓库 -->
  <rect x="100" y="480" width="180" height="60" rx="10" ry="10" fill="#4285f4" stroke="#2b579a" stroke-width="2"/>
  <text x="190" y="515" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">5. 推送镜像到仓库</text>
  
  <!-- 步骤6: 连接服务器 -->
  <rect x="400" y="480" width="180" height="60" rx="10" ry="10" fill="#0f9d58" stroke="#0b8043" stroke-width="2"/>
  <text x="490" y="515" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">6. 连接到服务器</text>
  
  <!-- 步骤7: 拉取镜像 -->
  <rect x="400" y="380" width="180" height="60" rx="10" ry="10" fill="#0f9d58" stroke="#0b8043" stroke-width="2"/>
  <text x="490" y="415" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">7. 拉取Docker镜像</text>
  
  <!-- 步骤8: 配置环境 -->
  <rect x="400" y="280" width="180" height="60" rx="10" ry="10" fill="#0f9d58" stroke="#0b8043" stroke-width="2"/>
  <text x="490" y="315" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">8. 配置环境变量</text>
  
  <!-- 步骤9: 运行容器 -->
  <rect x="400" y="180" width="180" height="60" rx="10" ry="10" fill="#0f9d58" stroke="#0b8043" stroke-width="2"/>
  <text x="490" y="215" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">9. 运行Docker容器</text>
  
  <!-- 步骤10: 验证部署 -->
  <rect x="400" y="80" width="180" height="60" rx="10" ry="10" fill="#0f9d58" stroke="#0b8043" stroke-width="2"/>
  <text x="490" y="115" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">10. 验证部署</text>
  
  <!-- 箭头连接 -->
  <!-- 本地开发流程箭头 -->
  <path d="M190 140 L190 180" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M190 240 L190 280" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M190 340 L190 380" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M190 440 L190 480" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- 服务器部署流程箭头 -->
  <path d="M280 510 L400 510" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M490 480 L490 440" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M490 380 L490 340" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M490 280 L490 240" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path d="M490 180 L490 140" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
  
  <!-- 图例 -->
  <rect x="600" y="200" width="20" height="20" fill="#4285f4" stroke="#2b579a" stroke-width="1"/>
  <text x="630" y="215" font-family="Arial, sans-serif" font-size="14" fill="#333">本地开发环节</text>
  
  <rect x="600" y="240" width="20" height="20" fill="#0f9d58" stroke="#0b8043" stroke-width="1"/>
  <text x="630" y="255" font-family="Arial, sans-serif" font-size="14" fill="#333">服务器部署环节</text>
</svg>`; 