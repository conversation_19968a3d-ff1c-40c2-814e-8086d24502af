import React from 'react';
import CodeRunner from './CodeRunner';
import styles from './CodeRunnerPage.module.css';

const CodeRunnerPage: React.FC = () => {
  return (
    <div className={styles.pageContainer}>
      <div className={styles.header}>
        <h1 className={styles.title}>代码运行器</h1>
        <p className={styles.description}>
          输入前端代码并运行，立即查看运行效果。支持纯HTML页面和SVG图形。
        </p>
      </div>
      
      <div className={styles.runnerContainer}>
        <CodeRunner />
      </div>
      
      <div className={styles.tips}>
        <h3>使用提示：</h3>
        <ul>
          <li>支持两种模式：HTML模式和SVG模式</li>
          <li>HTML模式：可直接粘贴完整HTML代码，包括DOCTYPE、HTML标签等</li>
          <li>SVG模式：直接输入SVG标签代码，将自动嵌入HTML中显示</li>
        </ul>
      </div>
    </div>
  );
};

export default CodeRunnerPage; 