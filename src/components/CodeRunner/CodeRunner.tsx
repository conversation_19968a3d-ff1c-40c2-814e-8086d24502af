import React, { useEffect, useRef, useState } from 'react';
import styles from './CodeRunner.module.css';
import { loginHtmlExample, svgExample } from './CodeExamples';

interface CodeRunnerProps {
  code?: string;
  width?: string;
  height?: string;
}

const CodeRunner: React.FC<CodeRunnerProps> = ({ code: initialCode, width = '100%', height = '100%' }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [code, setCode] = useState(initialCode || '');
  const [isEditing, setIsEditing] = useState(!initialCode);
  const [codeType, setCodeType] = useState<'html' | 'svg'>('html');

  const runCode = () => {
    if (!iframeRef.current) return;
    setIsLoading(true);

    try {
      let htmlContent = '';

      if (codeType === 'svg') {
        // SVG代码模板，在HTML中显示SVG
        htmlContent = `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  min-height: 100vh;
                  background-color: #f8f9fa;
                  overflow-y: auto;
                }
                svg {
                  max-width: 100%;
                  height: auto;
                }
              </style>
            </head>
            <body>
              ${code}
            </body>
          </html>
        `;
      } else {
        // 直接使用用户输入的HTML，但添加视口设置和滚动样式
        if (code.includes('<head>')) {
          // 如果HTML包含head标签，在其中添加视口和滚动样式
          htmlContent = code.replace('<head>', `<head>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              html, body {
                overflow-y: auto;
                height: auto;
                min-height: 100%;
              }
            </style>`);
        } else {
          // 否则直接使用用户的HTML
          htmlContent = code;
        }
      }

      // 写入HTML内容到iframe
      const iframe = iframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (iframeDoc) {
        iframeDoc.open();
        iframeDoc.write(htmlContent);
        iframeDoc.close();
      }
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '运行代码时发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  // 当初始代码变更或组件加载时运行代码
  useEffect(() => {
    if (initialCode !== undefined && code !== initialCode) {
      setCode(initialCode);
    }
  }, [initialCode]);

  // 当代码变更且不处于编辑模式时自动运行
  useEffect(() => {
    if (!isEditing && code) {
      runCode();
    }
  }, [code, isEditing]);

  // 检测代码类型
  useEffect(() => {
    // 简单检测代码类型
    if (code.trim().toLowerCase().startsWith('<!doctype html>') || 
        code.trim().toLowerCase().startsWith('<html')) {
      setCodeType('html');
    } else if (code.trim().toLowerCase().startsWith('<svg')) {
      setCodeType('svg');
    }
  }, [code]);

  const loadExampleHTML = () => {
    setCode(loginHtmlExample);
    setCodeType('html');
  };

  const loadExampleSVG = () => {
    setCode(svgExample);
    setCodeType('svg');
  };

  return (
    <div className={styles.container} style={{ width, height, minHeight: '600px' }}>
      {isEditing ? (
        <div className={styles.editorSection}>
          <textarea
            value={code}
            onChange={(e) => setCode(e.target.value)}
            className={styles.codeEditor}
            placeholder="在这里输入HTML或SVG代码..."
            spellCheck="false"
            autoCorrect="off"
            autoCapitalize="off"
          />
          <div className={styles.buttonContainer}>
            <div className={styles.leftButtons}>
              <div className={styles.typeSelector}>
                <label>
                  <input
                    type="radio"
                    name="codeType"
                    checked={codeType === 'html'}
                    onChange={() => setCodeType('html')}
                  />
                  HTML
                </label>
                <label>
                  <input
                    type="radio"
                    name="codeType"
                    checked={codeType === 'svg'}
                    onChange={() => setCodeType('svg')}
                  />
                  SVG
                </label>
              </div>
              <div className={styles.exampleButtons}>
                <button 
                  className={styles.exampleButton}
                  onClick={loadExampleHTML}
                >
                  加载HTML登录页面
                </button>
                <button 
                  className={styles.exampleButton}
                  onClick={loadExampleSVG}
                >
                  加载SVG流程图
                </button>
              </div>
            </div>
            <button 
              className={styles.runButton}
              onClick={() => {
                runCode();
                setIsEditing(false);
              }}
            >
              运行代码
            </button>
          </div>
        </div>
      ) : (
        <div className={styles.resultSection}>
          {error && (
            <div className={styles.errorContainer}>
              {error}
            </div>
          )}
          <iframe
            ref={iframeRef}
            className={styles.iframe}
            style={{
              width: '100%',
              height: error ? 'calc(100% - 40px)' : '100%',
            }}
            sandbox="allow-scripts allow-forms allow-popups allow-same-origin"
            title="代码运行结果"
            onLoad={() => setIsLoading(false)}
            scrolling="yes"
          />
          {isLoading && (
            <div className={styles.loadingOverlay}>
              加载中...
            </div>
          )}
          <button 
            className={styles.editButton}
            onClick={() => setIsEditing(true)}
          >
            编辑代码
          </button>
        </div>
      )}
    </div>
  );
};

export default CodeRunner; 