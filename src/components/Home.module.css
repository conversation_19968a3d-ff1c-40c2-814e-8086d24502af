.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1f35, #2d3a55);
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  background-image: 
    radial-gradient(circle at center, rgba(56, 189, 248, 0.03) 0%, transparent 50%),
    linear-gradient(rgba(56, 189, 248, 0.01) 2px, transparent 2px),
    linear-gradient(90deg, rgba(56, 189, 248, 0.01) 2px, transparent 2px);
  background-size: 100% 100%, 40px 40px, 40px 40px;
  animation: rotate 60s linear infinite;
  z-index: 0;
}

.container::after {
  content: '';
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 30%, rgba(56, 189, 248, 0.07) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.techLines {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
  opacity: 0.3;
}

.techLines::before {
  content: '';
  position: absolute;
  inset: -100%;
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(56, 189, 248, 0.03) 10px,
    rgba(56, 189, 248, 0.03) 20px
  );
  animation: techMove 20s linear infinite;
}

.techLines::after {
  content: '';
  position: absolute;
  inset: -100%;
  background-image: repeating-linear-gradient(
    -45deg,
    transparent,
    transparent 10px,
    rgba(99, 102, 241, 0.03) 10px,
    rgba(99, 102, 241, 0.03) 20px
  );
  animation: techMove 15s linear infinite reverse;
}

@keyframes techMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(100px, 100px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg) scale(1.5);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1.5);
  }
}

.header {
  padding: 32px 24px;
  background: rgba(26, 31, 53, 0.8);
  border-bottom: 1px solid rgba(56, 189, 248, 0.1);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 10;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.titleSection {
  flex: 1;
}

.mainTitle {
  font-size: 2.5rem;
  font-weight: 600;
  background: linear-gradient(135deg, #fff, #38bdf8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 12px 0 0;
  line-height: 1.5;
}

.headerSettings {
  margin-left: 24px;
  background-color: rgba(26, 31, 53, 0.8);
}

.content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  width: 100%;
  overflow-y: auto;
  position: relative;
  z-index: 1;
  height: 100%;
}

.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.content::-webkit-scrollbar-thumb {
  background: rgba(56, 189, 248, 0.2);
  border-radius: 3px;
  transition: background 0.3s;
}

.content::-webkit-scrollbar-thumb:hover {
  background: rgba(56, 189, 248, 0.3);
}

.categorySection {
  margin-bottom: 48px;
  position: relative;
}

.categorySection::before {
  content: '';
  position: absolute;
  left: -24px;
  top: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(56, 189, 248, 0.2),
    transparent
  );
}

.categoryTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin: 0 0 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(56, 189, 248, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.categoryTitle::before {
  content: '';
  display: block;
  width: 8px;
  height: 8px;
  background: #38bdf8;
  transform: rotate(45deg);
}

.cardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.cardWrapper {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.cardWrapper:hover {
  transform: translateY(-4px);
}

.card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  height: 100%;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, 
    rgba(var(--card-rgb), 0.1), 
    rgba(var(--card-rgb), 0.05)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent, 
    var(--card-color), 
    transparent
  );
}

.card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-color: var(--card-color);
}

.card:hover::before {
  opacity: 1;
}

.cardIcon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.03);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.cardIcon::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--card-color), transparent);
  border-radius: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover .cardIcon {
  transform: scale(1.1);
  background: rgba(var(--card-rgb), 0.1);
}

.card:hover .cardIcon::before {
  opacity: 0.5;
}

.cardContent {
  flex: 1;
  position: relative;
}

.cardTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #fff;
  margin: 0 0 8px;
  line-height: 1.4;
}

.cardDescription {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.5;
}

.cardArrow {
  position: absolute;
  bottom: 24px;
  right: 24px;
  font-size: 1.25rem;
  color: var(--card-color);
  opacity: 0;
  transform: translateX(-8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover .cardArrow {
  opacity: 1;
  transform: translateX(0);
}

@media (max-width: 768px) {
  .header {
    padding: 20px;
  }

  .mainTitle {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .content {
    padding: 20px;
    height: calc(100vh - 85px);
  }

  .cardGrid {
    grid-template-columns: 1fr;
  }

  .card {
    padding: 20px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .cardGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}