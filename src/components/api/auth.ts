// src/api/auth.ts
import { setToken } from "../../utils/storage";
import { LOGIN_ENDPOINT } from "../../Constant/RouterConstant";

interface LoginResponse {
    access_token: string;
    username: string;
    token_type: string;
    role: string;
}

export const login = async (username: string, password: string): Promise<LoginResponse> => {
    const formData = new FormData();
    formData.append("username", username);
    formData.append("password", password);
    const response = await fetch(LOGIN_ENDPOINT, {
        method: "POST",
        body: formData, // 不要自己设置 Content-Type，会自动生成
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Login failed");
    }

    const data: LoginResponse = await response.json();
    setToken(data.access_token); // 保存 token 到 localStorage
    return data;
};
