// src/components/api/api.ts
import {MANAGER_API_BASE_URL} from '../../Constant/ServerConstant';
import { message } from 'antd';

const getToken = (): string | null => localStorage.getItem('access_token');

const handleResponse = async (response: Response) => {
  if (response.status === 401) {
    localStorage.removeItem('access_token');
    throw new Error('Unauthorized');
  }

  if (!response.ok) {
    const errorData = await response.json();
    const errorMessage = errorData.detail || `HTTP error! status: ${response.status}`;
    message.error(errorMessage);
    throw new Error(errorMessage);
  }

  return response.json();
};

export const fetchBulk = async (endpoint: string, params: Record<string, any> = {}) => {
  const token = getToken();
  try {
    const url = new URL(endpoint);
    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });
    const data = await handleResponse(response);
    return data;
  } catch (error: any) {
    message.error(error.message || '批量获取数据失败');
    throw error;
  }
};

export const fetchData = async (endpoint: string) => {
  const token = getToken();
  try {
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.status === 401) {
      localStorage.removeItem("access_token");
      throw new Error('Unauthorized');
    }

    if (response.status !== 200) {
      return null;
    }

    return await response.json();
  } catch (error: any) {
    message.error(error.message || '获取数据失败');
    throw error;
  }
};

export const deleteData = async (endpoint: string) => {
  const token = getToken();
  try {
    const response = await fetch(endpoint, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    const data = await handleResponse(response);
    return data;
  } catch (error: any) {
    message.error(error.message || '删除数据失败');
    throw error;
  }
};

export const deleteBulk = async (endpoint: string, kbData: any) => {
  const token = getToken();
  try {
    const response = await fetch(endpoint, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(kbData),
    });

    const data = await handleResponse(response);
    return data;
  } catch (error: any) {
    message.error(error.message || '批量删除数据失败');
    throw error;
  }
};

export const createData = async (endpoint: string, data: any) => {
  const token = getToken();
  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    const responseData = await handleResponse(response);
    return responseData;
  } catch (error: any) {
    message.error(error.message || '新增数据失败');
    throw error;
  }
};

export const textToJson = async (endpoint: string, data: any) => {
  const token = getToken();
  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    const responseData = await handleResponse(response);
    return responseData;
  } catch (error: any) {
    message.error(error.message || '文本转JSON失败');
    throw error;
  }
};

export const updateData = async (endpoint: string, kbData: any) => {
  const token = getToken();
  try {
    const response = await fetch(endpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(kbData),
    });

    const responseData = await handleResponse(response);
    return responseData;
  } catch (error: any) {
    message.error(error.message || '更新数据失败');
    throw error;
  }
};

export const uploadFile = async (file: File): Promise<any> => {
  const token = getToken();
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${MANAGER_API_BASE_URL}/api/v1/file-managers/upload_file_chat_v2/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    const data = await handleResponse(response);
    return data;
  } catch (error: any) {
    message.error(error.message || '文件上传失败');
    throw error;
  }
};

// 上传图片到服务器获取URL
export const uploadImageToServer = async (imageUrl: string, filename: string): Promise<string> => {
  try {
    // 如果已经是HTTP URL，直接返回
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // 获取认证 token
    const token = getToken();
    
    // 从Blob URL获取图片数据
    const response = await fetch(imageUrl);
    const blob = await response.blob();

    // 创建FormData
    const formData = new FormData();
    formData.append('file', blob, filename);

    // 调用上传接口
    const UPLOAD_API = `${MANAGER_API_BASE_URL}/api/v1/file-managers/only_upload_file/`;
    console.log('上传图片到:', UPLOAD_API);

    const uploadResponse = await fetch(UPLOAD_API, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData,
    });

    if (!uploadResponse.ok) {
      throw new Error(`上传失败: ${uploadResponse.status}`);
    }

    const result = await uploadResponse.json();

    if (result.code === 200 && result.url) {
      console.log('图片上传成功，URL:', result.url);
      return result.url;
    } else {
      throw new Error('上传接口返回格式不正确');
    }
  } catch (error) {
    console.error('图片上传错误:', error);
    message.error('图片上传失败，请重试');
    throw error;
  }
};

interface ExcelData {
  [key: string]: any;
}


interface ChatExcelInput {
  dify_inputs:{
    text: string;
    output_columns: string;
  }
  api_key:string;
}

export const mockApiCall = async (data: ExcelData, outputColumns: string, apiKey: string): Promise<any> => {
  const token = getToken();
  const requestData: ChatExcelInput = {
    dify_inputs:{
      text: data.title || '',
      output_columns: outputColumns,
    },
    api_key:apiKey
  };
  // console.log(requestData,'requestData');
  return fetch(`${MANAGER_API_BASE_URL}/api/v1/chat/test`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(requestData)
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('网络请求失败');
    }
    return response.json();
  })
  .catch(error => {
    message.error('API调用失败：' + error.message);
    throw error;
  });
};