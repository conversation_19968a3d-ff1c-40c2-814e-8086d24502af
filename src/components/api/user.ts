// src/api/user.ts
import { REGISTER_ENDPOINT,UPDATE_USER_ENDPOINT } from "../../Constant/RouterConstant";
// export const register = async (user: { username: string; password: string }) => {
//     const response = await fetch(REGISTER_ENDPOINT, {
//         method: "POST",
//         headers: {
//             "Content-Type": "application/json",
//         },
//         body: JSON.stringify(user),
//     });

//     if (!response.ok) {
//         const errorData = await response.json();
//         throw new Error(errorData.detail || "Registration failed");
//     }

//     return await response.json();
// };

// src/api/user.ts
export interface RegisterInput {
    username: string;
    email: string;   // 新增 email
    password: string;
}

export const register = async (user: RegisterInput) => {
    const response = await fetch(REGISTER_ENDPOINT, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(user),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Registration failed");
    }

    return await response.json();
};


export const resetPassword = async (
    userId: number,
    passwords: { old_password: string; new_password: string }
) => {
    const token = localStorage.getItem("access_token");

    if (!token) {
        throw new Error("No access token found");
    }

    const response = await fetch(`${UPDATE_USER_ENDPOINT}/${userId}/password`, {
        method: "PUT",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(passwords),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Password reset failed");
    }

    return await response.json();
};
