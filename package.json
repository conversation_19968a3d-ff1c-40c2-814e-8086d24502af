{"name": "llm-research", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "start": "vite"}, "dependencies": {"@ant-design/x": "^1.0.4", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@reduxjs/toolkit": "^2.5.0", "@types/dompurify": "^3.0.5", "@types/highlight.js": "^9.12.4", "@types/jszip": "^3.4.0", "@types/marked": "^5.0.2", "@xyflow/react": "^12.4.4", "antd": "^5.25.1", "antd-style": "^3.7.1", "classnames": "^2.5.1", "dompurify": "^3.2.4", "grapesjs": "^0.22.7", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jsonrepair": "^3.12.0", "jspdf": "^3.0.1", "jszip": "^3.10.1", "jwt-decode": "^3.1.2", "lucide-react": "^0.471.0", "markdown-it": "^14.1.0", "marked": "^15.0.7", "openai": "^4.76.3", "pdf-lib": "^1.17.1", "prismjs": "^1.30.0", "puppeteer": "^24.7.2", "puppeteer-core": "^24.7.2", "puppeteer-html-pdf": "^4.0.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.1", "reveal.js": "^5.2.1", "uuid": "^11.0.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/markdown-it": "^14.1.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}